﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员在上报配置页面点击路径配置按钮,总部平台上报路径配置,输入集团平台上报路径信息,E,上报路径信息,平台IP、端口、协议类型（HTTPS）、IPv4/IPv6标识、网关ID,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员在上报配置页面点击路径配置按钮,总部平台上报路径配置,校验IP地址格式及端口有效性,R,地址校验规则,IP格式规则、端口范围规则、协议版本要求,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员在上报配置页面点击路径配置按钮,总部平台上报路径配置,关联业务地址与网关配置,W,业务地址网关关联,业务地址ID、网关ID、协议参数,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员在上报配置页面点击路径配置按钮,总部平台上报路径配置,保存上报路径配置,W,上报路径信息,平台IP、端口、协议类型、IPv4/IPv6标识,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员在上报配置页面点击路径配置按钮,总部平台上报路径配置,返回配置结果,X,操作结果信息,操作状态、错误代码、提示信息,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员点击上报路径查询按钮,总部平台上报路径查看,读取已配置的上报路径信息,R,上报路径信息,平台IP、端口、协议类型、IPv4/IPv6标识、网关ID,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：管理员，接收者：系统管理-总部平台HTTPS对接模块,管理员点击上报路径查询按钮,总部平台上报路径查看,展示上报路径配置列表,X,上报路径信息,平台IP、端口、协议类型、IPv4/IPv6标识,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：系统，接收者：HTTPS通道管理模块,系统初始化时触发HTTPS通道建立,总部平台HTTPS通道对接,读取HTTPS通道配置参数,R,HTTPS配置信息,TLS版本、加密套件列表、证书路径,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：系统，接收者：HTTPS通道管理模块,系统初始化时触发HTTPS通道建立,总部平台HTTPS通道对接,验证HTTPS配置合规性,R,安全策略规则,TLS版本白名单、加密套件白名单,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：系统，接收者：HTTPS通道管理模块,系统初始化时触发HTTPS通道建立,总部平台HTTPS通道对接,建立HTTPS安全连接,X,连接参数,目标IP、端口、协议版本、加密套件,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：系统，接收者：HTTPS通道管理模块,系统初始化时触发HTTPS通道建立,总部平台HTTPS通道对接,返回通道状态,X,连接结果信息,连接状态、错误代码、证书验证结果,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击保存按钮,总部平台访问凭证配置,输入访问凭证信息,E,访问凭证信息,平台ID、访问密钥ID(AK)、秘密密钥(SK)、状态,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击保存按钮,总部平台访问凭证配置,验证凭证格式和有效性,R,凭证校验规则,AK格式规则、SK长度规则、状态枚举值,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击保存按钮,总部平台访问凭证配置,保存访问凭证到数据库,W,访问凭证信息,平台ID、访问密钥ID(AK)、秘密密钥(SK)、状态,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击保存按钮,总部平台访问凭证配置,返回配置结果,X,操作结果信息,操作状态、错误代码、提示信息,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击查询按钮,访问凭证查看,读取访问凭证信息,R,访问凭证信息,平台ID、访问密钥ID(AK)、状态,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：管理员，接收者：系统管理-总部平台对接模块,管理员在AKSK认证配置页面点击查询按钮,访问凭证查看,展示访问凭证信息,X,访问凭证信息,平台ID、访问密钥ID(AK)、状态,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,读取当前访问凭证,R,访问凭证信息,平台ID、访问密钥ID(AK)、秘密密钥(SK),1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,生成认证请求参数,E,认证请求信息,认证时间戳、随机字符串、签名算法版本,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,执行密码算法保护处理,E,加密参数,加密算法类型、密钥版本、加密数据,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,发送认证请求到总部平台,X,认证请求信息,认证时间戳、随机字符串、签名结果,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,接收总部平台认证响应,R,认证结果信息,认证状态、错误代码、会话令牌,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：系统，接收者：总部平台认证服务,系统定时触发认证流程或用户手动发起认证,AKSK认证,返回认证结果,X,认证结果信息,认证状态、错误代码、会话令牌,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册审核列表查询按钮,用户注册信息列表查询,输入查询条件（如审核状态）,E,查询条件信息,页码、单页数量、审核状态,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册审核列表查询按钮,用户注册信息列表查询,读取并展示用户注册信息列表,R,用户注册信息,用户ID、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册信息录入按钮,注册用户信息,输入用户注册信息,E,用户注册信息,账户名、姓名、用户类型、备注,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册信息录入按钮,注册用户信息,验证用户注册信息格式,R,系统配置规则,账户名格式规则、用户类型枚举值,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册信息录入按钮,注册用户信息,保存用户注册信息,W,用户注册信息,账户名、姓名、用户类型、备注,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户信息编辑按钮,编辑用户信息,选择待编辑的用户注册记录,E,用户注册信息,用户ID,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户信息编辑按钮,编辑用户信息,输入更新后的用户信息,E,用户注册信息,用户ID、姓名,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户信息编辑按钮,编辑用户信息,验证更新后的用户信息,R,系统配置规则,姓名格式规则,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户信息编辑按钮,编辑用户信息,更新用户注册信息,W,用户注册信息,用户ID、姓名,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击删除注册记录按钮,删除注册记录,选择待删除的用户注册记录,E,用户注册信息,用户ID,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击删除注册记录按钮,删除注册记录,删除用户注册记录,W,用户注册信息,用户ID,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册审核按钮,用户注册审核,选择审核操作类型（通过/拒绝）,E,审核信息,审核状态,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册审核按钮,用户注册审核,输入审核意见及所属角色,E,审核信息,审核意见、所属角色,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：管理员，接收者：用户认证系统,管理员点击用户注册审核按钮,用户注册审核,保存审核结果,W,审核信息,用户ID、审核状态、审核意见、所属角色,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击用户信息列表查询按钮,用户信息列表查询,输入分页和筛选条件,E,用户信息,页码、单页数量、用户ID、账号名、姓名、角色、所属租户、账号有效期、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击用户信息列表查询按钮,用户信息列表查询,读取用户信息列表数据,R,用户信息,用户ID、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击用户信息列表查询按钮,用户信息列表查询,返回分页后的用户信息列表,X,用户信息,页码、单页数量、用户ID、账号名、姓名、角色、所属租户、账号有效期、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击启用用户按钮,启用用户,选择待启用的用户,E,用户信息,用户ID、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击启用用户按钮,启用用户,更新用户状态为启用,W,用户信息,用户ID、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击禁用用户按钮,禁用用户,选择待禁用的用户,E,用户信息,用户ID、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击禁用用户按钮,禁用用户,更新用户状态为禁用,W,用户信息,用户ID、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击重置用户密码按钮,重置用户密码,选择待重置密码的用户,E,用户信息,用户ID,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击重置用户密码按钮,重置用户密码,更新用户密码为默认密码,W,用户信息,用户ID、密码,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击解锁用户按钮,解锁用户锁定,选择待解锁的用户,E,用户信息,用户ID、锁定状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击解锁用户按钮,解锁用户锁定,更新用户锁定状态为未锁定,W,用户信息,用户ID、锁定状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击设置口令有效期按钮,设置用户的口令有效期,选择待设置有效期的用户,E,用户信息,用户ID,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击设置口令有效期按钮,设置用户的口令有效期,输入新的口令有效期,E,用户信息,用户ID、密码有效期,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击设置口令有效期按钮,设置用户的口令有效期,更新用户口令有效期,W,用户信息,用户ID、密码有效期,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击删除用户按钮,删除用户,选择待删除的用户,E,用户信息,用户ID,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：管理员，接收者：用户管理系统,管理员点击删除用户按钮,删除用户,从系统中删除用户记录,W,用户信息,用户ID,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户在登录界面输入用户名和口令,口令登录,输入用户口令信息,E,用户口令信息,用户名、口令（明文）、验证码,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户在登录界面输入用户名和口令,口令登录,读取口令黑名单信息,R,口令黑名单信息,明文口令、密文口令、失效标志,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户在登录界面输入用户名和口令,口令登录,验证口令有效性并记录登录日志,W,用户登录记录,用户名、登录时间、登录状态、IP地址,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户插入Ukey并点击登录,Ukey登录,读取Ukey设备信息,R,Ukey信息,Ukey序列号、证书信息、绑定用户ID,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户插入Ukey并点击登录,Ukey登录,验证Ukey合法性,R,Ukey绑定信息,用户ID、Ukey序列号、绑定状态,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户插入Ukey并点击登录,Ukey登录,输入用户口令进行二次验证,E,用户口令信息,用户名、口令（明文）,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户插入Ukey并点击登录,Ukey登录,生成登录令牌并记录日志,W,用户登录记录,用户名、登录时间、登录方式、IP地址,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：认证系统,用户插入Ukey并点击登录,Ukey登录,返回登录结果,X,登录结果信息,登录状态、错误代码、提示信息,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击口令黑名单查询菜单,口令黑名单列表查询,输入查询条件,E,查询条件信息,页码、单页数量、口令状态,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击口令黑名单查询菜单,口令黑名单列表查询,读取口令黑名单数据,R,口令黑名单信息,明文口令、密文口令、失效标志、创建时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击口令黑名单查询菜单,口令黑名单列表查询,返回分页查询结果,X,口令黑名单信息,明文口令、密文口令、失效标志、创建时间、页码、总记录数,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击新增黑名单按钮,新建口令黑名单,输入新口令黑名单信息,E,口令黑名单信息,明文口令、密文口令、失效标志,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击新增黑名单按钮,新建口令黑名单,验证口令格式有效性,R,口令规则信息,最小长度、特殊字符要求、历史口令限制,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击新增黑名单按钮,新建口令黑名单,保存新口令黑名单,W,口令黑名单信息,明文口令、密文口令、失效标志、创建时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击编辑黑名单项,编辑口令黑名单,选择待编辑的黑名单项,E,口令黑名单信息,黑名单ID、明文口令,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击编辑黑名单项,编辑口令黑名单,读取原始黑名单信息,R,口令黑名单信息,明文口令、密文口令、失效标志,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击编辑黑名单项,编辑口令黑名单,更新口令黑名单信息,W,口令黑名单信息,明文口令、密文口令、失效标志、修改时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击删除黑名单项,删除口令黑名单,选择待删除的黑名单项,E,口令黑名单信息,黑名单ID、明文口令,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击删除黑名单项,删除口令黑名单,读取待删除黑名单信息,R,口令黑名单信息,明文口令、密文口令、失效标志,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：管理员，接收者：认证系统,管理员点击删除黑名单项,删除口令黑名单,删除口令黑名单记录,W,口令黑名单信息,黑名单ID、删除时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙列表菜单,智能密码钥匙列表,输入查询条件（分页信息）,E,查询条件信息,页码、单页数量、序列号、类型、所属账号名、所属用户名,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙列表菜单,智能密码钥匙列表,读取UKey列表数据,R,UKey信息,序列号、类型、所属账号名、所属用户名、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙列表菜单,智能密码钥匙列表,处理分页逻辑,R,分页配置信息,总记录数、当前页码、单页数量,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙列表菜单,智能密码钥匙列表,展示UKey列表（含分页信息）,X,UKey信息,序列号、类型、所属账号名、所属用户名、状态、页码、总页数,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,选择UKey类型,E,UKey类型信息,UKey类型ID、UKey类型名称,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,输入UKey口令,E,UKey口令信息,UKey口令、确认口令,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,选择绑定用户,E,用户信息,用户ID、用户名,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,验证UKey口令格式,R,配置规则信息,口令长度规则、特殊字符规则,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,生成并保存UKey凭证,W,UKey信息,序列号、类型、所属账号ID、所属用户ID、UKey口令（加密存储）,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙新增按钮,智能密码钥匙新增,返回新增结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙启用按钮,智能密码钥匙启用,选择目标UKey,E,UKey信息,序列号、所属用户ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙启用按钮,智能密码钥匙启用,读取UKey状态信息,R,UKey信息,序列号、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙启用按钮,智能密码钥匙启用,更新UKey状态为启用,W,UKey信息,序列号、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙启用按钮,智能密码钥匙启用,返回启用结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙禁用按钮,智能密码钥匙禁用,选择目标UKey,E,UKey信息,序列号、所属用户ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙禁用按钮,智能密码钥匙禁用,读取UKey状态信息,R,UKey信息,序列号、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙禁用按钮,智能密码钥匙禁用,更新UKey状态为禁用,W,UKey信息,序列号、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙禁用按钮,智能密码钥匙禁用,返回禁用结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙删除按钮,智能密码钥匙删除,选择目标UKey,E,UKey信息,序列号、所属用户ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙删除按钮,智能密码钥匙删除,读取UKey状态信息,R,UKey信息,序列号、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙删除按钮,智能密码钥匙删除,删除UKey凭证,W,UKey信息,序列号,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员点击智能密码钥匙删除按钮,智能密码钥匙删除,返回删除结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换口令登录开关,是否开启口令登录,输入登录配置参数,E,登录配置信息,是否开启口令登录,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换口令登录开关,是否开启口令登录,验证配置参数,R,配置规则信息,参数校验规则,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换口令登录开关,是否开启口令登录,保存登录配置,W,登录配置信息,是否开启口令登录,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换口令登录开关,是否开启口令登录,返回配置结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换UKey登录开关,是否开启UKey登录,输入登录配置参数,E,登录配置信息,是否开启UKey登录,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换UKey登录开关,是否开启UKey登录,验证配置参数,R,配置规则信息,参数校验规则,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换UKey登录开关,是否开启UKey登录,保存登录配置,W,登录配置信息,是否开启UKey登录,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：管理员，接收者：用户ukey策略管理模块,管理员在登录配置页面切换UKey登录开关,是否开启UKey登录,返回配置结果,X,操作结果信息,操作状态、提示信息,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面点击设置默认口令按钮,设置用户默认口令,输入默认口令配置信息,E,口令策略信息,默认口令值、加密方式、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面点击设置默认口令按钮,设置用户默认口令,验证口令规则并保存配置,W,口令策略信息,默认口令值、加密方式、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置历史口令限制,历史口令限制次数,输入历史口令限制次数,E,口令策略信息,历史限制次数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置历史口令限制,历史口令限制次数,验证限制规则并更新配置,W,口令策略信息,历史限制次数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面设置禁用天数,长时间未登录禁用账户天数,输入禁用天数配置,E,账户禁用策略,禁用天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面设置禁用天数,长时间未登录禁用账户天数,验证规则并保存禁用策略,W,账户禁用策略,禁用天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置口令有效期,口令有效期天数,输入口令有效期配置,E,口令有效期策略,有效期天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置口令有效期,口令有效期天数,验证规则并保存有效期配置,W,口令有效期策略,有效期天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置告警天数,口令有效期告警天数,输入告警天数配置,E,口令有效期策略,告警天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置告警天数,口令有效期告警天数,验证规则并保存告警配置,W,口令有效期策略,告警天数、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置登录失败次数限制,登录失败次数限制次数,输入登录失败次数限制,E,登录失败策略,失败次数上限、锁定时长,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置登录失败次数限制,登录失败次数限制次数,验证规则并保存失败策略,W,登录失败策略,失败次数上限、锁定时长,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置登录失败锁定时长,登录失败锁定时长(分钟),输入登录失败锁定时长,E,登录失败策略,锁定时长(分钟),1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置登录失败锁定时长,登录失败锁定时长(分钟),验证规则并保存锁定时长配置,W,登录失败策略,锁定时长(分钟),1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置强制修改默认口令,是否强制修改默认口令,输入强制修改配置,E,口令策略信息,强制修改标志、生效范围,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：用户口令策略管理模块,系统管理员在用户口令策略页面配置强制修改默认口令,是否强制修改默认口令,验证规则并保存强制修改策略,W,口令策略信息,强制修改标志、生效范围,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容列表菜单,上报内容列表,输入分页和筛选条件,E,查询条件信息,页码、单页数量、上报内容标识、启用状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容列表菜单,上报内容列表,读取上报内容配置信息,R,上报内容信息,上报内容标识、上报内容名称、上报周期、启用状态、最后上报时间,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容列表菜单,上报内容列表,处理分页逻辑,R,分页信息,总记录数、当前页码、总页数,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容列表菜单,上报内容列表,展示上报内容列表,X,上报内容信息,上报内容标识、上报内容名称、上报周期、启用状态、最后上报时间,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容配置按钮,上报内容配置,选择目标上报内容,E,上报内容信息,上报内容标识、启用状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容配置按钮,上报内容配置,读取当前配置状态,R,上报内容信息,上报内容标识、启用状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容配置按钮,上报内容配置,更新启用状态配置,W,上报内容信息,上报内容标识、启用状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报内容配置按钮,上报内容配置,返回配置结果,X,操作结果信息,操作状态、提示信息,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,选择目标上报内容,E,上报内容信息,上报内容标识,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,读取当前频率配置,R,上报频率信息,频率ID、频率名称、cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,读取可用频率字典,R,频率字典信息,频率ID、频率名称、cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,选择并验证新频率,E,上报频率信息,频率ID、cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,更新频率配置,W,上报频率信息,上报内容标识、频率ID,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,记录配置变更日志,W,操作日志信息,操作类型、操作人、操作时间、变更详情,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：管理员，接收者：系统管理模块,管理员点击上报频率配置按钮,上报频率配置,返回配置结果,X,操作结果信息,操作状态、提示信息,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面输入查询条件并点击查询按钮,查询登录日志,输入查询条件（时间范围/用户/操作结果）,E,查询条件信息,开始时间、结束时间、用户名、操作结果,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面输入查询条件并点击查询按钮,查询登录日志,验证查询条件有效性,R,系统配置信息,时间范围限制规则、用户权限规则,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面输入查询条件并点击查询按钮,查询登录日志,读取符合条件的登录日志,R,操作日志信息,日志ID、操作时间、用户名、操作结果、IP地址,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面输入查询条件并点击查询按钮,查询登录日志,展示分页查询结果,X,操作日志信息,日志ID、操作时间、用户名、操作结果、IP地址、页码、单页数量,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志列表中选中多条记录并点击批量审计按钮,批量审计,选择待审计的日志记录,E,操作日志信息,日志ID列表,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志列表中选中多条记录并点击批量审计按钮,批量审计,读取待审计日志的详细信息,R,操作日志信息,日志ID、操作时间、用户名、操作结果、IP地址,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志列表中选中多条记录并点击批量审计按钮,批量审计,执行批量审计操作,W,审核信息,审核状态、审核人、审核时间,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志列表中选中多条记录并点击批量审计按钮,批量审计,返回审计结果,X,操作结果信息,成功/失败状态、审计日志ID列表,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面点击导出按钮,日志导出,输入导出条件（时间范围/用户）,E,查询条件信息,开始时间、结束时间、用户名,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面点击导出按钮,日志导出,验证导出数量限制,R,系统配置信息,单次导出最大记录数（5000）,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面点击导出按钮,日志导出,读取符合导出条件的日志数据,R,操作日志信息,日志ID、操作时间、用户名、操作结果、IP地址,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：管理员，接收者：系统日志模块,管理员在登录日志页面点击导出按钮,日志导出,生成并导出日志文件,X,操作日志信息,日志ID、操作时间、用户名、操作结果、IP地址,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面输入查询条件并点击查询按钮,操作日志查询,输入查询条件（功能模块/操作人/时间范围等）,E,查询条件信息,功能模块、操作人、开始时间、结束时间、页码、单页数量,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面输入查询条件并点击查询按钮,操作日志查询,验证查询条件格式及权限,R,系统配置信息,时间格式规则、分页限制规则,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面输入查询条件并点击查询按钮,操作日志查询,读取符合条件的操作日志数据,R,操作日志信息,操作ID、租户ID、应用ID、操作人、操作时间、操作结果、HMAC检验状态,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面输入查询条件并点击查询按钮,操作日志查询,分页展示操作日志列表,X,操作日志信息,页码、单页数量、操作ID、租户ID、操作人、操作时间、操作结果,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志列表中选中多条记录并点击批量审批按钮,批量审批,选择待审批的操作日志,E,操作日志信息,操作ID列表,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志列表中选中多条记录并点击批量审批按钮,批量审批,输入审核信息（审核状态/审核意见）,E,审核信息,审核状态、审核意见,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志列表中选中多条记录并点击批量审批按钮,批量审批,更新操作日志的审核状态,W,操作日志信息,操作ID、审核状态、审核人、审核时间,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志列表中选中多条记录并点击批量审批按钮,批量审批,返回批量审批结果,X,操作结果信息,成功数量、失败数量、失败原因,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面点击导出按钮,日志导出,输入导出条件（时间范围/功能模块等）,E,查询条件信息,开始时间、结束时间、功能模块,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面点击导出按钮,日志导出,验证导出数量限制（最大5000条）,R,系统配置信息,导出数量限制规则,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面点击导出按钮,日志导出,生成操作日志导出文件,W,操作日志信息,操作ID、租户ID、操作人、操作时间、操作结果,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：管理员，接收者：系统管理模块,管理员在操作日志页面点击导出按钮,日志导出,返回导出文件下载链接,X,文件信息,文件URL、文件大小、生成时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用类型列表页面,密码应用类型分页列表查询,输入分页参数,E,密码应用类型信息,页码、单页数量,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用类型列表页面,密码应用类型分页列表查询,读取分页密码应用类型列表,R,密码应用类型信息,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用类型列表页面,密码应用类型分页列表查询,返回分页密码应用类型列表,X,密码应用类型信息,类型编码、类型名称、备注、创建时间、页码、总页数,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用类型列表输入过滤条件,密码应用类型过滤查询,输入过滤条件,E,密码应用类型信息,类型编码、类型名称,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用类型列表输入过滤条件,密码应用类型过滤查询,读取过滤后的密码应用类型列表,R,密码应用类型信息,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用类型列表输入过滤条件,密码应用类型过滤查询,返回过滤后的密码应用类型列表,X,密码应用类型信息,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用类型按钮,新增密码应用类型,输入新增密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用类型按钮,新增密码应用类型,验证类型编码唯一性,R,密码应用类型信息,类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用类型按钮,新增密码应用类型,保存新增密码应用类型,W,密码应用类型信息,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用类型按钮,新增密码应用类型,返回新增结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击编辑密码应用类型按钮,编辑密码应用类型,选择待编辑密码应用类型,E,密码应用类型信息,类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击编辑密码应用类型按钮,编辑密码应用类型,读取待编辑密码应用类型信息,R,密码应用类型信息,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击编辑密码应用类型按钮,编辑密码应用类型,输入更新后的密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击编辑密码应用类型按钮,编辑密码应用类型,保存更新后的密码应用类型,W,密码应用类型信息,类型编码、类型名称、备注、修改时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击删除密码应用类型按钮,删除密码应用类型,选择待删除密码应用类型,E,密码应用类型信息,类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击删除密码应用类型按钮,删除密码应用类型,验证关联应用是否存在,R,密码应用信息,关联应用ID,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击删除密码应用类型按钮,删除密码应用类型,删除密码应用类型,W,密码应用类型信息,类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：管理员，接收者：密码服务管理平台,管理员点击删除密码应用类型按钮,删除密码应用类型,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员在创建密码应用或过滤查询时选择应用类型,密码应用类型下拉选择,输入类型查询条件,E,类型查询条件,查询关键字、分页参数,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员在创建密码应用或过滤查询时选择应用类型,密码应用类型下拉选择,读取密码应用类型列表,R,应用类型信息,类型ID、类型名称、业务类型编码,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员在创建密码应用或过滤查询时选择应用类型,密码应用类型下拉选择,返回类型下拉选项数据,X,应用类型信息,类型ID、类型名称,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员点击应用类型统计菜单,密码应用类型应用数量分布,读取所有密码应用数据,R,密码应用信息,应用ID、应用类型ID,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员点击应用类型统计菜单,密码应用类型应用数量分布,读取密码应用类型定义,R,应用类型信息,类型ID、类型名称,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员点击应用类型统计菜单,密码应用类型应用数量分布,生成类型数量统计结果,W,应用数量分布信息,类型ID、类型名称、应用数量,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：管理员，接收者：密码应用管理系统,管理员点击应用类型统计菜单,密码应用类型应用数量分布,输出类型数量分布图表,X,应用数量分布信息,类型ID、类型名称、应用数量,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击分页查询按钮,密码应用分页列表查询,输入分页查询条件,E,密码应用信息,页码、单页数量、应用标识、应用名称、所属单位,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击分页查询按钮,密码应用分页列表查询,读取分页密码应用数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述、完整性校验状态,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击分页查询按钮,密码应用分页列表查询,返回分页密码应用列表,X,密码应用信息,页码、单页数量、应用标识、应用名称、所属单位、业务描述、完整性校验状态,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面输入过滤条件,密码应用过滤查询,输入过滤查询条件,E,密码应用信息,应用标识、应用名称、简称,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面输入过滤条件,密码应用过滤查询,读取过滤后的密码应用数据,R,密码应用信息,应用标识、应用名称、简称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面输入过滤条件,密码应用过滤查询,返回过滤后的密码应用列表,X,密码应用信息,应用标识、应用名称、简称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,输入新增密码应用信息,E,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,验证业务类型与密码服务集群对应关系,R,密码服务配置,业务类型、可用密码服务集群,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,验证认证方式有效性,R,认证方式配置,认证方式类型、配置状态,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,保存密码应用基础信息,W,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,建立密码服务调度关系,W,密码服务调度,应用标识、密码服务集群、调度规则,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用按钮并填写表单,新增密码应用,返回新增结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击编辑按钮并修改信息,编辑密码应用,输入密码应用编辑信息,E,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击编辑按钮并修改信息,编辑密码应用,更新密码应用信息,W,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击编辑按钮并修改信息,编辑密码应用,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击删除按钮,删除密码应用,输入密码应用删除请求,E,密码应用信息,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击删除按钮,删除密码应用,验证应用关联状态,R,密码应用关联信息,关联密钥数量、关联证书数量,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击删除按钮,删除密码应用,删除密码应用及关联数据,W,密码应用信息,应用标识、认证方式配置、密码服务调度关系,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击删除按钮,删除密码应用,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击详情按钮,密码应用详情,输入密码应用详情请求,E,密码应用信息,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击详情按钮,密码应用详情,读取密码应用基础信息,R,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击详情按钮,密码应用详情,读取密码服务调度关系,R,密码服务调度,密码服务集群、调度规则,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用管理页面点击详情按钮,密码应用详情,返回密码应用详情信息,X,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述、密码服务调度关系,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时检测密码应用数据完整性,密码应用信息完整性校验,读取密码应用原始数据,R,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时检测密码应用数据完整性,密码应用信息完整性校验,计算数据哈希值,R,数据校验信息,原始数据哈希值,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时检测密码应用数据完整性,密码应用信息完整性校验,比对数据哈希值,R,数据校验信息,存储哈希值,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时检测密码应用数据完整性,密码应用信息完整性校验,返回完整性校验结果,X,数据校验信息,校验状态、异常字段,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击认证凭证列表查询菜单,应用认证凭证列表查询,输入分页查询条件,E,分页查询条件,页码、单页数量,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击认证凭证列表查询菜单,应用认证凭证列表查询,读取认证凭证列表数据,R,认证凭证列表信息,认证凭证ID、认证方式、密钥ID、描述、状态、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击认证凭证列表查询菜单,应用认证凭证列表查询,展示认证凭证列表,X,认证凭证列表信息,认证凭证ID、认证方式、密钥ID、描述、状态、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面输入过滤条件,应用认证凭证过滤查询,输入过滤条件,E,过滤条件,密钥ID、描述,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面输入过滤条件,应用认证凭证过滤查询,读取匹配的认证凭证数据,R,认证凭证列表信息,认证凭证ID、认证方式、密钥ID、描述、状态、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面输入过滤条件,应用认证凭证过滤查询,展示过滤后的认证凭证列表,X,认证凭证列表信息,认证凭证ID、认证方式、密钥ID、描述、状态、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增认证凭证按钮,新增应用认证凭证,选择认证方式,E,认证方式信息,认证方式类型（AK/SK）,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增认证凭证按钮,新增应用认证凭证,生成SK文件并保存,W,认证凭证信息,认证凭证ID、认证方式、密钥ID、SK文件内容、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增认证凭证按钮,新增应用认证凭证,下载生成的SK文件,X,SK文件信息,文件内容、下载链接,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增认证凭证按钮,新增应用认证凭证,同步认证凭证到认证中心,W,认证中心同步信息,认证凭证ID、认证方式、密钥ID、SK文件哈希值,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增认证凭证按钮,新增应用认证凭证,返回新增结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击编辑按钮,编辑应用认证凭证,输入编辑的描述信息,E,认证凭证描述信息,认证凭证ID、新描述,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击编辑按钮,编辑应用认证凭证,读取原认证凭证数据,R,认证凭证信息,认证凭证ID、认证方式、密钥ID、描述、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击编辑按钮,编辑应用认证凭证,更新认证凭证描述,W,认证凭证信息,认证凭证ID、认证方式、密钥ID、新描述、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击编辑按钮,编辑应用认证凭证,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击启用按钮,启用应用认证凭证,选择启用的认证凭证,E,认证凭证选择信息,认证凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击启用按钮,启用应用认证凭证,读取认证凭证状态,R,认证凭证信息,认证凭证ID、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击启用按钮,启用应用认证凭证,更新认证凭证状态为启用,W,认证凭证信息,认证凭证ID、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击启用按钮,启用应用认证凭证,通知认证中心启用凭证,W,认证中心通知信息,认证凭证ID、操作类型（启用）,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击启用按钮,启用应用认证凭证,返回启用结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击停用按钮,停用应用认证凭证,选择停用的认证凭证,E,认证凭证选择信息,认证凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击停用按钮,停用应用认证凭证,读取认证凭证状态,R,认证凭证信息,认证凭证ID、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击停用按钮,停用应用认证凭证,更新认证凭证状态为停用,W,认证凭证信息,认证凭证ID、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击停用按钮,停用应用认证凭证,通知认证中心停用凭证,W,认证中心通知信息,认证凭证ID、操作类型（停用）,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击停用按钮,停用应用认证凭证,返回停用结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击删除按钮,删除应用认证凭证,选择删除的认证凭证,E,认证凭证选择信息,认证凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击删除按钮,删除应用认证凭证,读取认证凭证数据,R,认证凭证信息,认证凭证ID、认证方式、密钥ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击删除按钮,删除应用认证凭证,删除认证凭证数据,W,认证凭证信息,认证凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击删除按钮,删除应用认证凭证,同步删除认证中心凭证,W,认证中心同步信息,认证凭证ID、操作类型（删除）,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证列表页面点击删除按钮,删除应用认证凭证,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,系统定时执行数据完整性校验,应用认证凭证完整性校验,读取所有认证凭证数据,R,认证凭证列表信息,认证凭证ID、认证方式、密钥ID、SK文件哈希值,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,系统定时执行数据完整性校验,应用认证凭证完整性校验,校验数据完整性,R,完整性校验规则,校验算法、校验字段,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,系统定时执行数据完整性校验,应用认证凭证完整性校验,标记异常数据,W,认证凭证信息,认证凭证ID、异常状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,系统定时执行数据完整性校验,应用认证凭证完整性校验,输出校验结果,X,完整性校验结果,异常凭证列表、校验时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用业务功能列表菜单,密码应用业务功能列表,输入查询条件（分页信息）,E,密码应用业务信息,页码、单页数量、业务类型、服务集群名称,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用业务功能列表菜单,密码应用业务功能列表,读取密码应用业务绑定关系,R,密码应用业务信息,业务类型代码、服务集群ID、绑定状态,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击密码应用业务功能列表菜单,密码应用业务功能列表,展示密码应用业务列表,X,密码应用业务信息,业务类型名称、服务集群名称、绑定时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用业务功能按钮,新增密码应用业务功能,选择业务类型,E,密码应用业务信息,业务类型代码、业务类型名称,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用业务功能按钮,新增密码应用业务功能,选择密码服务集群,E,密码应用业务信息,服务集群ID、服务集群名称,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用业务功能按钮,新增密码应用业务功能,验证业务类型与服务集群关联性,R,密码服务集群信息,服务集群类型、支持的业务类型,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员点击新增密码应用业务功能按钮,新增密码应用业务功能,保存业务绑定关系,W,密码应用业务信息,业务类型代码、服务集群ID、绑定时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用业务列表点击删除按钮,删除密码应用业务功能,选择待删除的业务绑定记录,E,密码应用业务信息,业务ID、服务集群ID,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用业务列表点击删除按钮,删除密码应用业务功能,验证业务绑定关系有效性,R,密码应用业务信息,绑定状态、关联服务实例数,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：管理员，接收者：密码服务管理平台,管理员在密码应用业务列表点击删除按钮,删除密码应用业务功能,删除业务绑定关系,W,密码应用业务信息,业务ID、服务集群ID、删除时间,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击分页查询按钮,密码应用场景分页列表查询,输入分页查询条件,E,密码应用场景信息,页码、单页数量、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击分页查询按钮,密码应用场景分页列表查询,读取分页密码应用场景数据,R,密码应用场景信息,序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击分页查询按钮,密码应用场景分页列表查询,输出分页密码应用场景列表,X,密码应用场景信息,页码、单页数量、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击新建按钮,新建密码应用场景,输入新建密码应用场景信息,E,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击新建按钮,新建密码应用场景,验证业务系统名称唯一性,R,密码应用场景信息,业务系统名称,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击新建按钮,新建密码应用场景,保存新建密码应用场景,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击新建按钮,新建密码应用场景,返回新建操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击编辑按钮,编辑密码应用场景,选择待编辑密码应用场景,E,密码应用场景信息,业务系统名称,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击编辑按钮,编辑密码应用场景,更新密码应用场景信息,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击编辑按钮,编辑密码应用场景,返回编辑操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击删除按钮,删除密码应用场景,选择待删除密码应用场景,E,密码应用场景信息,业务系统名称,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击删除按钮,删除密码应用场景,删除密码应用场景,W,密码应用场景信息,业务系统名称,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用场景管理页面点击删除按钮,删除密码应用场景,返回删除操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用改造厂商列表菜单,密码应用改造厂商分页列表查询,输入分页查询条件,E,查询条件信息,页码、单页数量、厂商名称、产品名称,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用改造厂商列表菜单,密码应用改造厂商分页列表查询,读取密码应用改造厂商信息,R,密码应用改造厂商信息,厂商ID、厂商名称、产品名称、产品版本、服务类型、创建时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用改造厂商列表菜单,密码应用改造厂商分页列表查询,处理分页逻辑并生成结果集,R,分页处理信息,总记录数、当前页数据、分页状态,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用改造厂商列表菜单,密码应用改造厂商分页列表查询,返回分页厂商列表,X,密码应用改造厂商信息,厂商ID、厂商名称、产品名称、产品版本、服务类型、创建时间、分页信息,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新建密码厂商按钮,新增密码应用改造厂商,输入厂商基础信息,E,密码应用改造厂商信息,厂商名称、产品名称、产品版本、服务类型、联系人、联系方式,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新建密码厂商按钮,新增密码应用改造厂商,验证厂商信息格式,R,校验规则信息,名称长度规则、版本格式规则、服务类型枚举值,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新建密码厂商按钮,新增密码应用改造厂商,保存新增厂商记录,W,密码应用改造厂商信息,厂商ID、厂商名称、产品名称、产品版本、服务类型、创建时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表编辑按钮,编辑密码应用改造厂商,选择目标厂商记录,E,密码应用改造厂商信息,厂商ID、厂商名称,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表编辑按钮,编辑密码应用改造厂商,读取厂商详细信息,R,密码应用改造厂商信息,厂商ID、厂商名称、产品名称、产品版本、服务类型、创建时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表编辑按钮,编辑密码应用改造厂商,输入修改后的厂商信息,E,密码应用改造厂商信息,厂商名称、产品名称、产品版本、服务类型,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表编辑按钮,编辑密码应用改造厂商,更新厂商记录,W,密码应用改造厂商信息,厂商ID、厂商名称、产品名称、产品版本、服务类型、修改时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表删除按钮,删除密码应用改造厂商,选择待删除厂商,E,密码应用改造厂商信息,厂商ID、厂商名称,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表删除按钮,删除密码应用改造厂商,验证厂商关联状态,R,关联校验信息,是否存在关联密码应用、关联业务系统,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表删除按钮,删除密码应用改造厂商,执行厂商删除操作,W,密码应用改造厂商信息,厂商ID、删除状态、操作时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：管理员，接收者：密码应用数据管理系统,管理员点击厂商列表删除按钮,删除密码应用改造厂商,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员访问密码服务管理页面,密码服务列表,读取密码服务信息,R,密码服务信息,服务ID、服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员访问密码服务管理页面,密码服务列表,展示密码服务列表,X,密码服务信息,服务ID、服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务列表页面输入查询条件,密码服务查询,输入查询条件,E,查询条件信息,服务名称、服务类型、IP地址、端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务列表页面输入查询条件,密码服务查询,读取密码服务信息,R,密码服务信息,服务ID、服务名称、服务类型、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务列表页面输入查询条件,密码服务查询,展示查询结果,X,密码服务信息,服务ID、服务名称、服务类型、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统，接收者：密码资产管理系统,系统定时触发状态检测任务,密码服务状态检测,调用REST接口检测服务状态,E,服务状态检测请求,服务ID、检测URL,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统，接收者：密码资产管理系统,系统定时触发状态检测任务,密码服务状态检测,接收服务状态响应,X,服务状态检测结果,服务ID、运行状态、响应时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统，接收者：密码资产管理系统,系统定时触发状态检测任务,密码服务状态检测,更新服务运行状态,W,密码服务信息,服务ID、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击新建密码服务按钮,新建密码服务,输入服务基本信息,E,密码服务信息,服务名称、区域、服务类型、服务集群、设备集群,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击新建密码服务按钮,新建密码服务,输入服务规格或IP端口信息,E,密码服务信息,CPU核数、内存、管理IP、业务IP、管理端口、业务端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击新建密码服务按钮,新建密码服务,验证服务唯一性,R,密码服务信息,服务名称、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击新建密码服务按钮,新建密码服务,保存密码服务信息,W,密码服务信息,服务ID、服务名称、区域、服务类型、服务集群、设备集群、CPU核数、内存、管理IP、业务IP、管理端口、业务端口、备注,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击新建密码服务按钮,新建密码服务,返回创建结果,X,操作结果信息,操作状态、服务ID、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑密码服务按钮,编辑密码服务,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑密码服务按钮,编辑密码服务,输入修改信息,E,密码服务信息,服务名称、备注,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑密码服务按钮,编辑密码服务,更新密码服务信息,W,密码服务信息,服务ID、服务名称、备注,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑密码服务按钮,编辑密码服务,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击重启密码服务按钮,重启密码服务,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击重启密码服务按钮,重启密码服务,发送重启指令,E,服务操作指令,服务ID、操作类型,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击重启密码服务按钮,重启密码服务,接收重启结果,X,服务操作结果,服务ID、操作状态、响应时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击启动密码服务按钮,启动密码服务,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击启动密码服务按钮,启动密码服务,发送启动指令,E,服务操作指令,服务ID、操作类型,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击启动密码服务按钮,启动密码服务,接收启动结果,X,服务操作结果,服务ID、操作状态、响应时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击停止密码服务按钮,停止密码服务,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击停止密码服务按钮,停止密码服务,发送停止指令,E,服务操作指令,服务ID、操作类型,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击停止密码服务按钮,停止密码服务,接收停止结果,X,服务操作结果,服务ID、操作状态、响应时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击更新密码服务规格按钮,更新密码服务规格,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击更新密码服务规格按钮,更新密码服务规格,输入新规格参数,E,密码服务信息,CPU核数、内存,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击更新密码服务规格按钮,更新密码服务规格,验证规格有效性,R,规格限制信息,最小CPU核数、最大CPU核数、最小内存、最大内存,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击更新密码服务规格按钮,更新密码服务规格,更新服务规格,W,密码服务信息,服务ID、CPU核数、内存,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击更新密码服务规格按钮,更新密码服务规格,返回更新结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击删除密码服务按钮,删除密码服务,选择目标服务,E,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击删除密码服务按钮,删除密码服务,验证服务状态,R,密码服务信息,服务ID、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击删除密码服务按钮,删除密码服务,删除密码服务,W,密码服务信息,服务ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：管理员，接收者：密码资产管理系统,管理员点击删除密码服务按钮,删除密码服务,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务组管理页面点击新增服务组按钮,密码服务服务组新增,输入服务组基础信息,E,服务组信息,服务组ID、服务组名称、服务组代码、业务类型、备注,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务组管理页面点击新增服务组按钮,密码服务服务组新增,校验服务组唯一性,R,服务组信息,服务组代码、服务组名称,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务组管理页面点击新增服务组按钮,密码服务服务组新增,配置数据库连接参数,E,数据库配置信息,数据库类型、IP地址、端口、数据库名称、用户名、密码,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务组管理页面点击新增服务组按钮,密码服务服务组新增,保存服务组及数据库配置,W,服务组信息,服务组ID、服务组名称、服务组代码、业务类型、数据库配置信息,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组管理菜单,密码服务服务组列表,输入分页查询条件,E,查询条件信息,页码、单页数量、服务组名称、业务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组管理菜单,密码服务服务组列表,读取服务组基础信息,R,服务组信息,服务组ID、服务组名称、服务组代码、业务类型、创建时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组管理菜单,密码服务服务组列表,读取服务组关联服务数量,R,服务组信息,服务组ID、服务数量,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组管理菜单,密码服务服务组列表,返回服务组列表数据,X,服务组信息,服务组ID、服务组名称、服务组代码、业务类型、服务数量、创建时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组编辑按钮,密码服务服务组编辑,输入服务组修改信息,E,服务组信息,服务组ID、新服务组名称、新业务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组编辑按钮,密码服务服务组编辑,更新服务组基础信息,W,服务组信息,服务组ID、服务组名称、业务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组详情页的服务管理按钮,密码服务管理列表,输入服务组筛选条件,E,查询条件信息,服务组ID、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组详情页的服务管理按钮,密码服务管理列表,读取服务组关联服务信息,R,服务信息,服务ID、服务名称、服务类型、服务状态,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员点击服务组详情页的服务管理按钮,密码服务管理列表,返回服务列表数据,X,服务信息,服务ID、服务名称、服务类型、服务状态,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务管理页面点击释放服务按钮,密码服务释放,选择目标服务组和服务,E,服务组信息,服务组ID、服务ID,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务管理页面点击释放服务按钮,密码服务释放,验证服务释放条件,R,服务组信息,服务组ID、服务ID、服务状态,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：管理员，接收者：密码资产管理系统,管理员在服务管理页面点击释放服务按钮,密码服务释放,更新服务组关联关系,W,服务组信息,服务组ID、服务ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员进入密码服务镜像管理页面,密码服务镜像列表,读取分页和过滤条件,E,查询条件信息,页码、单页数量、镜像名称、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员进入密码服务镜像管理页面,密码服务镜像列表,查询镜像信息,R,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员进入密码服务镜像管理页面,密码服务镜像列表,组装分页镜像列表,R,分页信息,总记录数、总页数、当前页码,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员进入密码服务镜像管理页面,密码服务镜像列表,返回镜像列表和分页信息,X,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注、总记录数、总页数、当前页码,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像上传按钮,密码服务镜像上传,输入镜像文件和元数据,E,镜像信息,镜像名称、镜像版本、服务类型、文件大小、文件摘要,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像上传按钮,密码服务镜像上传,验证文件摘要和格式,R,校验规则信息,摘要算法类型、文件格式白名单,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像上传按钮,密码服务镜像上传,存储镜像文件和元数据,W,镜像信息,镜像名称、镜像版本、服务类型、文件大小、文件摘要、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像上传按钮,密码服务镜像上传,返回上传结果,X,操作结果信息,操作状态、提示信息、镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像上传按钮,密码服务镜像上传,更新镜像列表缓存,W,缓存信息,镜像ID、镜像名称、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像编辑按钮,密码服务镜像编辑,选择目标镜像,E,镜像信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像编辑按钮,密码服务镜像编辑,输入编辑后的备注信息,E,镜像信息,备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像编辑按钮,密码服务镜像编辑,更新镜像备注信息,W,镜像信息,镜像ID、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像编辑按钮,密码服务镜像编辑,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员在镜像列表输入查询条件,密码服务镜像查询,输入查询条件,E,查询条件信息,镜像名称、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员在镜像列表输入查询条件,密码服务镜像查询,查询匹配的镜像信息,R,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员在镜像列表输入查询条件,密码服务镜像查询,返回查询结果,X,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像启用按钮,密码服务镜像启用,选择目标镜像,E,镜像信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像启用按钮,密码服务镜像启用,验证镜像状态是否可启用,R,镜像信息,镜像ID、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像启用按钮,密码服务镜像启用,更新镜像状态为启用,W,镜像信息,镜像ID、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像启用按钮,密码服务镜像启用,返回启用结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像禁用按钮,密码服务镜像禁用,选择目标镜像,E,镜像信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像禁用按钮,密码服务镜像禁用,验证镜像状态是否可禁用,R,镜像信息,镜像ID、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像禁用按钮,密码服务镜像禁用,更新镜像状态为禁用,W,镜像信息,镜像ID、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像禁用按钮,密码服务镜像禁用,返回禁用结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像删除按钮,密码服务镜像删除,选择目标镜像,E,镜像信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像删除按钮,密码服务镜像删除,验证镜像状态是否为禁用,R,镜像信息,镜像ID、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像删除按钮,密码服务镜像删除,删除镜像文件和元数据,W,镜像信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像删除按钮,密码服务镜像删除,更新镜像列表缓存,W,缓存信息,镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：管理员，接收者：密码资产管理系统,管理员点击镜像删除按钮,密码服务镜像删除,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击新增数据库按钮,密码服务数据库新增,输入数据库基础信息,E,数据库信息,数据库名称、数据库类型、数据库IP、数据库端口,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击新增数据库按钮,密码服务数据库新增,输入管理员账号密码,E,数据库信息,管理员账号、管理员密码,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击新增数据库按钮,密码服务数据库新增,验证数据库连接有效性,R,数据库连接状态,连接状态、错误信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击新增数据库按钮,密码服务数据库新增,保存数据库配置信息,W,数据库信息,数据库名称、数据库类型、数据库IP、数据库端口、管理员账号、管理员密码,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击编辑按钮,密码服务数据库编辑,选择待编辑数据库,E,数据库信息,数据库ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击编辑按钮,密码服务数据库编辑,输入数据库名称和备注,E,数据库信息,数据库名称、数据库备注,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击编辑按钮,密码服务数据库编辑,更新数据库元数据,W,数据库信息,数据库ID、数据库名称、数据库备注,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击编辑按钮,密码服务数据库编辑,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击删除按钮,密码服务数据库删除,选择待删除数据库,E,数据库信息,数据库ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击删除按钮,密码服务数据库删除,验证数据库关联状态,R,数据库关联信息,关联服务数量、关联密钥数量,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击删除按钮,密码服务数据库删除,执行数据库删除操作,W,数据库信息,数据库ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员在数据库列表中点击删除按钮,密码服务数据库删除,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员访问数据库管理页面,密码服务数据库列表,输入查询条件,E,查询条件信息,页码、单页数量、数据库类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员访问数据库管理页面,密码服务数据库列表,读取数据库配置信息,R,数据库列表信息,数据库ID、数据库名称、数据库类型、实例库名称、数据库IP、数据库端口,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员访问数据库管理页面,密码服务数据库列表,校验数据库完整性,R,数据库完整性信息,完整性状态、校验时间,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：管理员，接收者：密码资产管理系统,管理员访问数据库管理页面,密码服务数据库列表,展示数据库列表,X,数据库列表信息,数据库ID、数据库名称、数据库类型、实例库名称、数据库IP、数据库端口、完整性状态,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击数据库模式列表菜单,密码服务数据库模式列表,读取数据库模式信息,R,数据库模式信息,模式ID、模式名称、模式类型、创建时间、更新时间、关联服务ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击数据库模式列表菜单,密码服务数据库模式列表,展示数据库模式列表,X,数据库模式信息,模式ID、模式名称、模式类型、创建时间、更新时间、关联服务ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表点击删除按钮,密码服务数据库模式删除,输入删除请求,E,数据库模式信息,模式ID、模式名称,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表点击删除按钮,密码服务数据库模式删除,读取待删除模式信息,R,数据库模式信息,模式ID、模式名称、关联服务ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表点击删除按钮,密码服务数据库模式删除,执行模式删除操作,W,数据库模式信息,模式ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表点击删除按钮,密码服务数据库模式删除,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表输入查询条件,密码服务数据库模式查询,输入查询条件,E,查询条件信息,页码、单页数量、模式名称、模式类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表输入查询条件,密码服务数据库模式查询,读取符合查询条件的数据库模式,R,数据库模式信息,模式ID、模式名称、模式类型、创建时间、更新时间、关联服务ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表输入查询条件,密码服务数据库模式查询,返回查询结果,X,数据库模式信息,模式ID、模式名称、模式类型、创建时间、更新时间、关联服务ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增数据库模式按钮,密码服务数据库模式新增,输入新增模式信息,E,数据库模式信息,模式名称、模式类型、关联服务ID、描述,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增数据库模式按钮,密码服务数据库模式新增,验证模式信息格式,R,校验规则信息,名称长度限制、类型枚举值,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增数据库模式按钮,密码服务数据库模式新增,保存新增模式,W,数据库模式信息,模式名称、模式类型、关联服务ID、描述,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增数据库模式按钮,密码服务数据库模式新增,返回新增结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关列表菜单,API网关列表,输入查询条件（分页信息）,E,查询条件信息,页码、单页数量、所属区域、网关类型,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关列表菜单,API网关列表,读取网关信息列表,R,网关信息,网关ID、网关名称、所属区域、网关标识、网关类型、IP地址、业务端口、管理端口、区域内IP、反向代理地址端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关列表菜单,API网关列表,处理分页逻辑,R,分页信息,总记录数、总页数、当前页码,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关列表菜单,API网关列表,展示网关列表,X,网关信息,网关名称、所属区域、网关标识、网关类型、IP地址、业务端口、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统，接收者：密码服务平台-API网关模块,平台部署完成且选择部署API网关,API网关初始化,读取平台部署信息,R,部署配置信息,网关IP、网关端口、网关类型、所属区域,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统，接收者：密码服务平台-API网关模块,平台部署完成且选择部署API网关,API网关初始化,生成网关基础信息,W,网关信息,网关ID、网关名称、网关标识、网关类型、IP地址、业务端口、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统，接收者：密码服务平台-API网关模块,平台部署完成且选择部署API网关,API网关初始化,返回初始化结果,X,操作结果信息,操作状态、网关ID、错误信息,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击新建网关按钮,API网关新增,输入网关基础信息,E,网关信息,网关名称、所属区域、网关标识、网关类型、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击新建网关按钮,API网关新增,验证网关标识唯一性,R,网关信息,网关标识,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击新建网关按钮,API网关新增,保存网关配置,W,网关信息,网关ID、网关名称、所属区域、网关标识、网关类型、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关编辑按钮,API网关编辑,选择待编辑网关,E,网关信息,网关ID,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关编辑按钮,API网关编辑,输入修改后的网关信息,E,网关信息,网关名称、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关编辑按钮,API网关编辑,更新网关配置,W,网关信息,网关ID、网关名称、管理端口,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关删除按钮,API网关删除,选择待删除网关,E,网关信息,网关ID,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关删除按钮,API网关删除,验证网关关联状态,R,关联关系信息,路由配置数量、服务绑定状态,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：管理员，接收者：密码服务平台-API网关模块,管理员点击网关删除按钮,API网关删除,执行网关删除,W,网关信息,网关ID,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员点击路由管理菜单进入路由列表页面,路由管理列表,输入路由查询条件（含分页信息）,E,查询条件信息,页码、单页数量、路由名称、服务类型、所属应用、URL路径,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员点击路由管理菜单进入路由列表页面,路由管理列表,读取路由基础信息,R,路由信息,路由ID、路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、超时时间,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员点击路由管理菜单进入路由列表页面,路由管理列表,读取路由关联的上游配置信息,R,上游配置信息,上游服务器地址、负载均衡策略、健康检查配置,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员点击路由管理菜单进入路由列表页面,路由管理列表,返回分页后的路由列表数据,X,路由信息,路由ID、路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、超时时间、分页信息,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员在路由列表点击详情按钮,路由管理详情,输入路由详情查询请求,E,路由标识信息,路由ID、路由组件标识,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员在路由列表点击详情按钮,路由管理详情,读取路由基础详情信息,R,路由详情信息,路由ID、路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、匹配条件、超时时间,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员在路由列表点击详情按钮,路由管理详情,读取关联服务列表信息,R,服务列表信息,服务ID、服务名称、服务类型、服务地址、服务状态,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：管理员，接收者：网关管理系统,管理员在路由列表点击详情按钮,路由管理详情,读取关联应用信息,R,应用信息,应用ID、应用名称、应用描述、所属部门、负责人,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击设备类型管理页面的展示按钮,设备类型展示,输入设备类型查询条件,E,查询条件信息,页码、单页数量、设备类型名称、所属厂商,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击设备类型管理页面的展示按钮,设备类型展示,读取设备类型配置信息,R,设备类型信息,设备类型ID、设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击设备类型管理页面的展示按钮,设备类型展示,读取监控配置关联信息,R,监控配置信息,监控类型、URL、端口、协议类型,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击设备类型管理页面的展示按钮,设备类型展示,展示设备类型列表及监控配置,X,设备类型信息,设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口、监控配置详情,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统管理员，接收者：密码资产管理系统,平台部署时触发初始化流程,设备类型初始化,读取平台默认设备类型配置,R,初始化配置信息,预设设备类型列表、厂商信息、接口协议,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统管理员，接收者：密码资产管理系统,平台部署时触发初始化流程,设备类型初始化,校验初始化配置格式,R,校验规则信息,设备类型字段校验规则,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统管理员，接收者：密码资产管理系统,平台部署时触发初始化流程,设备类型初始化,写入默认设备类型数据,W,设备类型信息,设备类型ID、设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统管理员，接收者：密码资产管理系统,平台部署时触发初始化流程,设备类型初始化,返回初始化结果,X,操作结果信息,操作状态、失败设备类型列表,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击新增设备类型按钮,设备类型新增,输入设备类型基础信息,E,设备类型信息,设备类型名称、所属厂商、设备类型分类,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击新增设备类型按钮,设备类型新增,输入管理接口配置信息,E,设备类型信息,管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击新增设备类型按钮,设备类型新增,校验设备类型唯一性,R,设备类型信息,设备类型名称、所属厂商,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击新增设备类型按钮,设备类型新增,保存新设备类型数据,W,设备类型信息,设备类型ID、设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑设备类型按钮,设备类型编辑,选择待编辑设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑设备类型按钮,设备类型编辑,读取当前设备类型配置,R,设备类型信息,设备类型ID、设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑设备类型按钮,设备类型编辑,输入更新后的设备类型信息,E,设备类型信息,设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑设备类型按钮,设备类型编辑,更新设备类型数据,W,设备类型信息,设备类型ID、设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击停用设备类型按钮,设备类型停用,选择待停用设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击停用设备类型按钮,设备类型停用,校验设备类型是否可停用,R,设备信息,关联设备数量,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击停用设备类型按钮,设备类型停用,更新设备类型状态为停用,W,设备类型信息,设备类型ID、启用状态,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击启用设备类型按钮,设备类型启用,选择待启用设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击启用设备类型按钮,设备类型启用,校验设备类型是否可启用,R,设备类型信息,停用原因、停用时间,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击启用设备类型按钮,设备类型启用,更新设备类型状态为启用,W,设备类型信息,设备类型ID、启用状态,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击删除设备类型按钮,设备类型删除,选择待删除设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击删除设备类型按钮,设备类型删除,校验设备类型是否可删除,R,设备信息,关联设备数量,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击删除设备类型按钮,设备类型删除,删除设备类型数据,W,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置查看按钮,监控信息配置查看,选择设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置查看按钮,监控信息配置查看,读取监控配置信息,R,监控配置信息,监控类型、URL、端口、协议类型、认证信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置查看按钮,监控信息配置查看,展示监控配置详情,X,监控配置信息,监控类型、URL、端口、协议类型、认证信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置保存按钮,监控信息配置,选择设备类型,E,设备类型信息,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置保存按钮,监控信息配置,输入监控配置信息,E,监控配置信息,监控类型、URL、端口、协议类型、认证信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置保存按钮,监控信息配置,校验监控配置格式,R,校验规则信息,URL格式规则、端口范围规则,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：管理员，接收者：密码资产管理系统,管理员点击监控配置保存按钮,监控信息配置,保存监控配置数据,W,监控配置信息,设备类型ID、监控类型、URL、端口、协议类型、认证信息,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群列表菜单,密码设备集群列表,输入分页和筛选条件,E,查询条件信息,页码、单页数量、设备类型、所属区域,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群列表菜单,密码设备集群列表,读取密码设备集群信息,R,密码设备集群信息,集群ID、名称、设备类型、所属区域、设备数量、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群列表菜单,密码设备集群列表,处理分页逻辑,R,分页信息,总记录数、总页数、当前页码,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群列表菜单,密码设备集群列表,展示密码设备集群列表,X,密码设备集群信息,集群ID、名称、设备类型、所属区域、设备数量、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群新增按钮,密码设备集群新增,输入密码设备集群信息,E,密码设备集群信息,名称、设备类型、所属区域、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群新增按钮,密码设备集群新增,验证设备类型有效性,R,密码机服务类型字典,设备类型代码、设备类型名称,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群新增按钮,密码设备集群新增,保存密码设备集群,W,密码设备集群信息,集群ID、名称、设备类型、所属区域、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群编辑按钮,密码设备集群编辑,选择目标密码设备集群,E,密码设备集群信息,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群编辑按钮,密码设备集群编辑,输入编辑后的集群信息,E,密码设备集群信息,名称、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群编辑按钮,密码设备集群编辑,更新密码设备集群,W,密码设备集群信息,集群ID、名称、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群删除按钮,密码设备集群删除,选择待删除的密码设备集群,E,密码设备集群信息,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群删除按钮,密码设备集群删除,检查集群调用状态,R,密码服务调用信息,服务ID、集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密码设备集群删除按钮,密码设备集群删除,删除密码设备集群,W,密码设备集群信息,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击绑定密码设备按钮,绑定密码设备,选择目标密码设备集群,E,密码设备集群信息,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击绑定密码设备按钮,绑定密码设备,选择待绑定的密码设备,E,密码设备信息,设备ID、设备类型,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击绑定密码设备按钮,绑定密码设备,验证设备类型匹配性,R,密码设备集群信息,设备类型,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击绑定密码设备按钮,绑定密码设备,执行设备绑定及密钥配置,W,设备绑定关系,集群ID、设备ID、密钥状态,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击释放密码设备按钮,释放密码设备,选择待释放的密码设备,E,密码设备信息,设备ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击释放密码设备按钮,释放密码设备,检查集群调用状态,R,密码服务调用信息,服务ID、集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击释放密码设备按钮,释放密码设备,解除设备绑定关系,W,设备绑定关系,集群ID、设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面输入名称或管理IP进行模糊查询,云密码机列表,输入查询条件（名称、管理IP）,E,云密码机查询条件,名称、管理IP,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面输入名称或管理IP进行模糊查询,云密码机列表,读取云密码机基础信息,R,云密码机信息,设备名称、管理IP、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面输入名称或管理IP进行模糊查询,云密码机列表,执行模糊匹配查询,R,云密码机信息,设备名称、管理IP、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面输入名称或管理IP进行模糊查询,云密码机列表,返回分页查询结果,X,云密码机信息,设备名称、管理IP、服务类型、使用状态、备注、页码、单页数量,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机管理页面的“新建”按钮,云密码机新建,输入云密码机基础信息,E,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机管理页面的“新建”按钮,云密码机新建,验证管理IP格式和端口范围,R,配置规则信息,IP格式规则、端口范围规则,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机管理页面的“新建”按钮,云密码机新建,校验设备唯一性（名称/IP）,R,云密码机信息,设备名称、管理IP,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机管理页面的“新建”按钮,云密码机新建,保存云密码机配置,W,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机管理页面的“新建”按钮,云密码机新建,返回操作结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“编辑”按钮,云密码机编辑,选择待编辑的云密码机,E,云密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“编辑”按钮,云密码机编辑,读取当前云密码机配置,R,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“编辑”按钮,云密码机编辑,输入修改后的配置信息,E,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“编辑”按钮,云密码机编辑,验证修改后的配置有效性,R,配置规则信息,IP格式规则、端口范围规则,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“编辑”按钮,云密码机编辑,更新云密码机配置,W,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“删除”按钮,云密码机删除,选择待删除的云密码机,E,云密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“删除”按钮,云密码机删除,检查是否存在关联虚拟机,R,虚拟机关联信息,关联虚拟机ID列表,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“删除”按钮,云密码机删除,执行删除操作,W,云密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“删除”按钮,云密码机删除,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“详情”按钮,云密码机详情,选择待查看的云密码机,E,云密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“详情”按钮,云密码机详情,读取完整配置信息,R,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注、创建时间、更新时间,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“详情”按钮,云密码机详情,读取关联状态信息,R,云密码机状态,在线状态、负载率、CPU使用率、内存使用率,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“详情”按钮,云密码机详情,读取关联虚拟机信息,R,虚拟机关联信息,关联虚拟机ID列表、虚拟机名称列表,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机列表的“详情”按钮,云密码机详情,展示云密码机详情,X,云密码机信息,设备名称、管理IP、管理端口、服务类型、使用状态、备注、创建时间、更新时间、在线状态、负载率、CPU使用率、内存使用率、关联虚拟机ID列表、虚拟机名称列表,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机网络配置菜单,网络配置列表,读取虚拟机网络配置信息,R,虚拟机网络配置,网络配置ID、网络名称、网卡名称、IP范围、端口范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机网络配置菜单,网络配置列表,展示虚拟机网络配置列表,X,虚拟机网络配置,网络配置ID、网络名称、网卡名称、IP范围、端口范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,输入虚拟机网络配置信息,E,虚拟机网络配置,网络名称、网卡名称、IP范围、端口范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,验证IP地址格式有效性,R,IP地址规则,IP地址格式规则、端口范围规则,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,验证IP地址范围唯一性,R,虚拟机网络配置,网络名称、IP范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,保存虚拟机网络配置,W,虚拟机网络配置,网络配置ID、网络名称、网卡名称、IP范围、端口范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,生成网络配置ID,W,虚拟机网络配置,网络配置ID,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,返回配置结果,X,操作结果信息,操作状态、提示信息、网络配置ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,输入虚拟机创建配置信息,E,虚拟密码机配置信息,设备类型、虚拟机网络、资源配置、创建数量、管理端口、服务端口、连接密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,验证虚拟机网络配置有效性,R,虚拟机网络配置,网络名称、网卡名称、主机序列号,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,校验资源配置参数范围,R,资源配置规则,CPU限制、内存限制、存储限制,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,生成虚拟机唯一标识,W,虚拟密码机信息,虚拟机ID、创建时间、创建人,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,调用云密码机0088标准创建接口,X,虚拟机创建请求,虚拟机ID、配置参数、网络信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,接收云密码机创建响应,R,虚拟机创建结果,创建状态、错误代码、实例ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,更新虚拟机状态为创建中,W,虚拟密码机信息,虚拟机ID、状态、创建进度,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,异步轮询创建完成状态,R,虚拟机状态信息,虚拟机ID、运行状态、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,更新虚拟机最终状态,W,虚拟密码机信息,虚拟机ID、状态、完成时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟密码机管理界面点击批量创建虚拟机按钮,批量创建虚拟机,返回批量创建结果,X,操作结果信息,成功数量、失败数量、失败明细,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户访问虚拟密码机管理页面,虚拟密码机列表,读取虚拟密码机基础信息,R,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户访问虚拟密码机管理页面,虚拟密码机列表,关联云密码机平台信息,R,云密码机信息,云机ID、集群版本、所属平台,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户访问虚拟密码机管理页面,虚拟密码机列表,整合虚拟机列表数据,R,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户访问虚拟密码机管理页面,虚拟密码机列表,展示虚拟机列表,X,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态、操作按钮,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户在虚拟机列表输入查询条件,虚拟密码机列表查询,输入查询条件,E,查询条件信息,页码、单页数量、名称、主机、管理IP、服务IP、设备类型,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户在虚拟机列表输入查询条件,虚拟密码机列表查询,构建查询参数,E,查询参数,名称模糊匹配、IP范围过滤、设备类型筛选,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户在虚拟机列表输入查询条件,虚拟密码机列表查询,执行分页查询,R,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户在虚拟机列表输入查询条件,虚拟密码机列表查询,返回查询结果,X,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态、分页信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在云密码机管理界面点击创建虚拟机按钮,创建虚拟密码机,选择云密码机实例,E,云密码机信息,云机ID、集群版本、可用资源,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在云密码机管理界面点击创建虚拟机按钮,创建虚拟密码机,输入虚拟机配置参数,E,虚拟密码机配置信息,设备类型、网络配置、资源配置、管理端口、服务端口,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在云密码机管理界面点击创建虚拟机按钮,创建虚拟密码机,生成虚拟机唯一标识,W,虚拟密码机信息,虚拟机ID、创建时间、创建人,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在云密码机管理界面点击创建虚拟机按钮,创建虚拟密码机,调用云密码机创建接口,X,虚拟机创建请求,虚拟机ID、配置参数、网络信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户点击虚拟机详情按钮,虚拟密码机详情,读取虚拟机基础信息,R,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户点击虚拟机详情按钮,虚拟密码机详情,获取云密码机关联信息,R,云密码机信息,云机ID、集群版本、所属平台,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户点击虚拟机详情按钮,虚拟密码机详情,查询网络配置详情,R,虚拟机网络配置,网络名称、网卡名称、主机序列号,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户点击虚拟机详情按钮,虚拟密码机详情,整合虚拟机详细信息,R,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态、创建时间、网络配置,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：虚拟密码机管理系统,用户点击虚拟机详情按钮,虚拟密码机详情,展示虚拟机详情,X,虚拟密码机信息,虚拟机ID、名称、设备类型、管理IP、服务IP、状态、网络配置、操作记录,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机详情页面点击编辑按钮,编辑虚拟密码机,读取当前虚拟机配置,R,虚拟密码机信息,虚拟机ID、名称、连接密码、网络配置,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机详情页面点击编辑按钮,编辑虚拟密码机,输入编辑内容,E,虚拟密码机配置信息,新名称、新连接密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机详情页面点击编辑按钮,编辑虚拟密码机,验证连接密码格式,R,密码规则信息,密码强度规则、特殊字符要求,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机详情页面点击编辑按钮,编辑虚拟密码机,更新虚拟机配置,W,虚拟密码机信息,虚拟机ID、名称、连接密码、修改时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机详情页面点击编辑按钮,编辑虚拟密码机,动态下发配置到密码服务,X,配置下发请求,虚拟机ID、新配置参数,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击删除按钮,删除虚拟密码机,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击删除按钮,删除虚拟密码机,验证删除条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击删除按钮,删除虚拟密码机,调用删除接口,X,删除请求,虚拟机ID、删除原因,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击删除按钮,删除虚拟密码机,更新虚拟机状态,W,虚拟密码机信息,虚拟机ID、状态、删除时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击启动按钮,启动虚拟密码机,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击启动按钮,启动虚拟密码机,验证启动条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击启动按钮,启动虚拟密码机,调用启动接口,X,启动请求,虚拟机ID、启动参数,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击启动按钮,启动虚拟密码机,更新虚拟机状态,W,虚拟密码机信息,虚拟机ID、状态、启动时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击停止按钮,停止虚拟密码机,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击停止按钮,停止虚拟密码机,验证停止条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击停止按钮,停止虚拟密码机,调用停止接口,X,停止请求,虚拟机ID、停止参数,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击停止按钮,停止虚拟密码机,更新虚拟机状态,W,虚拟密码机信息,虚拟机ID、状态、停止时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击重启按钮,重启虚拟密码机,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击重启按钮,重启虚拟密码机,验证重启条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击重启按钮,重启虚拟密码机,调用重启接口,X,重启请求,虚拟机ID、重启参数,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击重启按钮,重启虚拟密码机,更新虚拟机状态,W,虚拟密码机信息,虚拟机ID、状态、重启时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击强制删除按钮,强制删除虚拟密码机,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击强制删除按钮,强制删除虚拟密码机,验证强制删除条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击强制删除按钮,强制删除虚拟密码机,调用强制删除接口,X,强制删除请求,虚拟机ID、删除原因,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机列表点击强制删除按钮,强制删除虚拟密码机,清除残留数据记录,W,虚拟密码机信息,虚拟机ID、删除时间、操作人,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击生成影像按钮,生成虚机影像,选择目标虚拟机,E,虚拟密码机信息,虚拟机ID、名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击生成影像按钮,生成虚机影像,验证影像生成条件,R,虚拟机状态信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击生成影像按钮,生成虚机影像,调用影像生成接口,X,影像生成请求,虚拟机ID、生成参数,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击生成影像按钮,生成虚机影像,保存影像元数据,W,虚拟机影像信息,影像ID、虚拟机ID、生成时间、存储路径,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击下载影像按钮,下载虚机影像,选择目标影像,E,虚拟机影像信息,影像ID、虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击下载影像按钮,下载虚机影像,验证影像下载权限,R,用户权限信息,用户ID、操作权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击下载影像按钮,下载虚机影像,生成临时下载链接,X,下载链接信息,下载URL、有效期、文件大小,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击导入影像按钮,导入虚机影像,上传影像文件,E,虚拟机影像文件,文件名、文件大小、校验码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击导入影像按钮,导入虚机影像,验证影像文件格式,R,影像格式规则,支持格式、最大尺寸,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击导入影像按钮,导入虚机影像,解析影像元数据,R,虚拟机影像信息,影像ID、虚拟机配置、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：管理员，接收者：虚拟密码机管理系统,管理员在虚拟机管理界面点击导入影像按钮,导入虚机影像,保存影像到存储系统,W,虚拟机影像信息,影像ID、存储路径、上传时间,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机列表菜单,物理密码机列表,输入查询条件和分页信息,E,物理密码机信息,页码、单页数量、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机列表菜单,物理密码机列表,读取物理密码机配置信息,R,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机列表菜单,物理密码机列表,处理分页逻辑并组合数据,R,分页信息,总记录数、当前页码、总页数,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机列表菜单,物理密码机列表,展示物理密码机列表和分页信息,X,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、总记录数、当前页码、总页数,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,物理密码机新建,输入物理密码机基础信息,E,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,物理密码机新建,验证设备类型和IP格式,R,配置规则信息,设备类型白名单、IP格式规则、端口范围规则,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,物理密码机新建,保存物理密码机信息,W,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,物理密码机新建,关联密码产品信息,W,密码产品信息,密码产品ID、密码产品名称、子组件、配置建议,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,物理密码机新建,返回新建结果,X,操作结果信息,操作状态、提示信息、新设备ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机编辑按钮,物理密码机编辑,选择目标物理密码机,E,物理密码机信息,设备ID、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机编辑按钮,物理密码机编辑,输入编辑后的物理密码机信息,E,物理密码机信息,名称、备注、连接密码,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机编辑按钮,物理密码机编辑,更新物理密码机信息,W,物理密码机信息,设备ID、名称、备注、连接密码,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机删除按钮,物理密码机删除,选择待删除物理密码机,E,物理密码机信息,设备ID、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机删除按钮,物理密码机删除,验证设备管理状态,R,设备管理状态信息,设备ID、管理状态代码、管理状态名称,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机删除按钮,物理密码机删除,执行物理密码机删除,W,物理密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,物理密码机详情,选择目标物理密码机,E,物理密码机信息,设备ID、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,物理密码机详情,读取物理密码机基础信息,R,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,物理密码机详情,读取设备管理状态信息,R,设备管理状态信息,管理状态代码、管理状态名称、最后更新时间,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,物理密码机详情,读取设备服务类型信息,R,设备服务类型信息,服务类型代码、服务类型名称、服务描述,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,物理密码机详情,展示物理密码机详情信息,X,物理密码机信息,名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、管理状态代码、管理状态名称、服务类型代码、服务类型名称,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机强制删除按钮,强制删除,选择待强制删除物理密码机,E,物理密码机信息,设备ID、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机强制删除按钮,强制删除,验证设备运行状态,R,设备运行状态信息,设备ID、运行状态代码、运行状态名称,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机强制删除按钮,强制删除,执行强制删除操作,W,物理密码机信息,设备ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机管理页面跳转按钮,管理页面跳转,选择目标物理密码机,E,物理密码机信息,设备ID、名称、厂商、设备类型、设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机管理页面跳转按钮,管理页面跳转,读取管理页面配置信息,R,管理页面配置信息,管理页面URL模板、管理IP、管理端口,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机管理页面跳转按钮,管理页面跳转,生成管理页面访问链接,X,管理页面链接,完整访问URL,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在主密钥管理界面点击创建按钮,保护主密钥创建,输入主密钥创建参数,E,主密钥信息,设备类型、租户ID、密钥算法、密钥长度,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在主密钥管理界面点击创建按钮,保护主密钥创建,验证设备资源可用性,R,设备资源信息,设备状态、可用存储空间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在主密钥管理界面点击创建按钮,保护主密钥创建,生成主密钥并加密存储,W,主密钥信息,密钥值、加密算法、存储位置,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在主密钥管理界面点击创建按钮,保护主密钥创建,记录主密钥元数据,W,密钥元数据,创建时间、操作人、设备ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在主密钥管理界面点击创建按钮,保护主密钥创建,返回创建结果,X,操作结果信息,操作状态、密钥ID、错误代码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在设备管理界面点击同步按钮,保护主密钥同步,选择目标设备列表,E,设备信息,设备ID、设备类型、同步方式,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在设备管理界面点击同步按钮,保护主密钥同步,读取源设备主密钥,R,主密钥信息,密钥值、加密算法、存储位置,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在设备管理界面点击同步按钮,保护主密钥同步,加密传输主密钥,W,传输密钥信息,密钥值、传输加密算法、目标设备ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在设备管理界面点击同步按钮,保护主密钥同步,验证目标设备存储状态,R,设备资源信息,设备状态、存储空间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在设备管理界面点击同步按钮,保护主密钥同步,返回同步结果,X,操作结果信息,同步状态、成功设备列表、失败原因,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在备份管理界面点击备份按钮,保护主密钥备份,选择备份参数,E,备份参数,租户ID、备份类型、加密口令,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在备份管理界面点击备份按钮,保护主密钥备份,读取主密钥数据,R,主密钥信息,密钥值、加密算法、存储位置,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在备份管理界面点击备份按钮,保护主密钥备份,生成备份文件,W,备份文件信息,文件内容、加密方式、校验码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在备份管理界面点击备份按钮,保护主密钥备份,记录备份日志,W,备份记录,备份时间、操作人、文件哈希,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在备份管理界面点击备份按钮,保护主密钥备份,返回备份结果,X,操作结果信息,备份状态、文件路径、下载链接,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在恢复管理界面点击还原按钮,保护主密钥还原,选择还原文件,E,还原参数,文件路径、租户ID、验证口令,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在恢复管理界面点击还原按钮,保护主密钥还原,验证备份文件完整性,R,备份记录,文件哈希、加密方式、校验码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在恢复管理界面点击还原按钮,保护主密钥还原,解密并写入主密钥,W,主密钥信息,密钥值、存储位置、解密算法,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在恢复管理界面点击还原按钮,保护主密钥还原,更新设备密钥状态,W,设备密钥状态,设备ID、密钥版本、激活状态,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统管理员，接收者：密钥管理系统,系统管理员在恢复管理界面点击还原按钮,保护主密钥还原,返回还原结果,X,操作结果信息,还原状态、密钥ID、错误代码,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,输入签名证书信息,E,用户证书信息,证书类型、证书内容、证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,验证签名证书格式,R,证书验证规则,证书格式规则、序列号校验规则,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,输入加密证书信息,E,用户证书信息,证书类型、证书内容、证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,验证加密证书格式,R,证书验证规则,证书格式规则、序列号校验规则,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,保存用户证书信息,W,用户证书信息,证书类型、证书内容、证书序列号、用户ID、证书状态,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击证书导入按钮,用户证书导入,返回证书导入结果,X,操作结果信息,操作状态、成功/失败原因,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击用户证书管理页面的证书列表菜单,用户证书列表,输入分页查询条件,E,查询条件信息,页码、单页数量、用户ID过滤条件,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击用户证书管理页面的证书列表菜单,用户证书列表,读取用户证书列表数据,R,用户证书信息,证书ID、证书类型、证书内容摘要、证书状态、创建时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击用户证书管理页面的证书列表菜单,用户证书列表,返回分页证书列表,X,用户证书信息,总记录数、当前页数据、分页导航信息,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击停用按钮,用户证书停用,选择待停用的证书,E,用户证书信息,证书ID、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击停用按钮,用户证书停用,更新证书状态为停用,W,用户证书信息,证书ID、证书状态、操作时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击停用按钮,用户证书停用,返回证书停用结果,X,操作结果信息,操作状态、证书ID、更新时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击启用按钮,用户证书启用,选择待启用的证书,E,用户证书信息,证书ID、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击启用按钮,用户证书启用,更新证书状态为启用,W,用户证书信息,证书ID、证书状态、操作时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击启用按钮,用户证书启用,返回证书启用结果,X,操作结果信息,操作状态、证书ID、更新时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击删除按钮,用户证书删除,选择待删除的证书,E,用户证书信息,证书ID、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击删除按钮,用户证书删除,验证证书可删除性,R,证书关联信息,关联应用数量、证书使用状态,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书列表中点击删除按钮,用户证书删除,删除用户证书记录,W,用户证书信息,证书ID、删除时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在应用证书管理页面点击创建按钮,应用证书创建,输入应用证书基本信息,E,应用证书信息,应用ID、证书名称、证书内容、证书有效期,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在应用证书管理页面点击创建按钮,应用证书创建,验证证书格式和内容,R,证书校验规则,证书格式规则、有效期规则,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在应用证书管理页面点击创建按钮,应用证书创建,生成证书签名请求（CSR）,X,证书请求信息,CSR内容、请求时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在应用证书管理页面点击创建按钮,应用证书创建,保存应用证书到数据库,W,应用证书信息,应用ID、证书名称、证书内容、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在应用证书管理页面点击创建按钮,应用证书创建,返回证书创建结果,X,操作结果信息,操作状态、证书ID、错误信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书管理页面点击下载证书请求按钮,下载应用证书证书请求,选择证书请求模板,R,证书请求模板,模板内容、模板格式,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书管理页面点击下载证书请求按钮,下载应用证书证书请求,生成证书请求文件,X,证书请求文件,文件内容、文件格式,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书管理页面点击下载证书请求按钮,下载应用证书证书请求,返回下载链接,X,下载链接信息,下载URL、文件名称,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书请求文件并提交导入,应用证书导入,上传证书请求文件,E,证书请求文件,文件内容、文件格式,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书请求文件并提交导入,应用证书导入,解析并验证证书请求,R,证书校验规则,证书格式规则、签名算法,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书请求文件并提交导入,应用证书导入,保存证书到数据库,W,应用证书信息,证书内容、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书请求文件并提交导入,应用证书导入,返回导入结果,X,操作结果信息,操作状态、证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书和密钥文件并提交导入,导入应用证书和密钥,上传签名证书文件,E,签名证书信息,证书内容、证书格式,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书和密钥文件并提交导入,导入应用证书和密钥,上传加密证书文件,E,加密证书信息,证书内容、证书格式,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书和密钥文件并提交导入,导入应用证书和密钥,上传加密私钥文件,E,加密私钥信息,私钥内容、加密算法,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书和密钥文件并提交导入,导入应用证书和密钥,验证证书和密钥匹配性,R,证书校验规则,签名算法、密钥长度,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员上传证书和密钥文件并提交导入,导入应用证书和密钥,保存证书和密钥到数据库,W,应用证书信息,签名证书、加密证书、加密私钥,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员点击证书列表查询按钮,应用证书列表查询,输入分页查询条件,E,查询条件信息,页码、单页数量、证书名称,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员点击证书列表查询按钮,应用证书列表查询,读取应用证书列表,R,应用证书列表,证书ID、证书名称、证书状态、有效期,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员点击证书列表查询按钮,应用证书列表查询,返回分页证书列表,X,应用证书列表,证书ID、证书名称、证书状态、有效期,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击停用按钮,应用证书停用,选择待停用的证书,E,应用证书信息,证书ID、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击停用按钮,应用证书停用,更新证书状态为停用,W,应用证书信息,证书ID、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击停用按钮,应用证书停用,返回停用结果,X,操作结果信息,操作状态、证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击启用按钮,应用证书启用,选择待启用的证书,E,应用证书信息,证书ID、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击启用按钮,应用证书启用,更新证书状态为启用,W,应用证书信息,证书ID、证书状态,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击启用按钮,应用证书启用,返回启用结果,X,操作结果信息,操作状态、证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击删除按钮,应用证书删除,选择待删除的证书,E,应用证书信息,证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击删除按钮,应用证书删除,删除证书记录,W,应用证书信息,证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：管理员，接收者：密码服务管理平台,管理员在证书列表中选择证书并点击删除按钮,应用证书删除,返回删除结果,X,操作结果信息,操作状态、证书ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,输入密钥基本信息,E,密钥信息,密钥名称、密钥类型（对称/非对称）、密钥算法（3DES/AES/SM4/SM2/SM9/RSA/ZUC）、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,验证密钥算法有效性,R,算法配置信息,支持算法列表、算法参数规则,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,生成密钥材料,W,密钥信息,密钥值、密钥摘要值、生成时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,关联密钥生命周期属性,R,密钥生命周期属性,生命周期阶段、有效期、使用限制,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,保存密钥到存储,W,密钥信息,密钥ID、密钥状态、存储位置,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,记录密钥操作日志,W,操作日志信息,操作类型、操作时间、操作人,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,返回新增结果,X,操作结果信息,操作状态、密钥ID、错误信息,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,校验密钥唯一性,R,密钥信息,密钥名称、密钥算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,生成密钥标识符,W,密钥信息,key_id、key_code,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新增密钥按钮,新增密钥,触发密钥初始化事件,X,密钥事件信息,事件类型、密钥ID、触发时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥列表页面,密钥信息列表,输入分页查询条件,E,查询条件信息,页码、单页数量、应用名称、密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥列表页面,密钥信息列表,读取密钥基础信息,R,密钥信息,密钥ID、密钥名称、密钥类型、密钥算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥列表页面,密钥信息列表,读取密钥扩展信息,R,密钥信息,密钥长度、密钥状态、创建时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥列表页面,密钥信息列表,返回分页密钥列表,X,密钥信息,总记录数、分页数据、密钥详情,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户在密钥列表页面输入查询条件,密钥查询,输入查询参数,E,查询条件信息,应用名称、密钥ID、密钥名称,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户在密钥列表页面输入查询条件,密钥查询,执行模糊匹配查询,R,密钥信息,匹配的密钥ID、密钥名称,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户在密钥列表页面输入查询条件,密钥查询,返回查询结果,X,密钥信息,匹配密钥列表、匹配数量,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥详情按钮,密钥详情,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥详情按钮,密钥详情,读取密钥元数据,R,密钥信息,密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥详情按钮,密钥详情,读取密钥安全属性,R,密钥信息,密钥摘要值、密钥来源、是否可导出,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥详情按钮,密钥详情,返回密钥详情,X,密钥信息,完整密钥属性集合,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥链接查看按钮,密钥链接,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥链接查看按钮,密钥链接,读取密钥关联关系,R,密钥链接信息,关联密钥ID列表、链接类型、链接时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击密钥链接查看按钮,密钥链接,返回密钥链接数据,X,密钥链接信息,链接拓扑结构、关联密钥详情,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击历史版本按钮,密钥历史版本,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击历史版本按钮,密钥历史版本,读取版本历史记录,R,密钥版本信息,版本号、修改时间、修改人,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：密码资产数据管理系统,用户点击历史版本按钮,密钥历史版本,返回版本详情,X,密钥版本信息,版本差异对比、版本状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,验证翻新权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,生成新密钥材料,W,密钥信息,新密钥值、新密钥摘要,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,更新密钥状态,W,密钥信息,当前密钥状态、翻新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,记录翻新日志,W,操作日志信息,操作类型、旧密钥ID、新密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥翻新按钮,密钥翻新,返回翻新结果,X,操作结果信息,操作状态、新密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,读取翻新策略,R,策略配置信息,触发条件、翻新算法、执行频率,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,筛选待翻新密钥,R,密钥信息,密钥ID、当前状态、上次翻新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,批量生成新密钥,W,密钥信息,新密钥集合、生成时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,更新密钥状态,W,密钥信息,密钥ID、新状态、翻新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,记录批量操作日志,W,操作日志信息,操作类型、处理数量、异常记录,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：密码资产数据管理系统,定时任务触发自动翻新策略,密钥自动翻新,返回执行报告,X,操作结果信息,成功数量、失败数量、详细日志,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥归档按钮,密钥归档,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥归档按钮,密钥归档,验证归档权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥归档按钮,密钥归档,更新密钥存储位置,W,密钥信息,新存储位置、归档时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥归档按钮,密钥归档,记录归档操作,W,操作日志信息,操作类型、密钥ID、存储位置,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥归档按钮,密钥归档,返回归档结果,X,操作结果信息,操作状态、新存储位置,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,验证恢复权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,读取归档密钥数据,R,密钥信息,密钥值、存储位置,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,更新密钥状态,W,密钥信息,当前状态、恢复时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,记录恢复操作,W,操作日志信息,操作类型、密钥ID、恢复时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥恢复按钮,密钥恢复,返回恢复结果,X,操作结果信息,操作状态、当前密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥注销按钮,密钥注销,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥注销按钮,密钥注销,验证注销权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥注销按钮,密钥注销,更新密钥状态,W,密钥信息,密钥状态、注销时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥注销按钮,密钥注销,记录注销操作,W,操作日志信息,操作类型、密钥ID、注销时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥注销按钮,密钥注销,返回注销结果,X,操作结果信息,操作状态、当前密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,验证销毁权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,执行密钥销毁,W,密钥信息,密钥ID、销毁时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,更新密钥状态,W,密钥信息,密钥状态、销毁时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,记录销毁操作,W,操作日志信息,操作类型、密钥ID、销毁时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥销毁按钮,密钥销毁,返回销毁结果,X,操作结果信息,操作状态、销毁确认,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,输入密钥标识,E,密钥信息,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,验证删除权限,R,权限信息,用户角色、操作权限,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,执行密钥删除,W,密钥信息,密钥ID、删除时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,更新密钥状态,W,密钥信息,密钥状态、删除时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,记录删除操作,W,操作日志信息,操作类型、密钥ID、删除时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：管理员，接收者：密码资产数据管理系统,管理员点击密钥删除按钮,密钥删除,返回删除结果,X,操作结果信息,操作状态、删除确认,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库管理页面点击上传按钮,添加密码知识库数据,输入知识库基本信息,E,知识库信息,知识库名称、知识库分类、文件类型、文件大小、备注、租户可见性,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库管理页面点击上传按钮,添加密码知识库数据,上传文件并获取存储路径,E,知识库信息,文件内容、存储路径,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库管理页面点击上传按钮,添加密码知识库数据,验证知识库信息格式,R,配置规则信息,文件类型限制规则、存储路径规则,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库管理页面点击上传按钮,添加密码知识库数据,保存知识库记录及文件,W,知识库信息,知识库名称、知识库分类、文件类型、存储路径、创建时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击编辑按钮,编辑密码知识库数据,选择目标知识库,E,知识库信息,知识库ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击编辑按钮,编辑密码知识库数据,更新知识库信息,W,知识库信息,知识库名称、知识库分类、备注、租户可见性,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击编辑按钮,编辑密码知识库数据,返回编辑结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击删除按钮,删除密码知识库数据,选择目标知识库,E,知识库信息,知识库ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击删除按钮,删除密码知识库数据,删除知识库记录及关联文件,W,知识库信息,知识库ID、存储路径,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击删除按钮,删除密码知识库数据,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表输入查询条件,查询密码知识库数据,输入查询条件,E,查询条件信息,文件类型、知识库分类、知识库名称、页码、单页数量,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表输入查询条件,查询密码知识库数据,读取匹配的知识库记录,R,知识库信息,知识库ID、知识库名称、知识库分类、文件类型、创建时间、租户可见性,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表输入查询条件,查询密码知识库数据,返回查询结果,X,知识库信息,知识库ID、知识库名称、知识库分类、文件类型、创建时间、租户可见性,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击显示/隐藏按钮,显示/隐藏知识库信息,选择目标知识库,E,知识库信息,知识库ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击显示/隐藏按钮,显示/隐藏知识库信息,更新租户可见性状态,W,知识库信息,知识库ID、租户可见性,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击显示/隐藏按钮,显示/隐藏知识库信息,返回更新结果,X,操作结果信息,操作状态、提示信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击预览按钮,预览知识库信息,选择目标知识库,E,知识库信息,知识库ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击预览按钮,预览知识库信息,读取知识库文件信息,R,知识库信息,存储路径、文件类型,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在密码知识库列表点击预览按钮,预览知识库信息,返回预览数据,X,知识库信息,存储路径、文件类型,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击改造阶段管理页面的分页列表按钮,密码应用测评改造阶段分页列表,输入分页参数,E,分页信息,页码、单页数量,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击改造阶段管理页面的分页列表按钮,密码应用测评改造阶段分页列表,查询测评阶段信息,R,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击改造阶段管理页面的分页列表按钮,密码应用测评改造阶段分页列表,返回分页列表结果,X,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间、总记录数,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员在改造阶段管理页面输入过滤条件,密码应用测评改造阶段过滤查询,输入过滤条件,E,过滤条件信息,阶段编码、阶段名称,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员在改造阶段管理页面输入过滤条件,密码应用测评改造阶段过滤查询,查询符合过滤条件的测评阶段,R,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员在改造阶段管理页面输入过滤条件,密码应用测评改造阶段过滤查询,返回过滤查询结果,X,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击新增改造阶段按钮,新增密码应用测评改造阶段,输入新增阶段信息,E,测评阶段信息,阶段名称、阶段编码、阶段描述,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击新增改造阶段按钮,新增密码应用测评改造阶段,校验阶段编码唯一性,R,测评阶段信息,阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击新增改造阶段按钮,新增密码应用测评改造阶段,保存新增阶段信息,W,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击新增改造阶段按钮,新增密码应用测评改造阶段,返回新增结果,X,操作结果信息,操作状态、提示信息,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击编辑改造阶段按钮,编辑密码应用测评改造阶段,选择目标阶段,E,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击编辑改造阶段按钮,编辑密码应用测评改造阶段,输入编辑后的阶段信息,E,测评阶段信息,阶段名称、阶段编码、阶段描述,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击编辑改造阶段按钮,编辑密码应用测评改造阶段,校验阶段编码唯一性,R,测评阶段信息,阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击编辑改造阶段按钮,编辑密码应用测评改造阶段,更新阶段信息,W,测评阶段信息,阶段ID、阶段名称、阶段编码、修改时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击删除改造阶段按钮,删除密码应用测评改造阶段,选择目标阶段,E,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击删除改造阶段按钮,删除密码应用测评改造阶段,校验阶段关联应用,R,密码应用信息,阶段ID、应用ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击删除改造阶段按钮,删除密码应用测评改造阶段,删除阶段信息,W,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员点击删除改造阶段按钮,删除密码应用测评改造阶段,返回删除结果,X,操作结果信息,操作状态、提示信息,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员为密码应用设置测评阶段,密码应用设置测评改造阶段,选择目标应用,E,密码应用信息,应用ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员为密码应用设置测评阶段,密码应用设置测评改造阶段,选择目标阶段,E,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员为密码应用设置测评阶段,密码应用设置测评改造阶段,校验阶段有效性,R,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员为密码应用设置测评阶段,密码应用设置测评改造阶段,更新应用阶段信息,W,密码应用信息,应用ID、阶段ID、设置时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员为密码应用设置测评阶段,密码应用设置测评改造阶段,返回设置结果,X,操作结果信息,操作状态、提示信息,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员修改密码应用的测评阶段,密码应用修改测评改造阶段,选择目标应用,E,密码应用信息,应用ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员修改密码应用的测评阶段,密码应用修改测评改造阶段,选择新阶段,E,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员修改密码应用的测评阶段,密码应用修改测评改造阶段,校验阶段有效性,R,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员修改密码应用的测评阶段,密码应用修改测评改造阶段,更新应用阶段信息,W,密码应用信息,应用ID、阶段ID、修改时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查询密码应用的测评阶段,查询密码应用测评改造阶段,输入查询条件,E,查询条件信息,应用ID、阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查询密码应用的测评阶段,查询密码应用测评改造阶段,查询应用阶段信息,R,密码应用信息,应用ID、阶段ID、设置时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查询密码应用的测评阶段,查询密码应用测评改造阶段,返回查询结果,X,密码应用信息,应用ID、阶段ID、设置时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查看测评阶段的应用分布,测评改造阶段的应用分布,输入阶段ID,E,测评阶段信息,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查看测评阶段的应用分布,测评改造阶段的应用分布,统计关联应用数量,R,密码应用信息,阶段ID、应用ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：管理员，接收者：密码应用测评系统,管理员查看测评阶段的应用分布,测评改造阶段的应用分布,返回应用分布结果,X,应用分布信息,阶段ID、应用数量,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击分页查询按钮,应用测评报告分页列表查询,输入分页查询条件,E,应用测评报告信息,页码、单页数量、报告名称、所属应用、报告格式、测评分数范围,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击分页查询按钮,应用测评报告分页列表查询,读取分页测评报告数据,R,应用测评报告信息,报告ID、报告名称、所属应用ID、报告格式、测评分数、上报时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击分页查询按钮,应用测评报告分页列表查询,输出分页测评报告列表,X,应用测评报告信息,总记录数、当前页数据列表（含报告ID、报告名称、所属应用名称、报告格式、测评分数、上报时间）,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击新增按钮,新增应用测评报告对象,输入测评报告基础信息,E,应用测评报告信息,报告名称、所属应用ID、报告格式、测评分数,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击新增按钮,新增应用测评报告对象,校验报告唯一性约束,R,应用测评报告信息,报告名称、所属应用ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在应用测评报告页面点击新增按钮,新增应用测评报告对象,保存测评报告对象,W,应用测评报告信息,报告ID、报告名称、所属应用ID、报告格式、测评分数、创建时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件上传按钮,应用测评报告文件上传,上传测评报告文件,E,文件信息,文件名称、文件类型、文件内容,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件上传按钮,应用测评报告文件上传,关联文件与测评报告,W,应用测评报告信息,报告ID、文件存储路径、文件哈希值,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件上传按钮,应用测评报告文件上传,返回文件上传结果,X,操作结果信息,上传状态、文件URL、错误信息,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件预览按钮,应用测评报告文件预览,请求文件预览,E,应用测评报告信息,报告ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件预览按钮,应用测评报告文件预览,读取文件存储路径,R,应用测评报告信息,文件存储路径,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击文件预览按钮,应用测评报告文件预览,输出文件预览内容,X,文件内容,文件二进制流、文件类型,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表选择报告并点击下载按钮,应用测评报告文件下载,选择目标测评报告,E,应用测评报告信息,报告ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表选择报告并点击下载按钮,应用测评报告文件下载,读取文件存储信息,R,应用测评报告信息,文件存储路径、文件名称,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表选择报告并点击下载按钮,应用测评报告文件下载,生成文件下载链接,X,文件下载信息,下载URL、文件名称、文件类型,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表选择报告并点击下载按钮,应用测评报告文件下载,执行文件下载操作,X,文件内容,文件二进制流,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击编辑按钮,编辑应用测评报告对象,输入修改后的报告信息,E,应用测评报告信息,报告ID、报告名称、测评分数,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击编辑按钮,编辑应用测评报告对象,读取原报告数据,R,应用测评报告信息,报告ID、原报告名称、原测评分数,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告详情页点击编辑按钮,编辑应用测评报告对象,更新测评报告信息,W,应用测评报告信息,报告ID、报告名称、测评分数、修改时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表点击删除按钮,删除应用测评报告对象,选择待删除报告,E,应用测评报告信息,报告ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表点击删除按钮,删除应用测评报告对象,读取报告关联文件信息,R,应用测评报告信息,文件存储路径,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表点击删除按钮,删除应用测评报告对象,删除测评报告数据,W,应用测评报告信息,报告ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表点击删除按钮,删除应用测评报告对象,删除关联文件,W,文件信息,文件存储路径,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在测评报告列表点击删除按钮,删除应用测评报告对象,返回删除结果,X,操作结果信息,删除状态、错误信息,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评方案列表菜单,密码应用测评方案分页列表,输入分页和查询条件,E,查询条件信息,页码、单页数量、应用名称、等保等级、等保状态,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评方案列表菜单,密码应用测评方案分页列表,读取密码应用测评方案数据,R,密码应用测评方案信息,方案ID、应用ID、等保等级、等保状态、创建时间、模板ID,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评方案列表菜单,密码应用测评方案分页列表,返回分页列表结果,X,密码应用测评方案信息,页码、总记录数、方案ID、应用ID、等保等级、等保状态、创建时间、模板ID,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增测评方案按钮并提交表单,新建密码应用测评方案,输入测评方案基础信息,E,密码应用测评方案信息,应用ID、等保等级、等保状态,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增测评方案按钮并提交表单,新建密码应用测评方案,读取模板配置信息,R,模板信息,进度模板ID、要求模板ID、交付模板ID,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增测评方案按钮并提交表单,新建密码应用测评方案,保存测评方案数据,W,密码应用测评方案信息,方案ID、应用ID、等保等级、等保状态、进度模板ID、要求模板ID、交付模板ID、创建时间,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增测评方案按钮并提交表单,新建密码应用测评方案,返回操作结果,X,操作结果信息,操作状态、方案ID、提示信息,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定进度模板按钮,绑定密码应用测评进度模板,输入测评进度模板信息,E,测评进度模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定进度模板按钮,绑定密码应用测评进度模板,验证模板存在性及关联关系,R,测评进度模板信息,模板ID、关联密评对象ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定进度模板按钮,绑定密码应用测评进度模板,保存测评方案与模板绑定关系,W,测评方案绑定信息,方案ID、模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定要求模板按钮,绑定密码应用测评要求模板,输入测评要求模板信息,E,测评要求模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定要求模板按钮,绑定密码应用测评要求模板,验证模板存在性及关联关系,R,测评要求模板信息,模板ID、关联密评对象ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击绑定要求模板按钮,绑定密码应用测评要求模板,保存测评方案与模板绑定关系,W,测评方案绑定信息,方案ID、模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑进度模板按钮,密码应用测评进度模板编辑,读取当前绑定的测评进度模板信息,R,测评进度模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑进度模板按钮,密码应用测评进度模板编辑,输入修改后的模板信息,E,测评进度模板信息,模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑进度模板按钮,密码应用测评进度模板编辑,更新测评进度模板信息,W,测评进度模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑要求模板按钮,密码应用测评要求模板编辑,读取当前绑定的测评要求模板信息,R,测评要求模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑要求模板按钮,密码应用测评要求模板编辑,输入修改后的模板信息,E,测评要求模板信息,模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案对象页面点击编辑要求模板按钮,密码应用测评要求模板编辑,更新测评要求模板信息,W,测评要求模板信息,模板ID、模板标识、模板名称,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,读取测评要求模板条目,R,测评要求模板信息,模板ID、条目名称、风险等级、测评要求级别,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,输入测评要求满足状态,E,测评要求信息,条目ID、满足状态（满足/不满足）、操作人,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,读取关联的测评要求调研选项,R,调研选项信息,选项ID、选项内容、适用场景,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,选择并关联调研选项,W,测评要求信息,条目ID、关联选项ID、关联时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,读取测评要求改进建议,R,改进建议信息,建议ID、建议内容、风险等级,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,编辑改进建议内容,W,改进建议信息,建议ID、修改后内容、修改时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评要求推进页面选择测评要求模板并开始评估,密码应用测评要求推进,保存测评要求推进结果,W,测评要求信息,条目ID、最终状态、关联选项ID、改进建议ID,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评进度推进页面点击编辑按钮,密码应用测评进度推进,读取当前测评整改进度,R,测评进度信息,进度ID、当前阶段、完成百分比、截止时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评进度推进页面点击编辑按钮,密码应用测评进度推进,输入整改进度更新信息,E,测评进度信息,进度ID、更新后阶段、更新后百分比,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评进度推进页面点击编辑按钮,密码应用测评进度推进,验证进度更新合理性,R,测评要求信息,关联条目ID、当前满足状态,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评进度推进页面点击编辑按钮,密码应用测评进度推进,保存整改进度更新,W,测评进度信息,进度ID、更新后阶段、更新后百分比、更新时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在密评进度推进页面点击编辑按钮,密码应用测评进度推进,返回进度更新结果,X,操作结果信息,操作状态、提示信息、更新后进度数据,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员进入密评进度跟踪报告展示页面,密码应用测评进度跟踪报告展示,读取密评进度模板数据,R,进度模板信息,模板ID、阶段名称、阶段描述、显示状态,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员进入密评进度跟踪报告展示页面,密码应用测评进度跟踪报告展示,生成流程进度列表,R,每日报告信息,报告ID、密评对象ID、阶段名称、预估完成时间、进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员进入密评进度跟踪报告展示页面,密码应用测评进度跟踪报告展示,展示可编辑的进度列表,X,进度展示信息,阶段名称、预估完成时间、进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员进入密评进度跟踪报告展示页面,密码应用测评进度跟踪报告展示,保存编辑后的进度数据,W,每日报告信息,报告ID、密评对象ID、阶段名称、预估完成时间、进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击模板详情编辑按钮,密码应用测评进度跟踪报告编辑,读取当前进度模板配置,R,进度模板信息,模板ID、阶段名称、显示状态、关联密评对象,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击模板详情编辑按钮,密码应用测评进度跟踪报告编辑,输入模板编辑参数,E,模板编辑参数,阶段名称、显示状态、排序序号,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击模板详情编辑按钮,密码应用测评进度跟踪报告编辑,校验模板配置完整性,R,配置校验规则,必填字段规则、唯一性校验规则,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击模板详情编辑按钮,密码应用测评进度跟踪报告编辑,更新进度模板配置,W,进度模板信息,模板ID、阶段名称、显示状态、关联密评对象,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击模板详情编辑按钮,密码应用测评进度跟踪报告编辑,返回模板更新结果,X,操作结果信息,操作状态、错误信息,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击报告下载按钮,密码应用测评进度跟踪报告下载,读取指定时间段的报告数据,R,历史报告信息,报告ID、生成时间、密评对象ID、阶段进度数据,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击报告下载按钮,密码应用测评进度跟踪报告下载,生成标准化报告文件,W,报告文件信息,文件ID、文件类型、生成时间、数据内容,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击报告下载按钮,密码应用测评进度跟踪报告下载,验证文件生成完整性,R,文件校验规则,文件格式规则、内容完整性规则,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击报告下载按钮,密码应用测评进度跟踪报告下载,输出下载文件,X,报告文件信息,文件ID、文件类型、生成时间、下载链接,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击密码应用测评差距分析展示页面,密码应用测评差距分析内容展示,输入查询条件（如测评ID、应用ID）,E,查询条件信息,测评ID、应用ID、页码、单页数量,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击密码应用测评差距分析展示页面,密码应用测评差距分析内容展示,验证用户权限并读取测评差距数据,R,密码应用测评差距信息,差距ID、测评ID、应用ID、差距描述、状态、创建时间,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击密码应用测评差距分析展示页面,密码应用测评差距分析内容展示,关联密码应用基础信息,R,密码应用信息,应用ID、业务系统名称、类型、所属省份,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击密码应用测评差距分析展示页面,密码应用测评差距分析内容展示,格式化并展示差距分析内容,X,密码应用测评差距信息,差距ID、测评ID、应用ID、差距描述、状态、创建时间、业务系统名称,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员点击密码应用测评差距分析展示页面,密码应用测评差距分析内容展示,返回分页导航信息,X,分页信息,当前页码、总页数、总记录数,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击编辑按钮,密码应用测评差距分析内容编辑,选择目标差距记录,E,密码应用测评差距信息,差距ID、测评ID、应用ID,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击编辑按钮,密码应用测评差距分析内容编辑,读取当前差距分析内容,R,密码应用测评差距信息,差距ID、测评ID、应用ID、差距描述、状态,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击编辑按钮,密码应用测评差距分析内容编辑,输入更新后的差距描述,E,密码应用测评差距信息,差距描述、状态,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击编辑按钮,密码应用测评差距分析内容编辑,验证并保存更新内容,W,密码应用测评差距信息,差距ID、测评ID、应用ID、差距描述、状态、修改时间,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,输入报告生成参数（如测评ID、报告格式）,E,报告生成参数,测评ID、报告格式（PDF/Word）,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,读取完整的差距分析数据集,R,密码应用测评差距信息,差距ID、测评ID、应用ID、差距描述、状态、创建时间,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,关联密码应用基础信息,R,密码应用信息,应用ID、业务系统名称、类型、所属省份,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,生成结构化报告文件,W,报告文件信息,报告ID、文件路径、生成时间、文件格式,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,返回报告生成结果,X,操作结果信息,操作状态、报告文件路径,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在报告生成后点击导出按钮,密码应用测评差距分析内容报告导出,选择目标报告文件,E,报告文件信息,报告ID、文件路径,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在报告生成后点击导出按钮,密码应用测评差距分析内容报告导出,读取报告文件内容,R,报告文件信息,文件内容、文件格式,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：管理员，接收者：密码应用测评系统,管理员在报告生成后点击导出按钮,密码应用测评差距分析内容报告导出,生成下载链接并返回,X,文件下载信息,下载链接、文件名称、文件大小,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员点击密评机构管理菜单,密评机构分页列表查询,输入分页查询条件,E,密评机构信息,页码、单页数量、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员点击密评机构管理菜单,密评机构分页列表查询,读取分页密评机构数据,R,密评机构信息,ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员点击密评机构管理菜单,密评机构分页列表查询,输出分页密评机构列表,X,密评机构信息,页码、单页数量、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击新增按钮,新增密评机构,输入新增密评机构信息,E,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击新增按钮,新增密评机构,验证密评机构信息格式,R,配置规则信息,机构名称长度规则、电话格式规则、邮箱格式规则,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击新增按钮,新增密评机构,保存新增密评机构数据,W,密评机构信息,ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击新增按钮,新增密评机构,返回新增操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击编辑按钮,编辑密评机构,读取待编辑密评机构信息,R,密评机构信息,ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击编辑按钮,编辑密评机构,输入修改后的密评机构信息,E,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击编辑按钮,编辑密评机构,保存修改后的密评机构数据,W,密评机构信息,ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击删除按钮,删除密评机构,输入删除确认请求,E,操作请求信息,机构ID、确认标识,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击删除按钮,删除密评机构,读取待删除密评机构信息,R,密评机构信息,ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击删除按钮,删除密评机构,删除密评机构数据,W,密评机构信息,ID,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：管理员，接收者：密码服务平台-密评机构管理模块,管理员在密评机构管理页面点击删除按钮,删除密评机构,返回删除操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击密码漏洞/安全事件类型管理菜单,密码漏洞/安全事件类型分页列表展示,输入分页查询条件,E,密码漏洞/安全事件类型信息,页码、单页数量、事件类型名称、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击密码漏洞/安全事件类型管理菜单,密码漏洞/安全事件类型分页列表展示,读取密码漏洞/安全事件类型数据,R,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称、描述、状态、创建时间、更新时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击密码漏洞/安全事件类型管理菜单,密码漏洞/安全事件类型分页列表展示,展示分页后的事件类型列表,X,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称、描述、状态、创建时间、更新时间、页码、总页数,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击新增密码漏洞/安全事件类型按钮,新增密码漏洞/安全事件类型,输入新增事件类型信息,E,密码漏洞/安全事件类型信息,事件类型名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击新增密码漏洞/安全事件类型按钮,新增密码漏洞/安全事件类型,验证事件类型名称唯一性,R,密码漏洞/安全事件类型信息,事件类型名称,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击新增密码漏洞/安全事件类型按钮,新增密码漏洞/安全事件类型,保存新增事件类型数据,W,密码漏洞/安全事件类型信息,事件类型名称、描述、状态、创建人、创建时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击新增密码漏洞/安全事件类型按钮,新增密码漏洞/安全事件类型,返回新增操作结果,X,操作结果信息,操作状态、提示信息、事件类型ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击编辑密码漏洞/安全事件类型按钮,编辑密码漏洞/安全事件类型,输入编辑事件类型信息,E,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击编辑密码漏洞/安全事件类型按钮,编辑密码漏洞/安全事件类型,读取原始事件类型数据,R,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击编辑密码漏洞/安全事件类型按钮,编辑密码漏洞/安全事件类型,验证事件类型名称唯一性,R,密码漏洞/安全事件类型信息,事件类型名称,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击编辑密码漏洞/安全事件类型按钮,编辑密码漏洞/安全事件类型,保存编辑后的事件类型数据,W,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称、描述、状态、更新人、更新时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击编辑密码漏洞/安全事件类型按钮,编辑密码漏洞/安全事件类型,返回编辑操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击删除密码漏洞/安全事件类型按钮,删除密码漏洞/安全事件类型,输入待删除事件类型ID,E,密码漏洞/安全事件类型信息,事件类型ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击删除密码漏洞/安全事件类型按钮,删除密码漏洞/安全事件类型,读取待删除事件类型数据,R,密码漏洞/安全事件类型信息,事件类型ID、事件类型名称,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击删除密码漏洞/安全事件类型按钮,删除密码漏洞/安全事件类型,删除事件类型数据,W,密码漏洞/安全事件类型信息,事件类型ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：管理员，接收者：密码漏洞/安全事件类型管理模块,管理员点击删除密码漏洞/安全事件类型按钮,删除密码漏洞/安全事件类型,返回删除操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统管理员，接收者：密码漏洞/安全事件类型管理模块,系统部署时触发初始化事件类型操作,初始化密码漏洞/安全事件类型,读取初始化事件类型数据,R,密码漏洞/安全事件类型信息,事件类型名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统管理员，接收者：密码漏洞/安全事件类型管理模块,系统部署时触发初始化事件类型操作,初始化密码漏洞/安全事件类型,验证初始化数据完整性,R,系统配置信息,初始化数据校验规则,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统管理员，接收者：密码漏洞/安全事件类型管理模块,系统部署时触发初始化事件类型操作,初始化密码漏洞/安全事件类型,批量保存初始化事件类型,W,密码漏洞/安全事件类型信息,事件类型名称、描述、状态、创建人、创建时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统管理员，接收者：密码漏洞/安全事件类型管理模块,系统部署时触发初始化事件类型操作,初始化密码漏洞/安全事件类型,返回初始化操作结果,X,操作结果信息,操作状态、提示信息、成功数量、失败数量,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击分页查询按钮,漏洞/安全事件告警分页列表展示,输入分页参数及查询条件,E,告警信息,页码、单页数量、告警类型、告警状态、事件名称,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击分页查询按钮,漏洞/安全事件告警分页列表展示,读取分页告警数据,R,告警信息,ID、告警类型、事件名称、告警级别、发生时间、状态、租户ID、区域ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击分页查询按钮,漏洞/安全事件告警分页列表展示,计算分页总数,R,分页统计信息,总记录数、总页数,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击分页查询按钮,漏洞/安全事件告警分页列表展示,输出分页告警列表及统计信息,X,告警信息,ID、告警类型、事件名称、告警级别、发生时间、状态、租户ID、区域ID、总记录数、总页数,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击新增按钮,新增漏洞/安全事件告警,输入新增告警信息,E,告警信息,事件名称、告警类型、告警级别、描述、触发条件、处理建议,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击新增按钮,新增漏洞/安全事件告警,验证告警信息格式,R,校验规则信息,事件名称长度限制、告警级别范围、描述字符限制,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击新增按钮,新增漏洞/安全事件告警,保存新增告警记录,W,告警信息,ID、事件名称、告警类型、告警级别、描述、触发条件、处理建议、创建时间、创建人,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击新增按钮,新增漏洞/安全事件告警,关联告警指标,W,告警指标,告警ID、指标ID、指标名称、阈值,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击新增按钮,新增漏洞/安全事件告警,返回新增结果,X,操作结果信息,操作状态、告警ID、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击启用按钮,漏洞/安全事件告警启用,输入告警启用信息,E,告警信息,告警ID、操作人,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击启用按钮,漏洞/安全事件告警启用,读取告警配置信息,R,告警信息,ID、告警类型、触发条件、状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击启用按钮,漏洞/安全事件告警启用,更新告警状态为启用,W,告警信息,ID、状态、更新时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击禁用按钮,漏洞/安全事件告警禁用,输入告警禁用信息,E,告警信息,告警ID、操作人,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击禁用按钮,漏洞/安全事件告警禁用,读取告警配置信息,R,告警信息,ID、告警类型、触发条件、状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击禁用按钮,漏洞/安全事件告警禁用,更新告警状态为禁用,W,告警信息,ID、状态、更新时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击删除按钮,删除告警漏洞/安全事件,输入删除告警信息,E,告警信息,告警ID、操作人,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击删除按钮,删除告警漏洞/安全事件,验证告警关联关系,R,关联校验信息,关联告警指标数量、历史告警记录数量,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击删除按钮,删除告警漏洞/安全事件,删除告警记录,W,告警信息,ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：系统管理员，接收者：漏洞/安全事件管理系统,系统管理员在漏洞/安全事件管理页面点击删除按钮,删除告警漏洞/安全事件,返回删除结果,X,操作结果信息,操作状态、删除记录数、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警通知人列表展示菜单,漏洞/安全事件告警通知人列表展示,输入分页查询条件,E,告警通知人信息,页码、单页数量、通知人名称、通知人类型,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警通知人列表展示菜单,漏洞/安全事件告警通知人列表展示,读取告警通知人列表数据,R,告警通知人信息,通知人ID、通知人名称、通知人类型、通知方式、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警通知人列表展示菜单,漏洞/安全事件告警通知人列表展示,展示分页后的告警通知人列表,X,告警通知人信息,页码、单页数量、通知人ID、通知人名称、通知人类型、通知方式、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理界面点击绑定告警通知人按钮,绑定漏洞/安全事件告警通知人,选择目标漏洞/安全事件,E,漏洞/安全事件信息,事件ID、事件名称、事件级别,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理界面点击绑定告警通知人按钮,绑定漏洞/安全事件告警通知人,选择目标告警通知人,E,告警通知人信息,通知人ID、通知人名称、通知方式,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理界面点击绑定告警通知人按钮,绑定漏洞/安全事件告警通知人,保存事件与通知人绑定关系,W,事件通知绑定信息,事件ID、通知人ID、绑定时间、操作人,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理界面点击绑定告警通知人按钮,绑定漏洞/安全事件告警通知人,返回绑定操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人管理界面点击新增按钮,新增漏洞/安全事件告警通知人,输入告警通知人基本信息,E,告警通知人信息,通知人名称、通知人类型、通知方式、通知模板,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人管理界面点击新增按钮,新增漏洞/安全事件告警通知人,验证通知人信息格式,R,系统配置规则,通知方式格式规则、模板校验规则,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人管理界面点击新增按钮,新增漏洞/安全事件告警通知人,保存新增的告警通知人信息,W,告警通知人信息,通知人ID、通知人名称、通知人类型、通知方式、通知模板、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人管理界面点击新增按钮,新增漏洞/安全事件告警通知人,返回新增操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人列表点击删除按钮,删除漏洞/安全事件告警通知人,选择待删除的告警通知人,E,告警通知人信息,通知人ID、通知人名称,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人列表点击删除按钮,删除漏洞/安全事件告警通知人,校验通知人关联关系,R,事件通知绑定信息,事件ID、通知人ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人列表点击删除按钮,删除漏洞/安全事件告警通知人,删除告警通知人信息,W,告警通知人信息,通知人ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警通知人列表点击删除按钮,删除漏洞/安全事件告警通知人,返回删除操作结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击提交按钮,告警邮箱服务器配置提交,输入邮箱服务器配置信息,E,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、邮箱密码、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击提交按钮,告警邮箱服务器配置提交,验证配置信息格式,R,配置规则信息,IP地址格式规则、端口范围规则、邮箱格式规则,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击提交按钮,告警邮箱服务器配置提交,保存邮箱服务器配置,W,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、邮箱密码、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击提交按钮,告警邮箱服务器配置提交,返回配置结果,X,操作结果信息,操作状态、错误信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击查询按钮,告警邮箱服务器配置查询,输入查询条件,E,查询条件信息,页码、单页数量,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击查询按钮,告警邮箱服务器配置查询,读取邮箱服务器配置,R,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击查询按钮,告警邮箱服务器配置查询,返回查询结果,X,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击重置按钮,告警邮箱服务器配置重置,确认重置操作,E,操作确认信息,确认标识,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击重置按钮,告警邮箱服务器配置重置,清空邮箱服务器配置,W,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、邮箱密码、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击重置按钮,告警邮箱服务器配置重置,返回重置结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击发送测试邮件按钮,告警验证邮件发送,输入测试邮件信息,E,测试邮件信息,收件人邮箱、邮件主题、邮件内容,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击发送测试邮件按钮,告警验证邮件发送,读取邮箱服务器配置,R,邮箱配置信息,SMTP服务器地址、SMTP端口、邮箱地址、邮箱密码、是否启用SSL,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击发送测试邮件按钮,告警验证邮件发送,构建邮件内容,R,测试邮件信息,收件人邮箱、邮件主题、邮件内容,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击发送测试邮件按钮,告警验证邮件发送,发送测试邮件,X,测试邮件信息,收件人邮箱、邮件主题、邮件内容,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：管理员，接收者：告警邮箱配置模块,管理员在告警邮箱配置页面点击发送测试邮件按钮,告警验证邮件发送,返回发送结果,X,操作结果信息,操作状态、错误信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件详情页面,漏洞/安全事件基本信息展示,读取漏洞/安全事件基础信息,R,漏洞/安全事件信息,事件ID、事件名称、事件描述、发现时间、严重等级、影响范围,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件详情页面,漏洞/安全事件基本信息展示,展示漏洞/安全事件详细信息,X,漏洞/安全事件信息,事件ID、事件名称、事件描述、发现时间、严重等级、影响范围,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件详情页面,漏洞/安全事件基本信息展示,加载关联的告警配置信息,R,告警配置信息,告警类型、告警阈值、告警状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件编辑按钮,漏洞/安全事件信息编辑,输入漏洞/安全事件修改信息,E,漏洞/安全事件信息,事件ID、事件名称、事件描述、严重等级、影响范围,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件编辑按钮,漏洞/安全事件信息编辑,验证事件信息格式和完整性,R,校验规则信息,名称长度规则、描述格式规则,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击漏洞/安全事件编辑按钮,漏洞/安全事件信息编辑,更新漏洞/安全事件信息,W,漏洞/安全事件信息,事件ID、事件名称、事件描述、严重等级、影响范围,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击告警阈值配置按钮,漏洞/安全事件告警阈值配置,输入告警阈值参数,E,告警阈值信息,阈值类型、阈值数值、阈值单位、触发条件,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击告警阈值配置按钮,漏洞/安全事件告警阈值配置,校验阈值参数有效性,R,校验规则信息,数值范围规则、单位匹配规则,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击告警阈值配置按钮,漏洞/安全事件告警阈值配置,保存告警阈值配置,W,告警阈值信息,阈值类型、阈值数值、阈值单位、触发条件,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击告警阈值配置按钮,漏洞/安全事件告警阈值配置,关联阈值与事件,W,漏洞/安全事件信息,事件ID、关联阈值ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击告警阈值配置按钮,漏洞/安全事件告警阈值配置,返回配置结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击标签配置按钮,漏洞/安全事件标签配置,输入标签配置信息,E,标签信息,标签名称、标签类型、关联事件ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击标签配置按钮,漏洞/安全事件标签配置,验证标签唯一性,R,标签信息,标签名称、标签类型,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击标签配置按钮,漏洞/安全事件标签配置,保存标签配置,W,标签信息,标签名称、标签类型、关联事件ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,输入组合阈值规则,E,组合阈值规则,规则名称、条件列表、逻辑关系,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,校验规则语法有效性,R,校验规则信息,逻辑运算符规则、条件格式规则,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,保存组合阈值规则,W,组合阈值规则,规则名称、条件列表、逻辑关系,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,关联规则与事件,W,漏洞/安全事件信息,事件ID、关联规则ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,生成规则执行脚本,W,规则执行信息,规则ID、执行脚本内容,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：管理员，接收者：漏洞/安全事件管理系统,管理员点击组合阈值配置按钮,漏洞/安全事件告警组合阈值配置,返回配置结果,X,操作结果信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击密码产品监控列表页面,密码产品监控列表分布展示,输入查询条件（名称/管理IP）,E,查询条件信息,页码、单页数量、产品名称、管理IP,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击密码产品监控列表页面,密码产品监控列表分布展示,读取密码产品监控列表数据,R,密码产品信息,产品ID、产品名称、产品类型、管理IP、状态、创建时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击密码产品监控列表页面,密码产品监控列表分布展示,展示密码产品监控列表,X,密码产品信息,产品ID、产品名称、产品类型、管理IP、状态、创建时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控数据展示按钮,密码产品当前监控数据列表展示,选择目标密码产品,E,密码产品信息,产品ID、产品名称,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控数据展示按钮,密码产品当前监控数据列表展示,读取当前监控指标数据,R,监控数据信息,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控数据展示按钮,密码产品当前监控数据列表展示,展示当前监控数据列表,X,监控数据信息,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控数据展示按钮,密码产品当前监控数据列表展示,生成实时监控数据快照,W,监控数据信息,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控数据展示按钮,密码产品监控历史数据列表展示,输入查询条件（时间范围/产品名称）,E,查询条件信息,页码、单页数量、产品名称、开始时间、结束时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控数据展示按钮,密码产品监控历史数据列表展示,读取历史监控指标数据,R,历史监控数据,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控数据展示按钮,密码产品监控历史数据列表展示,展示历史监控数据列表,X,历史监控数据,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控数据展示按钮,密码产品监控历史数据列表展示,导出历史监控数据,X,历史监控数据,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控数据展示按钮,密码产品监控历史数据列表展示,生成历史数据统计报表,W,监控报表信息,产品ID、平均CPU使用率、峰值内存使用量、磁盘使用趋势,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控折线图展示按钮,密码产品当前监控数据折线图,选择目标密码产品,E,密码产品信息,产品ID、产品名称,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控折线图展示按钮,密码产品当前监控数据折线图,读取实时监控指标数据,R,监控数据信息,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控折线图展示按钮,密码产品当前监控数据折线图,生成实时折线图数据,X,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控折线图展示按钮,密码产品当前监控数据折线图,缓存实时图表数据,W,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击当前监控折线图展示按钮,密码产品当前监控数据折线图,推送实时图表更新,X,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控折线图展示按钮,密码产品监控历史数据折线图,输入查询条件（时间范围/产品名称）,E,查询条件信息,页码、单页数量、产品名称、开始时间、结束时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控折线图展示按钮,密码产品监控历史数据折线图,读取历史监控指标数据,R,历史监控数据,产品ID、CPU使用率、内存使用量、磁盘使用量、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控折线图展示按钮,密码产品监控历史数据折线图,生成历史折线图数据,X,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控折线图展示按钮,密码产品监控历史数据折线图,缓存历史图表数据,W,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：管理员，接收者：密码产品监控系统,管理员点击历史监控折线图展示按钮,密码产品监控历史数据折线图,导出历史图表数据,X,监控图表数据,时间序列、CPU使用率曲线、内存使用量曲线、磁盘使用量曲线,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码应用数据上报页面点击数据采集按钮,密码应用上报数据采集,输入数据采集规则,E,采集规则信息,采集字段列表、过滤条件、数据格式要求,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码应用数据上报页面点击数据采集按钮,密码应用上报数据采集,读取密码应用基础数据,R,密码应用数据,业务系统ID、业务系统名称、密码业务类型、密码产品类型,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码应用数据上报页面点击数据采集按钮,密码应用上报数据采集,转换数据格式并校验完整性,R,数据校验规则,字段校验规则、格式校验规则,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码应用数据上报页面点击数据采集按钮,密码应用上报数据采集,保存采集的上报数据,W,密码应用上报数据,业务系统ID、上报时间、采集状态、错误信息,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,读取接口配置参数,R,接口配置信息,接口地址、认证方式、数据传输协议,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,验证接口格式规范,R,接口规范文档,字段映射规则、数据格式要求,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,建立接口连接并发送测试数据,X,测试数据包,测试业务系统ID、测试密码类型、测试时间戳,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,接收并解析接口响应,E,接口响应信息,响应状态码、错误描述、数据接收时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,记录接口对接日志,W,接口日志信息,操作时间、操作类型、操作结果,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：集团平台接口服务,系统检测到上报接口配置变更,密码应用数据上报接口对接,更新接口配置状态,W,接口配置信息,连接状态、最后更新时间、异常计数,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：数据上报系统,用户访问密码应用上报数据列表页面,密码应用上报数据列表展示,输入分页和筛选条件,E,查询条件信息,页码、单页数量、业务系统名称、上报时间范围,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：数据上报系统,用户访问密码应用上报数据列表页面,密码应用上报数据列表展示,读取上报数据列表,R,密码应用上报数据列表,业务系统ID、业务系统名称、上报时间、数据状态、错误信息,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：数据上报系统,用户访问密码应用上报数据列表页面,密码应用上报数据列表展示,展示分页数据列表,X,密码应用上报数据列表,业务系统ID、业务系统名称、上报时间、数据状态、错误信息,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：数据上报系统,系统检测到配置的上报时间点,密码应用数据定时上报更新,读取上报频率配置,R,上报配置信息,上报频率、启用状态、上次上报时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：数据上报系统,系统检测到配置的上报时间点,密码应用数据定时上报更新,触发数据上报任务,X,定时任务指令,任务类型、执行时间、目标接口,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：数据上报系统,系统检测到配置的上报时间点,密码应用数据定时上报更新,执行数据上报操作,X,密码应用上报数据,业务系统ID、密码业务类型、上报时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：数据上报系统,系统检测到配置的上报时间点,密码应用数据定时上报更新,更新上报任务状态,W,上报任务记录,任务ID、执行时间、执行结果、错误信息,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码产品管理页面点击数据采集按钮,密码产品信息上报数据采集,输入密码产品查询条件,E,查询条件信息,页码、单页数量、产品类型、数据类型,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码产品管理页面点击数据采集按钮,密码产品信息上报数据采集,读取密码产品基础信息,R,密码产品信息,产品ID、服务类型、数据类型、产品名称、存储路径,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码产品管理页面点击数据采集按钮,密码产品信息上报数据采集,读取密码产品附件信息,R,密码产品信息,产品ID、存储路径、文件名称、备注,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员在密码产品管理页面点击数据采集按钮,密码产品信息上报数据采集,保存采集的密码产品数据,W,密码产品信息,产品ID、服务类型、数据类型、产品名称、存储路径、文件名称,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,读取接口配置参数,R,接口配置信息,接口地址、认证方式、超时时间,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,验证接口参数格式,R,配置规则信息,URL格式规则、认证方式校验规则,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,建立接口连接测试,E,接口测试信息,测试数据包、认证凭证,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,保存接口配置状态,W,接口配置信息,接口地址、认证方式、连接状态,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,返回接口对接结果,X,操作结果信息,操作状态、错误代码、详细日志,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到接口配置变更或首次对接,密码产品信息上报接口对接,记录接口调用日志,W,系统日志信息,操作时间、操作类型、操作详情,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员访问密码产品上报数据列表页面,密码产品信息上报数据列表展示,输入分页查询条件,E,查询条件信息,页码、单页数量、产品状态,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员访问密码产品上报数据列表页面,密码产品信息上报数据列表展示,读取上报密码产品数据,R,密码产品信息,产品ID、服务类型、数据类型、产品名称、上报时间,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：管理员，接收者：数据上报系统,管理员访问密码产品上报数据列表页面,密码产品信息上报数据列表展示,展示密码产品数据列表,X,密码产品信息,产品ID、服务类型、数据类型、产品名称、上报时间,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到配置的上报时间点,密码产品信息数据定时上报更新,读取定时任务配置,R,定时任务信息,执行频率、开始时间、结束时间,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到配置的上报时间点,密码产品信息数据定时上报更新,读取待上报密码产品数据,R,密码产品信息,产品ID、服务类型、数据类型、产品名称、存储路径,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到配置的上报时间点,密码产品信息数据定时上报更新,调用集团平台上报接口,E,接口请求信息,产品ID、服务类型、数据类型、产品名称、存储路径,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：系统，接收者：集团平台接口,系统检测到配置的上报时间点,密码产品信息数据定时上报更新,记录上报操作日志,W,系统日志信息,操作时间、操作类型、上报数量、失败详情,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,系统根据配置的上报频率触发密钥信息采集任务,密钥信息上报数据采集,读取密钥基础信息,R,密钥信息,密钥ID、密钥名称、密钥状态、创建时间、所属省份编码,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,系统根据配置的上报频率触发密钥信息采集任务,密钥信息上报数据采集,读取上报配置规则,R,上报配置信息,上报内容标识、上报频率ID、启用状态,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,系统根据配置的上报频率触发密钥信息采集任务,密钥信息上报数据采集,过滤符合上报条件的密钥数据,R,密钥信息,密钥ID、密钥名称、密钥状态、创建时间、所属省份编码,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,系统根据配置的上报频率触发密钥信息采集任务,密钥信息上报数据采集,生成标准化上报数据格式,W,上报数据信息,密钥ID、密钥名称、密钥状态、创建时间、所属省份编码、上报时间戳,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,读取接口认证凭据,R,接口认证信息,认证令牌、接口地址、超时时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,构建接口请求报文,E,接口请求信息,上报数据信息、请求时间戳、签名值,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,发送HTTP POST请求到集团平台,X,接口请求信息,上报数据信息、请求时间戳、签名值,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,接收集团平台响应报文,R,接口响应信息,响应状态码、响应消息、响应时间戳,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,验证接口响应结果,R,接口响应信息,响应状态码、响应消息、响应时间戳,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：数据上报服务，接收者：集团平台接口,采集完成的上报数据达到发送条件,密钥信息上报接口对接,记录接口调用日志,W,接口日志信息,请求内容、响应内容、调用时间、调用状态,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：管理员，接收者：数据上报服务,管理员访问密钥上报数据列表页面,密钥信息上报数据列表展示,输入分页查询条件,E,查询条件信息,页码、单页数量、密钥名称、所属省份编码,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：管理员，接收者：数据上报服务,管理员访问密钥上报数据列表页面,密钥信息上报数据列表展示,读取已上报的密钥数据,R,密钥信息,密钥ID、密钥名称、密钥状态、创建时间、所属省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：管理员，接收者：数据上报服务,管理员访问密钥上报数据列表页面,密钥信息上报数据列表展示,返回分页查询结果,X,密钥信息,页码、单页数量、密钥ID、密钥名称、密钥状态、创建时间、所属省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,定时任务触发密钥信息更新上报,密钥信息数据定时上报更新,读取定时任务配置,R,定时任务配置,任务ID、执行频率、下次执行时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,定时任务触发密钥信息更新上报,密钥信息数据定时上报更新,读取增量更新的密钥数据,R,密钥信息,密钥ID、密钥名称、密钥状态、修改时间、所属省份编码,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,定时任务触发密钥信息更新上报,密钥信息数据定时上报更新,生成增量上报数据包,W,上报数据信息,密钥ID、密钥名称、密钥状态、修改时间、所属省份编码、上报时间戳,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：系统定时任务，接收者：数据上报服务,定时任务触发密钥信息更新上报,密钥信息数据定时上报更新,调用上报接口发送增量数据,X,接口请求信息,上报数据信息、请求时间戳、签名值,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员在证书管理界面点击数据采集按钮,证书信息上报数据采集,输入证书采集参数,E,证书信息,"证书ID, 证书名称, 证书内容, 证书有效期, 证书状态",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员在证书管理界面点击数据采集按钮,证书信息上报数据采集,验证证书格式有效性,R,证书校验规则,"证书格式规则, 有效期范围规则, 状态枚举值",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员在证书管理界面点击数据采集按钮,证书信息上报数据采集,存储证书信息到数据库,W,证书信息,"证书ID, 证书名称, 证书内容, 证书有效期, 证书状态",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员在证书管理界面点击数据采集按钮,证书信息上报数据采集,返回采集结果,X,操作结果信息,"操作状态, 采集数量, 异常信息",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,输入接口配置信息,E,接口配置信息,"接口URL, 请求方法, 认证方式, 数据格式",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,验证接口参数格式,R,接口校验规则,"URL格式规则, 方法枚举值, 认证方式枚举值",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,建立接口连接测试,X,接口测试结果,"连接状态, 响应时间, 错误代码",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,保存接口配置,W,接口配置信息,"接口URL, 请求方法, 认证方式, 数据格式",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,生成接口调用日志,W,接口日志信息,"调用时间, 操作用户, 接口参数",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员配置集团平台接口参数,证书信息上报接口对接,返回接口配置结果,X,操作结果信息,"配置状态, 提示信息",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员访问证书信息上报列表页面,证书信息上报数据列表展示,输入查询条件,E,查询条件信息,"页码, 单页数量, 证书状态, 证书类型",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员访问证书信息上报列表页面,证书信息上报数据列表展示,读取证书信息列表,R,证书信息,"证书ID, 证书名称, 证书有效期, 证书状态, 上报时间",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：系统管理员，接收者：数据上报服务,系统管理员访问证书信息上报列表页面,证书信息上报数据列表展示,返回证书信息列表,X,证书信息,"证书ID, 证书名称, 证书有效期, 证书状态, 上报时间",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务服务，接收者：数据上报服务,定时任务触发证书信息上报,证书信息数据定时上报更新,读取定时任务配置,R,定时任务配置,"执行频率, 上报时间窗, 最大重试次数",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务服务，接收者：数据上报服务,定时任务触发证书信息上报,证书信息数据定时上报更新,读取待上报证书信息,R,证书信息,"证书ID, 证书名称, 证书内容, 证书有效期, 证书状态",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务服务，接收者：数据上报服务,定时任务触发证书信息上报,证书信息数据定时上报更新,调用集团平台上报接口,X,上报数据,"证书ID, 证书名称, 证书内容, 证书有效期, 证书状态",1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务服务，接收者：数据上报服务,定时任务触发证书信息上报,证书信息数据定时上报更新,记录上报结果,W,上报日志信息,"上报时间, 证书ID, 上报状态, 响应内容",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在密码文档管理界面点击数据采集按钮,密码文档信息上报数据采集,输入密码文档采集条件,E,密码文档采集条件,省份编码、文档类型、采集时间范围,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在密码文档管理界面点击数据采集按钮,密码文档信息上报数据采集,读取密码文档基础信息,R,密码文档信息,文档ID、文档名称、省份编码、文档类型、创建时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在密码文档管理界面点击数据采集按钮,密码文档信息上报数据采集,处理特殊字段编辑请求,W,密码文档信息,文档ID、特殊字段值、修改时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在密码文档管理界面点击数据采集按钮,密码文档信息上报数据采集,返回采集结果状态,X,操作结果信息,操作状态、成功文档数、失败文档数,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,输入集团平台接口参数,E,接口配置信息,接口地址、认证密钥、超时时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,验证接口参数格式,R,接口校验规则,URL格式规则、密钥长度规则,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,执行接口连通性测试,X,接口测试数据,测试文档ID、测试文档名称,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,接收接口测试响应,R,接口响应信息,响应状态码、响应内容,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,保存接口配置信息,W,接口配置信息,接口地址、认证密钥、配置时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统管理员，接收者：数据上报系统,系统管理员在接口配置界面点击对接测试按钮,密码文档信息上报接口对接,返回接口配置结果,X,操作结果信息,操作状态、配置详情,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：审计人员，接收者：数据上报系统,审计人员在上报记录界面点击刷新按钮,密码文档信息上报数据列表展示,输入列表查询条件,E,查询条件信息,页码、单页数量、省份编码,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：审计人员，接收者：数据上报系统,审计人员在上报记录界面点击刷新按钮,密码文档信息上报数据列表展示,读取密码文档上报记录,R,密码文档信息,文档ID、文档名称、上报状态、上报时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：审计人员，接收者：数据上报系统,审计人员在上报记录界面点击刷新按钮,密码文档信息上报数据列表展示,组装分页查询结果,X,密码文档信息,总记录数、当前页数据、分页信息,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统，接收者：数据上报系统,定时任务触发器按配置时间执行,密码文档信息数据定时上报更新,读取定时任务配置,R,定时任务配置,执行频率、生效时间、任务状态,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统，接收者：数据上报系统,定时任务触发器按配置时间执行,密码文档信息数据定时上报更新,读取待更新文档数据,R,密码文档信息,文档ID、文档名称、修改时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统，接收者：数据上报系统,定时任务触发器按配置时间执行,密码文档信息数据定时上报更新,执行数据格式转换,W,密码文档信息,文档ID、标准化字段值,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：系统，接收者：数据上报系统,定时任务触发器按配置时间执行,密码文档信息数据定时上报更新,调用上报接口提交数据,X,密码文档信息,文档ID、文档名称、标准化字段,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：文档管理员，接收者：文件存储服务,文档管理员在上传界面选择文件并点击上传按钮,密码文档文件上传,选择并上传文档文件,E,文件信息,文件名称、文件类型、文件大小,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：文档管理员，接收者：文件存储服务,文档管理员在上传界面选择文件并点击上传按钮,密码文档文件上传,验证文件格式和大小,R,文件校验规则,允许文件类型、最大文件大小,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：文档管理员，接收者：文件存储服务,文档管理员在上传界面选择文件并点击上传按钮,密码文档文件上传,执行文件存储操作,W,文件存储信息,存储路径、文件哈希值、存储时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：文档管理员，接收者：文件存储服务,文档管理员在上传界面选择文件并点击上传按钮,密码文档文件上传,关联文档与文件,W,密码文档信息,文档ID、文件存储路径、关联时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：文档管理员，接收者：文件存储服务,文档管理员在上传界面选择文件并点击上传按钮,密码文档文件上传,返回文件上传结果,X,操作结果信息,操作状态、文件URL,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员在数据采集页面点击数据采集按钮,密码应用测评上报数据采集,输入数据采集条件,E,数据采集条件,采集时间范围、测评状态、业务系统ID,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员在数据采集页面点击数据采集按钮,密码应用测评上报数据采集,读取密码应用测评数据,R,密码应用测评信息,测评ID、业务系统ID、测评结果、测评时间,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员在数据采集页面点击数据采集按钮,密码应用测评上报数据采集,处理测评数据格式,R,数据格式规则,字段映射规则、数据校验规则,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员在数据采集页面点击数据采集按钮,密码应用测评上报数据采集,保存处理后的测评数据,W,密码应用测评信息,测评ID、业务系统ID、标准化测评结果,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,输入接口配置参数,E,接口配置信息,接口地址、认证密钥、数据格式,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,验证接口参数有效性,R,接口校验规则,URL格式规则、密钥长度规则,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,建立与集团平台的连接,R,网络连接信息,IP地址、端口、SSL证书,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,发送测试数据包,X,测试数据包,测试数据内容、发送时间,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,接收接口响应结果,R,接口响应信息,响应状态码、错误信息,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员配置集团平台接口参数,密码应用测评数据上报接口对接,记录接口对接日志,W,系统日志信息,操作时间、操作用户、操作详情,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员访问数据列表页面,密码应用测评上报数据列表展示,输入分页查询条件,E,分页查询条件,页码、单页数量、测评状态,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员访问数据列表页面,密码应用测评上报数据列表展示,读取分页测评数据,R,密码应用测评信息,测评ID、业务系统名称、测评结果、上报时间,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统管理员访问数据列表页面,密码应用测评上报数据列表展示,生成分页展示数据,X,密码应用测评信息,总记录数、当前页数据、分页导航信息,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统定时任务触发,密码应用测评数据定时上报更新,读取定时上报配置,R,定时配置信息,执行周期、最大重试次数,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统定时任务触发,密码应用测评数据定时上报更新,触发定时任务执行,E,任务触发信息,触发时间、任务类型,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统定时任务触发,密码应用测评数据定时上报更新,读取待上报的测评数据,R,密码应用测评信息,测评ID、业务系统ID、更新时间,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统定时任务触发,密码应用测评数据定时上报更新,执行数据上报操作,X,密码应用测评信息,测评ID、标准化数据内容,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统管理员，接收者：数据上报系统,系统定时任务触发,密码应用测评数据定时上报更新,记录上报操作日志,W,系统日志信息,上报时间、上报数据量、操作结果,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：管理员，接收者：密码服务管理平台,管理员在上报配置页面点击数据采集按钮,密码应用漏洞/安全事件上报数据采集,输入漏洞/安全事件基本信息,E,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、影响范围,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：管理员，接收者：密码服务管理平台,管理员在上报配置页面点击数据采集按钮,密码应用漏洞/安全事件上报数据采集,读取关联密码应用信息,R,密码应用信息,应用ID、应用名称、应用类型、部署位置,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：管理员，接收者：密码服务管理平台,管理员在上报配置页面点击数据采集按钮,密码应用漏洞/安全事件上报数据采集,验证数据完整性和格式,R,校验规则信息,必填字段规则、格式校验规则,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：管理员，接收者：密码服务管理平台,管理员在上报配置页面点击数据采集按钮,密码应用漏洞/安全事件上报数据采集,保存漏洞/安全事件数据,W,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、影响范围,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,输入接口配置参数,E,接口配置信息,接口地址、认证方式、数据格式、超时时间,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,验证接口可用性,R,接口测试数据,测试用例、预期响应,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,保存接口配置信息,W,接口配置信息,接口地址、认证方式、数据格式、超时时间,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,生成接口调用凭证,W,安全凭证信息,API密钥、访问令牌、有效期,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,测试接口数据传输,X,测试响应数据,响应状态码、响应内容,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统管理员，接收者：密码服务管理平台,系统检测到集团平台接口变更,密码应用漏洞/安全事件上报接口对接,返回接口对接结果,X,操作结果信息,操作状态、错误代码、日志信息,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：审计人员，接收者：密码服务管理平台,审计人员访问上报数据列表,密码应用漏洞/安全事件上报数据列表展示,输入查询条件,E,查询条件信息,页码、单页数量、事件类型、时间范围,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：审计人员，接收者：密码服务管理平台,审计人员访问上报数据列表,密码应用漏洞/安全事件上报数据列表展示,读取上报事件数据,R,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、上报状态,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：审计人员，接收者：密码服务管理平台,审计人员访问上报数据列表,密码应用漏洞/安全事件上报数据列表展示,展示分页列表数据,X,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、上报状态,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统，接收者：密码服务管理平台,系统定时任务触发,密码应用漏洞/安全事件定时上报更新,读取上报配置信息,R,上报配置信息,上报频率、目标地址、数据格式,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统，接收者：密码服务管理平台,系统定时任务触发,密码应用漏洞/安全事件定时上报更新,读取待上报事件数据,R,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、上报状态,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统，接收者：密码服务管理平台,系统定时任务触发,密码应用漏洞/安全事件定时上报更新,转换数据格式,R,数据转换规则,字段映射规则、格式转换规则,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：系统，接收者：密码服务管理平台,系统定时任务触发,密码应用漏洞/安全事件定时上报更新,发送上报数据,X,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、上报状态,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：安全分析师，接收者：密码服务管理平台,安全分析师发现非平台监控事件,密码应用漏洞/安全事件补充,输入补充事件信息,E,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、影响范围,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：安全分析师，接收者：密码服务管理平台,安全分析师发现非平台监控事件,密码应用漏洞/安全事件补充,验证事件唯一性,R,事件校验规则,去重规则、关联规则,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：安全分析师，接收者：密码服务管理平台,安全分析师发现非平台监控事件,密码应用漏洞/安全事件补充,保存补充事件数据,W,安全漏洞事件信息,事件ID、事件名称、事件类型、发生时间、影响范围,1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：安全分析师，接收者：密码服务管理平台,安全分析师发现非平台监控事件,密码应用漏洞/安全事件补充,返回补充结果,X,操作结果信息,操作状态、事件ID、提示信息,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞上报页面点击新增按钮,创建漏洞事件上报,输入漏洞事件基本信息,E,安全漏洞事件信息,事件ID、事件名称、漏洞描述、发现时间、影响范围、上报单位、上报人,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞上报页面点击新增按钮,创建漏洞事件上报,校验漏洞事件格式规则,R,数据校验规则,事件名称长度限制、时间格式规则、必填字段校验,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞上报页面点击新增按钮,创建漏洞事件上报,关联密码应用信息,R,密码应用信息,应用ID、应用名称、应用类型、所属单位,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞上报页面点击新增按钮,创建漏洞事件上报,保存漏洞事件数据,W,安全漏洞事件信息,事件ID、事件名称、漏洞描述、发现时间、影响范围、上报单位、上报人、关联应用ID,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞上报页面点击新增按钮,创建漏洞事件上报,返回创建结果,X,操作结果信息,操作状态、事件ID、提示信息,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击查询按钮,查询漏洞事件列表,输入查询条件,E,查询条件信息,页码、单页数量、事件名称、上报时间范围、事件状态、上报单位,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击查询按钮,查询漏洞事件列表,读取漏洞事件数据,R,安全漏洞事件信息,事件ID、事件名称、漏洞描述、发现时间、影响范围、上报单位、上报人、事件状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击查询按钮,查询漏洞事件列表,返回分页查询结果,X,安全漏洞事件信息,页码、总记录数、事件列表（事件ID、事件名称、漏洞描述、发现时间、影响范围、上报单位、上报人、事件状态）,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞详情页面点击修改按钮,更新漏洞事件状态,选择目标事件,E,安全漏洞事件信息,事件ID、当前事件状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞详情页面点击修改按钮,更新漏洞事件状态,输入更新状态信息,E,事件状态信息,目标状态、处理意见,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞详情页面点击修改按钮,更新漏洞事件状态,更新事件状态记录,W,安全漏洞事件信息,事件ID、事件状态、处理意见、更新时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞详情页面点击修改按钮,更新漏洞事件状态,返回更新结果,X,操作结果信息,操作状态、事件ID、提示信息,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击导出按钮,导出漏洞事件数据,输入导出条件,E,导出条件信息,事件名称、上报时间范围、事件状态、上报单位,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击导出按钮,导出漏洞事件数据,读取导出数据,R,安全漏洞事件信息,事件ID、事件名称、漏洞描述、发现时间、影响范围、上报单位、上报人、事件状态、处理意见,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞列表页面点击导出按钮,导出漏洞事件数据,生成导出文件,X,导出文件信息,文件格式、文件大小、下载链接,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞审核页面点击审核按钮,审核漏洞事件,选择待审核事件,E,安全漏洞事件信息,事件ID、当前审核状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞审核页面点击审核按钮,审核漏洞事件,输入审核意见,E,审核信息,审核状态、审核意见、审核人,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞审核页面点击审核按钮,审核漏洞事件,更新审核记录,W,安全漏洞事件信息,事件ID、审核状态、审核意见、审核人、审核时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台-漏洞上报模块,管理员在漏洞审核页面点击审核按钮,审核漏洞事件,返回审核结果,X,操作结果信息,操作状态、事件ID、提示信息,1
