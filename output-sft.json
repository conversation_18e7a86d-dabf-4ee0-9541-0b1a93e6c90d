[{"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台HTTPS对接", "功能过程": "总部平台上报路径配置", "功能描述": "支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在上级平台管理页面点击上级平台地址配置按钮", "子过程": [{"子过程描述": "输入上级平台配置信息", "数据移动类型": "E", "数据组": "上报路径信息", "数据属性": "IP、端口、协议", "CFP": 1}, {"子过程描述": "验证配置信息格式", "数据移动类型": "R", "数据组": "配置规则信息", "数据属性": "IP格式规则、端口范围规则", "CFP": 1}, {"子过程描述": "保存上级平台配置", "数据移动类型": "W", "数据组": "上报路径信息", "数据属性": "IP、端口、协议", "CFP": 1}, {"子过程描述": "返回配置结果", "数据移动类型": "X", "数据组": "操作结果信息", "数据属性": "操作状态、提示信息", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台HTTPS对接", "功能过程": "总部平台上报路径查看", "功能描述": "支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击上级平台配置菜单", "子过程": [{"子过程描述": "读取上级平台配置信息", "数据移动类型": "R", "数据组": "上报路径信息", "数据属性": "IP、端口、协议", "CFP": 1}, {"子过程描述": "展示平台配置列表", "数据移动类型": "X", "数据组": "上报路径信息", "数据属性": "IP、端口、协议", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台HTTPS对接", "功能过程": "总部平台HTTPS通道对接", "功能描述": "支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击HTTPS通道对接", "子过程": [{"子过程描述": "发起HTTPS请求", "数据移动类型": "E", "数据组": "HTTPS请求信息", "数据属性": "HTTPS请求", "CFP": 1}, {"子过程描述": "HTTPS通道对接", "数据移动类型": "R", "数据组": "HTTPS通道信息", "数据属性": "TLS协议、加密套件", "CFP": 1}, {"子过程描述": "返回HTTPS响应", "数据移动类型": "X", "数据组": "HTTPS响应信息", "数据属性": "HTTPS响应", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台AKSK认证对接", "功能过程": "总部平台访问凭证配置", "功能描述": "通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在总部平台访问凭证配置页面点击总部平台访问凭证配置按钮", "子过程": [{"子过程描述": "输入总部平台访问凭证配置信息", "数据移动类型": "E", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "保存总部平台访问凭证配置信息", "数据移动类型": "W", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "返回展示总部平台访问凭证配置内容", "数据移动类型": "X", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}], "功能点个数": 3}, {"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台AKSK认证对接", "功能过程": "访问凭证查看", "功能描述": "通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在总部平台访问凭证列表页面点击总部平台访问凭证查询按钮", "子过程": [{"子过程描述": "输入总部平台访问凭证查询条件", "数据移动类型": "E", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "返回展示总部平台访问凭证查询结果", "数据移动类型": "X", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}], "功能点个数": 2}, {"一级功能模块": "系统管理", "二级功能模块": "总部一级平台对接", "三级功能模块": "总部平台AKSK认证对接", "功能过程": "AKSK认证", "功能描述": "通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮", "子过程": [{"子过程描述": "获取总部平台访问凭证", "数据移动类型": "R", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "生成总部平台访问凭证密钥", "数据移动类型": "R", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "总部平台访问凭证加密", "数据移动类型": "R", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "总部平台访问凭证认证", "数据移动类型": "X", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}, {"子过程描述": "总部平台访问凭证认证结果展示", "数据移动类型": "X", "数据组": "总部平台访问凭证", "数据属性": "总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量", "CFP": 1}], "功能点个数": 5}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户注册审核", "功能过程": "用户注册信息列表查询", "功能描述": "审核通过/拒绝、所属角色、审批意见", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：用户注册审核模块", "触发事件": "用户点击用户注册信息列表查询按钮", "子过程": [{"子过程描述": "输入用户注册信息列表查询条件", "数据移动类型": "E", "数据组": "用户注册信息列表查询请求", "数据属性": "用户注册信息列表查询条件、用户注册信息列表查询项", "CFP": 1}, {"子过程描述": "获取用户注册信息列表", "数据移动类型": "R", "数据组": "用户注册信息列表", "数据属性": "用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、分页数、用户注册信息列表数量", "CFP": 1}, {"子过程描述": "返回用户注册信息列表查询结果展示", "数据移动类型": "X", "数据组": "用户注册信息列表查询结果", "数据属性": "用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、分页数、用户注册信息列表数量", "CFP": 1}, {"子过程描述": "保存用户注册信息列表查询记录", "数据移动类型": "W", "数据组": "用户注册信息列表查询记录", "数据属性": "用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户注册审核", "功能过程": "注册用户信息", "功能描述": "审核通过/拒绝、所属角色、审批意见", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：用户注册审核模块", "触发事件": "用户点击注册用户信息", "子过程": [{"子过程描述": "输入注册用户信息", "数据移动类型": "E", "数据组": "注册用户信息", "数据属性": "注册用户信息ID、注册用户信息名称", "CFP": 1}, {"子过程描述": "注册用户信息重复性校验", "数据移动类型": "R", "数据组": "注册用户信息", "数据属性": "注册用户信息名称、主键、非空校验、判重、注册用户信息类型", "CFP": 1}, {"子过程描述": "注册用户信息保存", "数据移动类型": "W", "数据组": "注册用户信息", "数据属性": "注册用户信息名称、注册用户信息类型、注册用户信息ID、注册用户信息数量", "CFP": 1}, {"子过程描述": "返回展示注册用户信息", "数据移动类型": "X", "数据组": "注册用户信息", "数据属性": "注册用户信息名称、注册用户信息类型、注册用户信息ID、注册用户信息数量", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户注册审核", "功能过程": "编辑用户信息", "功能描述": "审核通过/拒绝、所属角色、审批意见", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：用户注册审核模块", "触发事件": "用户点击编辑用户信息", "子过程": [{"子过程描述": "输入编辑用户信息", "数据移动类型": "E", "数据组": "编辑用户信息", "数据属性": "编辑用户信息ID、编辑用户信息名称", "CFP": 1}, {"子过程描述": "获取编辑用户信息", "数据移动类型": "R", "数据组": "编辑用户信息", "数据属性": "编辑用户信息名称、编辑用户信息类型、编辑用户信息ID", "CFP": 1}, {"子过程描述": "编辑用户信息保存", "数据移动类型": "W", "数据组": "编辑用户信息", "数据属性": "编辑用户信息名称、编辑用户信息类型、编辑用户信息ID", "CFP": 1}, {"子过程描述": "返回展示编辑用户信息", "数据移动类型": "X", "数据组": "编辑用户信息", "数据属性": "编辑用户信息名称、编辑用户信息类型、编辑用户信息ID", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户注册审核", "功能过程": "删除注册记录", "功能描述": "审核通过/拒绝、所属角色、审批意见", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：用户注册审核模块", "触发事件": "用户点击删除注册记录", "子过程": [{"子过程描述": "发起删除注册记录请求", "数据移动类型": "E", "数据组": "删除注册记录请求", "数据属性": "删除注册记录指令", "CFP": 1}, {"子过程描述": "获取删除注册记录", "数据移动类型": "R", "数据组": "删除注册记录", "数据属性": "删除注册记录名称、删除注册记录类型、删除注册记录ID", "CFP": 1}, {"子过程描述": "删除注册记录", "数据移动类型": "W", "数据组": "删除注册记录", "数据属性": "删除注册记录名称、删除注册记录类型、删除注册记录ID", "CFP": 1}, {"子过程描述": "返回展示删除注册记录结果", "数据移动类型": "X", "数据组": "删除注册记录结果", "数据属性": "删除注册记录名称、删除注册记录类型、删除注册记录ID、删除结果", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户注册审核", "功能过程": "用户注册审核", "功能描述": "审核通过/拒绝、所属角色、审批意见", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：用户注册审核模块", "触发事件": "用户点击用户注册审核", "子过程": [{"子过程描述": "发起用户注册审核请求", "数据移动类型": "E", "数据组": "用户注册审核请求", "数据属性": "用户注册审核请求参数", "CFP": 1}, {"子过程描述": "获取用户注册审核信息", "数据移动类型": "R", "数据组": "用户注册审核信息", "数据属性": "用户注册审核名称、用户注册审核类型、用户注册审核ID", "CFP": 1}, {"子过程描述": "用户注册审核结果数据生成", "数据移动类型": "W", "数据组": "用户注册审核生成信息", "数据属性": "用户注册审核名称、用户注册审核类型、用户注册审核ID、生成时间", "CFP": 1}, {"子过程描述": "返回展示用户注册审核结果", "数据移动类型": "X", "数据组": "用户注册审核结果", "数据属性": "用户注册审核名称、用户注册审核类型、用户注册审核ID、分页数", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "用户信息列表查询", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击用户信息列表", "子过程": [{"子过程描述": "输入用户信息列表查询条件", "数据移动类型": "E", "数据组": "用户信息列表查询请求", "数据属性": "页码、单页数量、用户ID、用户名称、用户类型", "CFP": 1}, {"子过程描述": "获取用户信息列表", "数据移动类型": "R", "数据组": "用户信息列表", "数据属性": "用户ID、用户名称、用户类型、用户状态、用户权限、创建时间", "CFP": 1}, {"子过程描述": "返回展示用户信息列表", "数据移动类型": "X", "数据组": "用户信息列表查询结果", "数据属性": "页码、单页数量、用户ID、用户名称、用户类型、用户状态、用户权限、创建时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "启用用户", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击启用按钮", "子过程": [{"子过程描述": "发起启用用户请求", "数据移动类型": "E", "数据组": "用户ID", "数据属性": "用户ID", "CFP": 1}, {"子过程描述": "更新用户状态为启用", "数据移动类型": "W", "数据组": "用户信息", "数据属性": "用户ID、用户名称、用户类型、用户状态", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "禁用用户", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击禁用按钮", "子过程": [{"子过程描述": "发起禁用用户请求", "数据移动类型": "E", "数据组": "用户ID", "数据属性": "用户ID", "CFP": 1}, {"子过程描述": "更新用户状态为禁用", "数据移动类型": "W", "数据组": "用户信息", "数据属性": "用户ID、用户名称、用户类型、用户状态", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "重置用户密码", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击重置密码按钮", "子过程": [{"子过程描述": "发起重置用户密码请求", "数据移动类型": "E", "数据组": "用户ID", "数据属性": "用户ID", "CFP": 1}, {"子过程描述": "更新用户密码", "数据移动类型": "W", "数据组": "用户信息", "数据属性": "用户ID、用户名称、用户类型、用户状态、密码", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "解锁用户锁定", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击解锁按钮", "子过程": [{"子过程描述": "发起解锁用户请求", "数据移动类型": "E", "数据组": "用户ID", "数据属性": "用户ID", "CFP": 1}, {"子过程描述": "更新用户锁定状态", "数据移动类型": "W", "数据组": "用户信息", "数据属性": "用户ID、用户名称、用户类型、用户状态", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "设置用户的口令有效期", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击设置用户口令有效期按钮", "子过程": [{"子过程描述": "输入用户口令有效期", "数据移动类型": "E", "数据组": "用户口令有效期", "数据属性": "用户口令有效期", "CFP": 1}, {"子过程描述": "获取用户口令有效期", "数据移动类型": "R", "数据组": "用户口令有效期", "数据属性": "用户口令有效期", "CFP": 1}, {"子过程描述": "保存用户口令有效期", "数据移动类型": "W", "数据组": "用户口令有效期", "数据属性": "用户口令有效期", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "用户认证管理", "三级功能模块": "用户信息管理", "功能过程": "删除用户", "功能描述": "删除用户", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户信息管理模块", "触发事件": "用户点击删除按钮", "子过程": [{"子过程描述": "发起删除用户请求", "数据移动类型": "E", "数据组": "用户ID", "数据属性": "用户ID", "CFP": 1}, {"子过程描述": "删除用户", "数据移动类型": "W", "数据组": "用户信息", "数据属性": "用户ID、用户名称、用户类型", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "口令登录", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户输入口令登录", "子过程": [{"子过程描述": "输入口令", "数据移动类型": "E", "数据组": "口令", "数据属性": "口令", "CFP": 1}, {"子过程描述": "验证口令", "数据移动类型": "R", "数据组": "口令", "数据属性": "口令", "CFP": 1}, {"子过程描述": "口令登录", "数据移动类型": "X", "数据组": "口令", "数据属性": "口令", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "Ukey登录", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户使用Ukey登录", "子过程": [{"子过程描述": "Ukey插入", "数据移动类型": "E", "数据组": "<PERSON><PERSON>", "数据属性": "<PERSON><PERSON>", "CFP": 1}, {"子过程描述": "Ukey验证", "数据移动类型": "R", "数据组": "<PERSON><PERSON>", "数据属性": "<PERSON><PERSON>", "CFP": 1}, {"子过程描述": "Ukey登录", "数据移动类型": "X", "数据组": "<PERSON><PERSON>", "数据属性": "<PERSON><PERSON>", "CFP": 1}, {"子过程描述": "U<PERSON>口令验证", "数据移动类型": "R", "数据组": "口令", "数据属性": "口令", "CFP": 1}, {"子过程描述": "Ukey登录结果", "数据移动类型": "X", "数据组": "口令", "数据属性": "口令", "CFP": 1}], "预估工作量": 5}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "口令黑名单列表查询", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户点击口令黑名单列表", "子过程": [{"子过程描述": "输入口令黑名单列表查询条件", "数据移动类型": "E", "数据组": "口令黑名单列表查询请求", "数据属性": "口令黑名单列表查询条件、口令黑名单列表查询项", "CFP": 1}, {"子过程描述": "获取口令黑名单列表", "数据移动类型": "R", "数据组": "口令黑名单列表", "数据属性": "口令黑名单列表名称、口令黑名单列表类型、口令黑名单列表ID", "CFP": 1}, {"子过程描述": "返回展示口令黑名单列表查询结果", "数据移动类型": "X", "数据组": "口令黑名单列表查询结果", "数据属性": "口令黑名单列表名称、口令黑名单列表类型、口令黑名单列表ID、查询时间", "CFP": 1}], "预估工作量": 2}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "新建口令黑名单", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户新建口令黑名单", "子过程": [{"子过程描述": "输入新建口令黑名单信息", "数据移动类型": "E", "数据组": "口令黑名单新建信息", "数据属性": "口令黑名单新建ID、口令黑名单新建名称", "CFP": 1}, {"子过程描述": "口令黑名单重复性校验", "数据移动类型": "R", "数据组": "口令黑名单校验信息", "数据属性": "口令黑名单名称、主键、非空校验、判重、口令黑名单类型", "CFP": 1}, {"子过程描述": "口令黑名单新建保存", "数据移动类型": "W", "数据组": "口令黑名单新建结果", "数据属性": "口令黑名单名称、口令黑名单类型、口令黑名单ID、新建结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "编辑口令黑名单", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户编辑口令黑名单", "子过程": [{"子过程描述": "输入编辑口令黑名单信息", "数据移动类型": "E", "数据组": "口令黑名单编辑条件", "数据属性": "口令黑名单ID", "CFP": 1}, {"子过程描述": "口令黑名单编辑保存", "数据移动类型": "W", "数据组": "口令黑名单编辑结果", "数据属性": "编辑时间、口令黑名单名称、口令黑名单类型、口令黑名单ID", "CFP": 1}], "预估工作量": 2}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令管理", "功能过程": "删除口令黑名单", "功能描述": "删除用户登录口令黑名单", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令管理模块", "触发事件": "用户删除口令黑名单", "子过程": [{"子过程描述": "发起口令黑名单删除请求", "数据移动类型": "E", "数据组": "口令黑名单删除请求", "数据属性": "口令黑名单删除参数", "CFP": 1}, {"子过程描述": "口令黑名单删除保存", "数据移动类型": "W", "数据组": "口令黑名单删除结果", "数据属性": "口令黑名单名称、口令黑名单类型、口令黑名单ID、删除结果", "CFP": 1}], "预估工作量": 2}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "智能密码钥匙列表", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击智能密码钥匙列表", "子过程": [{"子过程描述": "输入智能密码钥匙列表查询条件", "数据移动类型": "E", "数据组": "智能密码钥匙列表查询请求", "数据属性": "智能密码钥匙列表查询条件、智能密码钥匙列表查询项", "CFP": 1}, {"子过程描述": "读取智能密码钥匙列表", "数据移动类型": "R", "数据组": "智能密码钥匙列表", "数据属性": "智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID", "CFP": 1}, {"子过程描述": "返回智能密码钥匙列表查询结果展示", "数据移动类型": "X", "数据组": "智能密码钥匙列表查询结果", "数据属性": "智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存智能密码钥匙列表查询记录", "数据移动类型": "W", "数据组": "智能密码钥匙列表查询记录", "数据属性": "智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "智能密码钥匙新增", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击智能密码钥匙新增", "子过程": [{"子过程描述": "输入智能密码钥匙新增信息", "数据移动类型": "E", "数据组": "智能密码钥匙新增信息", "数据属性": "智能密码钥匙新增ID、智能密码钥匙新增名称", "CFP": 1}, {"子过程描述": "智能密码钥匙重复性校验", "数据移动类型": "R", "数据组": "智能密码钥匙校验信息", "数据属性": "智能密码钥匙名称、主键、非空校验、判重、智能密码钥匙类型", "CFP": 1}, {"子过程描述": "智能密码钥匙新增入库", "数据移动类型": "W", "数据组": "智能密码钥匙新增内容", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示智能密码钥匙新增内容", "数据移动类型": "X", "数据组": "智能密码钥匙新增内容", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、新增结果", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "智能密码钥匙启用", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击智能密码钥匙启用", "子过程": [{"子过程描述": "发起智能密码钥匙启用请求", "数据移动类型": "E", "数据组": "智能密码钥匙启用请求", "数据属性": "智能密码钥匙启用指令", "CFP": 1}, {"子过程描述": "获取智能密码钥匙", "数据移动类型": "R", "数据组": "智能密码钥匙", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID", "CFP": 1}, {"子过程描述": "智能密码钥匙启用保存", "数据移动类型": "W", "数据组": "智能密码钥匙启用结果", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、启用结果", "CFP": 1}, {"子过程描述": "返回展示智能密码钥匙启用结果", "数据移动类型": "X", "数据组": "智能密码钥匙启用结果展示信息", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、启用内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "智能密码钥匙禁用", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击智能密码钥匙禁用", "子过程": [{"子过程描述": "发起智能密码钥匙禁用请求", "数据移动类型": "E", "数据组": "智能密码钥匙禁用请求", "数据属性": "智能密码钥匙禁用指令", "CFP": 1}, {"子过程描述": "获取智能密码钥匙", "数据移动类型": "R", "数据组": "智能密码钥匙", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID", "CFP": 1}, {"子过程描述": "智能密码钥匙禁用保存", "数据移动类型": "W", "数据组": "智能密码钥匙禁用结果", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、禁用结果", "CFP": 1}, {"子过程描述": "返回展示智能密码钥匙禁用结果", "数据移动类型": "X", "数据组": "智能密码钥匙禁用结果展示信息", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、禁用内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "智能密码钥匙删除", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击智能密码钥匙删除", "子过程": [{"子过程描述": "发起智能密码钥匙删除请求", "数据移动类型": "E", "数据组": "智能密码钥匙删除请求", "数据属性": "智能密码钥匙删除指令", "CFP": 1}, {"子过程描述": "获取操作员权限并判断智能密码钥匙是否可删除", "数据移动类型": "R", "数据组": "智能密码钥匙删除权限", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、操作员权限", "CFP": 1}, {"子过程描述": "智能密码钥匙删除保存", "数据移动类型": "W", "数据组": "智能密码钥匙删除结果", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示智能密码钥匙删除内容", "数据移动类型": "X", "数据组": "智能密码钥匙删除内容", "数据属性": "智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、删除内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "是否开启口令登录", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击是否开启口令登录", "子过程": [{"子过程描述": "发起是否开启口令登录请求", "数据移动类型": "E", "数据组": "是否开启口令登录请求", "数据属性": "是否开启口令登录指令", "CFP": 1}, {"子过程描述": "获取是否开启口令登录", "数据移动类型": "R", "数据组": "是否开启口令登录", "数据属性": "是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID", "CFP": 1}, {"子过程描述": "是否开启口令登录保存", "数据移动类型": "W", "数据组": "是否开启口令登录结果", "数据属性": "是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID、保存内容", "CFP": 1}, {"子过程描述": "返回展示是否开启口令登录内容", "数据移动类型": "X", "数据组": "是否开启口令登录内容", "数据属性": "是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID、展示内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户ukey策略管理", "功能过程": "是否开启UKey登录", "功能描述": "是否开启UKey登录，允许用户使用Ukey进行登录", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击是否开启UKey登录", "子过程": [{"子过程描述": "发起是否开启UKey登录请求", "数据移动类型": "E", "数据组": "是否开启UKey登录请求", "数据属性": "是否开启UKey登录指令", "CFP": 1}, {"子过程描述": "获取是否开启UKey登录", "数据移动类型": "R", "数据组": "是否开启UKey登录", "数据属性": "是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID", "CFP": 1}, {"子过程描述": "是否开启UKey登录保存", "数据移动类型": "W", "数据组": "是否开启UKey登录结果", "数据属性": "是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID、保存内容", "CFP": 1}, {"子过程描述": "返回展示是否开启UKey登录内容", "数据移动类型": "X", "数据组": "是否开启UKey登录内容", "数据属性": "是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID、展示内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "设置用户默认口令", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击设置用户默认口令按钮", "子过程": [{"子过程描述": "输入用户默认口令", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "用户默认口令、用户默认口令类型", "CFP": 1}, {"子过程描述": "保存用户默认口令", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "用户默认口令、用户默认口令类型、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "历史口令限制次数", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击历史口令限制次数按钮", "子过程": [{"子过程描述": "输入历史口令限制次数", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "历史口令限制次数", "CFP": 1}, {"子过程描述": "保存历史口令限制次数", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "历史口令限制次数、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "长时间未登录禁用账户天数", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击长时间未登录禁用账户天数按钮", "子过程": [{"子过程描述": "输入长时间未登录禁用账户天数", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "长时间未登录禁用账户天数", "CFP": 1}, {"子过程描述": "保存长时间未登录禁用账户天数", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "长时间未登录禁用账户天数、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "口令有效期天数", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击口令有效期天数按钮", "子过程": [{"子过程描述": "输入口令有效期天数", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "口令有效期天数", "CFP": 1}, {"子过程描述": "保存口令有效期天数", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "口令有效期天数、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "口令有效期告警天数", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击口令有效期告警天数按钮", "子过程": [{"子过程描述": "输入口令有效期告警天数", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "口令有效期告警天数", "CFP": 1}, {"子过程描述": "保存口令有效期告警天数", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "口令有效期告警天数、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "登录失败次数限制次数", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击登录失败次数限制次数按钮", "子过程": [{"子过程描述": "输入登录失败次数限制次数", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "登录失败次数限制次数", "CFP": 1}, {"子过程描述": "保存登录失败次数限制次数", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "登录失败次数限制次数、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "登录失败锁定时长(分钟)", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击登录失败锁定时长(分钟)按钮", "子过程": [{"子过程描述": "输入登录失败锁定时长", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "登录失败锁定时长", "CFP": 1}, {"子过程描述": "保存登录失败锁定时长", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "登录失败锁定时长、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "访问控制管理", "三级功能模块": "用户口令策略管理", "功能过程": "是否强制修改默认口令", "功能描述": "是否强制修改默认口令", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：用户口令策略模块", "触发事件": "用户在用户口令策略页面点击是否强制修改默认口令按钮", "子过程": [{"子过程描述": "输入是否强制修改默认口令", "数据移动类型": "E", "数据组": "用户口令策略", "数据属性": "是否强制修改默认口令", "CFP": 1}, {"子过程描述": "保存是否强制修改默认口令", "数据移动类型": "W", "数据组": "用户口令策略", "数据属性": "是否强制修改默认口令、保存时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "上报周期管理", "三级功能模块": "上报周期及频率管理", "功能过程": "上报内容列表", "功能描述": "提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击上报内容列表", "子过程": [{"子过程描述": "输入查询条件", "数据移动类型": "E", "数据组": "查询条件信息", "数据属性": "页码、单页数量、上报内容名称", "CFP": 1}, {"子过程描述": "读取上报内容列表", "数据移动类型": "R", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、上报内容类型、上报内容数量", "CFP": 1}, {"子过程描述": "返回展示上报内容列表", "数据移动类型": "X", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、上报内容类型、上报内容数量", "CFP": 1}, {"子过程描述": "保存上报内容列表", "数据移动类型": "W", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、上报内容类型、上报内容数量", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "上报周期管理", "三级功能模块": "上报周期及频率管理", "功能过程": "上报内容配置", "功能描述": "提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击上报内容配置", "子过程": [{"子过程描述": "输入上报内容配置信息", "数据移动类型": "E", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、是否开启", "CFP": 1}, {"子过程描述": "读取上报内容配置信息", "数据移动类型": "R", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、是否开启", "CFP": 1}, {"子过程描述": "上报内容配置保存", "数据移动类型": "W", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、是否开启", "CFP": 1}, {"子过程描述": "返回展示上报内容配置结果", "数据移动类型": "X", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、是否开启", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "上报周期管理", "三级功能模块": "上报周期及频率管理", "功能过程": "上报频率配置", "功能描述": "提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击上报频率配置", "子过程": [{"子过程描述": "输入上报频率配置信息", "数据移动类型": "E", "数据组": "上报频率信息", "数据属性": "上报频率ID、上报频率名称、上报频率类型", "CFP": 1}, {"子过程描述": "获取上报频率字典", "数据移动类型": "R", "数据组": "上报频率信息", "数据属性": "上报频率ID、上报频率名称、上报频率类型", "CFP": 1}, {"子过程描述": "上报频率配置保存", "数据移动类型": "W", "数据组": "上报频率信息", "数据属性": "上报频率ID、上报频率名称、上报频率类型", "CFP": 1}, {"子过程描述": "上报内容更新", "数据移动类型": "R", "数据组": "上报内容信息", "数据属性": "上报内容ID、上报内容名称、上报频率", "CFP": 1}, {"子过程描述": "上报频率配置更新", "数据移动类型": "W", "数据组": "上报频率信息", "数据属性": "上报频率ID、上报内容名称、上报频率", "CFP": 1}, {"子过程描述": "返回展示上报频率配置结果", "数据移动类型": "X", "数据组": "操作结果信息", "数据属性": "操作结果", "CFP": 1}, {"子过程描述": "上报频率配置归档", "数据移动类型": "W", "数据组": "上报频率信息", "数据属性": "上报频率ID、上报频率名称、归档时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "登录日志管理", "功能过程": "查询登录日志", "功能描述": "导出登录日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-日志管理模块", "触发事件": "用户在登录日志管理页面点击查询按钮", "子过程": [{"子过程描述": "输入登录日志查询条件", "数据移动类型": "E", "数据组": "登录日志查询请求", "数据属性": "页码、单页数量、登录日志ID、登录时间、登录结果、登录人", "CFP": 1}, {"子过程描述": "获取登录日志", "数据移动类型": "R", "数据组": "登录日志信息", "数据属性": "登录日志ID、登录时间、登录结果、登录人", "CFP": 1}, {"子过程描述": "返回登录日志查询结果展示", "数据移动类型": "X", "数据组": "登录日志查询结果", "数据属性": "登录日志ID、登录时间、登录结果、登录人、分页数", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "登录日志管理", "功能过程": "批量审计", "功能描述": "导出登录日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-日志管理模块", "触发事件": "用户在登录日志管理页面点击批量审计按钮", "子过程": [{"子过程描述": "发起登录日志批量审计请求", "数据移动类型": "E", "数据组": "登录日志批量审计请求", "数据属性": "登录日志批量审计参数", "CFP": 1}, {"子过程描述": "获取需要批量审计的登录日志", "数据移动类型": "R", "数据组": "登录日志信息", "数据属性": "登录日志ID、登录时间、登录结果、登录人", "CFP": 1}, {"子过程描述": "登录日志批量审计保存", "数据移动类型": "W", "数据组": "登录日志批量审计结果", "数据属性": "登录日志ID、登录时间、登录结果、登录人、审计结果", "CFP": 1}, {"子过程描述": "返回展示登录日志批量审计结果", "数据移动类型": "X", "数据组": "登录日志批量审计内容", "数据属性": "登录日志ID、登录时间、登录结果、登录人、审计结果", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "登录日志管理", "功能过程": "日志导出", "功能描述": "导出登录日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-日志管理模块", "触发事件": "用户在登录日志管理页面点击导出按钮", "子过程": [{"子过程描述": "发起登录日志导出请求", "数据移动类型": "E", "数据组": "登录日志导出请求", "数据属性": "登录日志导出参数", "CFP": 1}, {"子过程描述": "获取登录日志", "数据移动类型": "R", "数据组": "登录日志信息", "数据属性": "登录日志ID、登录时间、登录结果、登录人", "CFP": 1}, {"子过程描述": "登录日志导出", "数据移动类型": "X", "数据组": "登录日志导出内容", "数据属性": "登录日志ID、登录时间、登录结果、登录人、文件格式", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "操作日志管理", "功能过程": "操作日志查询", "功能描述": "导出操作日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-操作日志管理模块", "触发事件": "用户在操作日志管理页面点击操作日志查询按钮", "子过程": [{"子过程描述": "输入操作日志查询条件", "数据移动类型": "E", "数据组": "操作日志查询请求", "数据属性": "操作日志查询条件、操作日志查询项", "CFP": 1}, {"子过程描述": "读取操作日志", "数据移动类型": "R", "数据组": "操作日志", "数据属性": "操作日志名称、操作日志类型、操作日志ID、操作人、操作时间", "CFP": 1}, {"子过程描述": "返回操作日志查询结果展示", "数据移动类型": "X", "数据组": "操作日志查询结果", "数据属性": "操作日志名称、操作日志类型、操作日志ID、分页数、操作人、操作时间", "CFP": 1}, {"子过程描述": "保存操作日志查询记录", "数据移动类型": "W", "数据组": "操作日志查询记录", "数据属性": "操作日志名称、操作日志类型、操作日志ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "操作日志管理", "功能过程": "批量审批", "功能描述": "导出操作日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-操作日志管理模块", "触发事件": "用户在操作日志管理页面点击批量审批按钮", "子过程": [{"子过程描述": "发起批量审批请求", "数据移动类型": "E", "数据组": "批量审批请求", "数据属性": "批量审批参数", "CFP": 1}, {"子过程描述": "获取批量审批信息", "数据移动类型": "R", "数据组": "批量审批信息", "数据属性": "批量审批数量、批量审批ID", "CFP": 1}, {"子过程描述": "批量审批信息保存", "数据移动类型": "W", "数据组": "批量审批结果", "数据属性": "批量审批结果、批量审批时间、批量审批操作员", "CFP": 1}, {"子过程描述": "返回展示批量审批结果", "数据移动类型": "X", "数据组": "批量审批结果展示信息", "数据属性": "批量审批结果、批量审批内容", "CFP": 1}]}, {"一级功能模块": "系统管理", "二级功能模块": "日志管理/统计分析", "三级功能模块": "操作日志管理", "功能过程": "日志导出", "功能描述": "导出操作日志", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-操作日志管理模块", "触发事件": "用户在操作日志管理页面点击日志导出按钮", "子过程": [{"子过程描述": "输入日志导出参数", "数据移动类型": "E", "数据组": "日志导出请求", "数据属性": "日志导出参数", "CFP": 1}, {"子过程描述": "读取日志", "数据移动类型": "R", "数据组": "日志", "数据属性": "日志名称、日志类型、日志ID", "CFP": 1}, {"子过程描述": "日志导出", "数据移动类型": "X", "数据组": "日志导出内容", "数据属性": "日志名称、日志类型、日志ID、导出时间", "CFP": 1}, {"子过程描述": "日志导出数据归档", "数据移动类型": "W", "数据组": "日志导出记录", "数据属性": "日志名称、日志类型、日志ID、归档时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "密码应用类型管理", "功能过程": "密码应用类型分页列表查询", "功能描述": "应用类型下无对应用时，允许删除密码应用类型", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击分页查询按钮", "子过程": [{"子过程描述": "输入分页查询条件", "数据移动类型": "E", "数据组": "分页查询条件", "数据属性": "分页数、分页大小", "CFP": 1}, {"子过程描述": "读取密码应用类型分页列表", "数据移动类型": "R", "数据组": "密码应用类型信息", "数据属性": "类型编码、类型名称、备注、创建时间", "CFP": 1}, {"子过程描述": "返回密码应用类型分页列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用类型分页列表", "数据属性": "类型编码、类型名称、备注、创建时间、分页数、分页大小", "CFP": 1}, {"子过程描述": "保存密码应用类型分页列表查询记录", "数据移动类型": "W", "数据组": "密码应用类型分页列表", "数据属性": "类型编码、类型名称、备注、创建时间、分页数、分页大小", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "密码应用类型管理", "功能过程": "密码应用类型过滤查询", "功能描述": "应用类型下无对应用时，允许删除密码应用类型", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击过滤查询按钮", "子过程": [{"子过程描述": "输入过滤条件", "数据移动类型": "E", "数据组": "过滤条件", "数据属性": "类型名称、类型编码", "CFP": 1}, {"子过程描述": "读取密码应用类型", "数据移动类型": "R", "数据组": "密码应用类型信息", "数据属性": "类型编码、类型名称、备注", "CFP": 1}, {"子过程描述": "返回密码应用类型过滤查询结果", "数据移动类型": "X", "数据组": "密码应用类型过滤查询结果", "数据属性": "类型编码、类型名称、备注、过滤维度", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "密码应用类型管理", "功能过程": "新增密码应用类型", "功能描述": "应用类型下无对应用时，允许删除密码应用类型", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击新增按钮", "子过程": [{"子过程描述": "输入新增密码应用类型信息", "数据移动类型": "E", "数据组": "密码应用类型信息", "数据属性": "类型编码、类型名称、备注", "CFP": 1}, {"子过程描述": "密码应用类型重复性校验", "数据移动类型": "R", "数据组": "密码应用类型约束条件", "数据属性": "类型编码、类型名称、非空校验、判重、类型名称长度", "CFP": 1}, {"子过程描述": "新增密码应用类型入库", "数据移动类型": "W", "数据组": "密码应用类型信息", "数据属性": "类型编码、类型名称、备注", "CFP": 1}, {"子过程描述": "返回展示新增密码应用类型", "数据移动类型": "X", "数据组": "密码应用类型新增内容", "数据属性": "类型编码、类型名称、备注、新增结果", "CFP": 1}], "预估工作量": 4}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "密码应用类型管理", "功能过程": "编辑密码应用类型", "功能描述": "应用类型下无对应用时，允许删除密码应用类型", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击编辑按钮", "子过程": [{"子过程描述": "输入编辑密码应用类型信息", "数据移动类型": "E", "数据组": "密码应用类型编辑信息", "数据属性": "类型编码、类型名称、备注", "CFP": 1}, {"子过程描述": "获取密码应用类型编辑项", "数据移动类型": "R", "数据组": "密码应用类型编辑项", "数据属性": "类型编码、类型名称、备注", "CFP": 1}, {"子过程描述": "密码应用类型编辑保存", "数据移动类型": "W", "数据组": "密码应用类型编辑结果", "数据属性": "类型编码、类型名称、备注、编辑人", "CFP": 1}, {"子过程描述": "返回展示密码应用类型编辑结果", "数据移动类型": "X", "数据组": "密码应用类型编辑结果", "数据属性": "类型编码、类型名称、备注、编辑结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "密码应用类型管理", "功能过程": "删除密码应用类型", "功能描述": "应用类型下无对应用时，允许删除密码应用类型", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击删除按钮", "子过程": [{"子过程描述": "发起密码应用类型删除请求", "数据移动类型": "E", "数据组": "密码应用类型删除请求", "数据属性": "密码应用类型ID、删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码应用类型是否可删除", "数据移动类型": "R", "数据组": "密码应用类型删除权限", "数据属性": "操作员权限、密码应用类型名称、密码应用类型ID、操作员ID", "CFP": 1}, {"子过程描述": "密码应用类型删除保存", "数据移动类型": "W", "数据组": "密码应用类型删除结果", "数据属性": "密码应用类型名称、密码应用类型ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码应用类型删除内容", "数据移动类型": "X", "数据组": "密码应用类型删除内容", "数据属性": "密码应用类型名称、密码应用类型ID、删除内容", "CFP": 1}], "预估工作量": 4}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "应用关联应用类型", "功能过程": "密码应用类型下拉选择", "功能描述": "统计展示平台中应用类型下包含的应用数量分布", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块", "触发事件": "用户点击密码应用类型下拉框", "子过程": [{"子过程描述": "获取密码应用类型名称和ID", "数据移动类型": "R", "数据组": "密码应用类型", "数据属性": "密码应用类型名称、密码应用类型ID", "CFP": 1}, {"子过程描述": "返回密码应用类型名称和ID", "数据移动类型": "X", "数据组": "密码应用类型", "数据属性": "密码应用类型名称、密码应用类型ID", "CFP": 1}, {"子过程描述": "保存密码应用类型名称和ID", "数据移动类型": "W", "数据组": "密码应用类型", "数据属性": "密码应用类型名称、密码应用类型ID", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用类型管理", "三级功能模块": "应用关联应用类型", "功能过程": "密码应用类型应用数量分布", "功能描述": "统计展示平台中应用类型下包含的应用数量分布", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块", "触发事件": "用户点击密码应用类型应用数量分布", "子过程": [{"子过程描述": "获取密码应用类型名称", "数据移动类型": "R", "数据组": "密码应用类型", "数据属性": "密码应用类型名称、密码应用类型ID", "CFP": 1}, {"子过程描述": "统计密码应用类型应用数量", "数据移动类型": "R", "数据组": "应用数量", "数据属性": "密码应用类型名称、密码应用类型ID、应用数量", "CFP": 1}, {"子过程描述": "返回密码应用类型应用数量分布", "数据移动类型": "X", "数据组": "密码应用类型应用数量分布", "数据属性": "密码应用类型名称、密码应用类型ID、应用数量", "CFP": 1}, {"子过程描述": "保存密码应用类型应用数量分布", "数据移动类型": "W", "数据组": "密码应用类型应用数量分布", "数据属性": "密码应用类型名称、密码应用类型ID、应用数量", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "密码应用分页列表查询", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用分页列表", "子过程": [{"子过程描述": "输入密码应用分页列表查询条件", "数据移动类型": "E", "数据组": "密码应用分页列表查询请求", "数据属性": "密码应用分页列表查询条件、密码应用分页列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用分页列表", "数据移动类型": "R", "数据组": "密码应用分页列表", "数据属性": "密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID", "CFP": 1}, {"子过程描述": "返回密码应用分页列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用分页列表查询结果", "数据属性": "密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码应用分页列表查询记录", "数据移动类型": "W", "数据组": "密码应用分页列表查询记录", "数据属性": "密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "密码应用过滤查询", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用过滤查询", "子过程": [{"子过程描述": "输入密码应用过滤查询条件", "数据移动类型": "E", "数据组": "密码应用过滤查询请求", "数据属性": "密码应用过滤查询条件、密码应用过滤查询项", "CFP": 1}, {"子过程描述": "读取密码应用过滤查询结果", "数据移动类型": "R", "数据组": "密码应用过滤查询结果", "数据属性": "密码应用过滤查询维度、密码应用过滤查询时间、密码应用过滤查询数量", "CFP": 1}, {"子过程描述": "输出密码应用过滤查询结果", "数据移动类型": "X", "数据组": "密码应用过滤查询结果展示", "数据属性": "密码应用过滤查询维度、密码应用过滤查询时间、密码应用过滤查询数量、分页数、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "新增密码应用", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击新增密码应用", "子过程": [{"子过程描述": "输入新增密码应用信息", "数据移动类型": "E", "数据组": "新增密码应用信息", "数据属性": "密码应用ID、密码应用名称、密码应用类型", "CFP": 1}, {"子过程描述": "密码应用重复性校验", "数据移动类型": "R", "数据组": "密码应用校验信息", "数据属性": "密码应用约束条件、密码应用名称、主键、非空校验、判重、密码应用类型", "CFP": 1}, {"子过程描述": "新增密码应用入库", "数据移动类型": "W", "数据组": "新增密码应用入库信息", "数据属性": "密码应用ID、密码应用名称、密码应用类型、新增时间", "CFP": 1}, {"子过程描述": "返回展示新增密码应用", "数据移动类型": "X", "数据组": "新增密码应用展示信息", "数据属性": "密码应用ID、密码应用名称、密码应用类型、新增结果", "CFP": 1}, {"子过程描述": "记录新增密码应用日志", "数据移动类型": "W", "数据组": "新增密码应用日志", "数据属性": "密码应用ID、密码应用名称、密码应用类型、操作人、系统时间", "CFP": 1}, {"子过程描述": "新增密码应用数据统计", "数据移动类型": "W", "数据组": "新增密码应用统计数据", "数据属性": "密码应用ID、密码应用名称、密码应用类型、统计维度、统计时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "编辑密码应用", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击编辑密码应用", "子过程": [{"子过程描述": "输入编辑密码应用信息", "数据移动类型": "E", "数据组": "编辑密码应用信息", "数据属性": "密码应用ID、密码应用名称、密码应用类型", "CFP": 1}, {"子过程描述": "获取密码应用编辑项", "数据移动类型": "R", "数据组": "密码应用编辑项", "数据属性": "密码应用约束条件、密码应用名称、主键、非空校验、判重、密码应用类型", "CFP": 1}, {"子过程描述": "密码应用编辑保存", "数据移动类型": "W", "数据组": "密码应用编辑结果", "数据属性": "密码应用ID、密码应用名称、密码应用类型、编辑内容、编辑结果", "CFP": 1}, {"子过程描述": "返回展示编辑密码应用", "数据移动类型": "X", "数据组": "密码应用编辑结果展示", "数据属性": "密码应用ID、密码应用名称、密码应用类型、编辑内容、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "删除密码应用", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击删除密码应用", "子过程": [{"子过程描述": "发起删除密码应用请求", "数据移动类型": "E", "数据组": "删除密码应用请求", "数据属性": "删除密码应用指令", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码应用是否可删除", "数据移动类型": "R", "数据组": "删除权限", "数据属性": "操作员权限、操作员ID、操作员名称", "CFP": 1}, {"子过程描述": "密码应用删除保存", "数据移动类型": "W", "数据组": "密码应用删除结果", "数据属性": "密码应用ID、密码应用名称、密码应用类型、删除结果", "CFP": 1}, {"子过程描述": "返回展示删除密码应用内容", "数据移动类型": "X", "数据组": "密码应用删除内容", "数据属性": "密码应用ID、密码应用名称、密码应用类型、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "密码应用详情", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用详情", "子过程": [{"子过程描述": "发起密码应用详情请求", "数据移动类型": "E", "数据组": "密码应用详情请求", "数据属性": "密码应用详情查询条件", "CFP": 1}, {"子过程描述": "获取密码应用详情", "数据移动类型": "R", "数据组": "密码应用详情", "数据属性": "密码应用详情名称、密码应用详情类型、密码应用详情ID", "CFP": 1}, {"子过程描述": "返回展示密码应用详情", "数据移动类型": "X", "数据组": "密码应用详情查询结果", "数据属性": "密码应用详情名称、密码应用详情类型、密码应用详情ID、分页数", "CFP": 1}, {"子过程描述": "保存密码应用详情查询记录", "数据移动类型": "W", "数据组": "密码应用详情查询记录", "数据属性": "密码应用详情名称、密码应用详情类型、密码应用详情ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用管理", "功能过程": "密码应用信息完整性校验", "功能描述": "保障密码应用的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用信息完整性校验", "子过程": [{"子过程描述": "发起密码应用信息完整性校验请求", "数据移动类型": "E", "数据组": "密码应用信息完整性校验请求", "数据属性": "密码应用信息完整性校验指令", "CFP": 1}, {"子过程描述": "密码应用信息完整性校验", "数据移动类型": "R", "数据组": "密码应用信息完整性校验信息", "数据属性": "密码应用信息完整性校验结果、密码应用信息完整性校验时间、密码应用信息完整性校验方式", "CFP": 1}, {"子过程描述": "返回展示密码应用信息完整性校验结果", "数据移动类型": "X", "数据组": "密码应用信息完整性校验结果", "数据属性": "密码应用信息完整性校验名称、密码应用信息完整性校验类型、密码应用信息完整性校验ID、分页数", "CFP": 1}, {"子过程描述": "保存密码应用信息完整性校验记录", "数据移动类型": "W", "数据组": "密码应用信息完整性校验记录", "数据属性": "密码应用信息完整性校验名称、密码应用信息完整性校验类型、密码应用信息完整性校验ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "应用认证凭证列表查询", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面发起应用认证凭证列表查询请求", "子过程": [{"子过程描述": "输入应用认证凭证列表查询条件", "数据移动类型": "E", "数据组": "应用认证凭证列表查询请求", "数据属性": "页码、单页数量", "CFP": 1}, {"子过程描述": "获取应用认证凭证列表", "数据移动类型": "R", "数据组": "应用认证凭证列表", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、应用认证凭证状态", "CFP": 1}, {"子过程描述": "返回应用认证凭证列表查询结果展示", "数据移动类型": "X", "数据组": "应用认证凭证列表查询结果", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、应用认证凭证状态、页数、页码", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "应用认证凭证过滤查询", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面发起应用认证凭证过滤查询请求", "子过程": [{"子过程描述": "输入应用认证凭证过滤查询条件", "数据移动类型": "E", "数据组": "应用认证凭证过滤查询请求", "数据属性": "密钥ID、描述", "CFP": 1}, {"子过程描述": "获取应用认证凭证过滤查询结果", "数据移动类型": "R", "数据组": "应用认证凭证过滤查询结果", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、应用认证凭证状态、密钥ID、描述", "CFP": 1}, {"子过程描述": "返回展示应用认证凭证过滤查询结果", "数据移动类型": "X", "数据组": "应用认证凭证过滤查询结果展示", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、应用认证凭证状态、密钥ID、描述", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "新增应用认证凭证", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面点击新增应用认证凭证按钮", "子过程": [{"子过程描述": "输入新增应用认证凭证信息", "数据移动类型": "E", "数据组": "新增应用认证凭证信息", "数据属性": "应用认证凭证ID、应用认证凭证名称", "CFP": 1}, {"子过程描述": "选择认证方式", "数据移动类型": "E", "数据组": "认证方式", "数据属性": "认证方式", "CFP": 1}, {"子过程描述": "生成SK文件", "数据移动类型": "X", "数据组": "SK文件", "数据属性": "SK文件", "CFP": 1}, {"子过程描述": "保存新增应用认证凭证", "数据移动类型": "W", "数据组": "新增应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、认证方式、生成时间", "CFP": 1}, {"子过程描述": "同步到认证中心", "数据移动类型": "W", "数据组": "应用认证凭证同步信息", "数据属性": "应用认证凭证ID、应用认证凭证名称、认证方式", "CFP": 1}, {"子过程描述": "返回展示新增应用认证凭证", "数据移动类型": "X", "数据组": "新增应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、认证方式、生成结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "编辑应用认证凭证", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面点击编辑应用认证凭证按钮", "子过程": [{"子过程描述": "输入编辑应用认证凭证信息", "数据移动类型": "E", "数据组": "编辑应用认证凭证信息", "数据属性": "应用认证凭证ID、编辑项", "CFP": 1}, {"子过程描述": "获取应用认证凭证编辑项", "数据移动类型": "R", "数据组": "应用认证凭证编辑项", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型", "CFP": 1}, {"子过程描述": "更新应用认证凭证", "数据移动类型": "W", "数据组": "应用认证凭证更新信息", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、更新时间", "CFP": 1}, {"子过程描述": "返回展示编辑后的应用认证凭证", "数据移动类型": "X", "数据组": "应用认证凭证编辑结果", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "启用应用认证凭证", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面点击启用应用认证凭证按钮", "子过程": [{"子过程描述": "输入启用应用认证凭证指令", "数据移动类型": "E", "数据组": "启用应用认证凭证指令", "数据属性": "应用认证凭证ID、启用指令", "CFP": 1}, {"子过程描述": "获取应用认证凭证", "数据移动类型": "R", "数据组": "应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型", "CFP": 1}, {"子过程描述": "更新应用认证凭证状态为启用", "数据移动类型": "W", "数据组": "应用认证凭证状态", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、启用时间", "CFP": 1}, {"子过程描述": "通知认证中心启用应用认证凭证", "数据移动类型": "X", "数据组": "应用认证凭证启用通知", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、启用结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "停用应用认证凭证", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面点击停用应用认证凭证按钮", "子过程": [{"子过程描述": "输入停用应用认证凭证指令", "数据移动类型": "E", "数据组": "停用应用认证凭证指令", "数据属性": "应用认证凭证ID、停用指令", "CFP": 1}, {"子过程描述": "获取应用认证凭证", "数据移动类型": "R", "数据组": "应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型", "CFP": 1}, {"子过程描述": "更新应用认证凭证状态为停用", "数据移动类型": "W", "数据组": "应用认证凭证状态", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、停用时间", "CFP": 1}, {"子过程描述": "通知认证中心停用应用认证凭证", "数据移动类型": "X", "数据组": "应用认证凭证停用通知", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、停用结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "删除应用认证凭证", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面点击删除应用认证凭证按钮", "子过程": [{"子过程描述": "输入删除应用认证凭证指令", "数据移动类型": "E", "数据组": "删除应用认证凭证指令", "数据属性": "应用认证凭证ID、删除指令", "CFP": 1}, {"子过程描述": "获取应用认证凭证", "数据移动类型": "R", "数据组": "应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型", "CFP": 1}, {"子过程描述": "删除应用认证凭证", "数据移动类型": "W", "数据组": "应用认证凭证", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、删除人", "CFP": 1}, {"子过程描述": "通知认证中心删除应用认证凭证", "数据移动类型": "X", "数据组": "应用认证凭证删除通知", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、删除结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用认证凭证管理", "功能过程": "应用认证凭证完整性校验", "功能描述": "保障应用认证凭证的数据完整性，如数据被篡改，显示异常", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用数据管理-密码应用管理-密码应用认证凭证管理", "触发事件": "用户在应用认证凭证列表页面发起应用认证凭证完整性校验请求", "子过程": [{"子过程描述": "发起应用认证凭证完整性校验请求", "数据移动类型": "E", "数据组": "应用认证凭证完整性校验请求", "数据属性": "应用认证凭证完整性校验参数", "CFP": 1}, {"子过程描述": "获取应用认证凭证完整性", "数据移动类型": "R", "数据组": "应用认证凭证完整性", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型", "CFP": 1}, {"子过程描述": "返回展示应用认证凭证完整性校验结果", "数据移动类型": "X", "数据组": "应用认证凭证完整性校验结果", "数据属性": "应用认证凭证ID、应用认证凭证名称、应用认证凭证类型、校验结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用业务管理", "功能过程": "密码应用业务功能列表", "功能描述": "删除应用业务和密码服务集群的绑定关系", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用业务功能列表", "子过程": [{"子过程描述": "输入密码应用业务功能列表查询条件", "数据移动类型": "E", "数据组": "密码应用业务功能列表查询请求", "数据属性": "密码应用业务功能列表查询条件、密码应用业务功能列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用业务功能列表", "数据移动类型": "R", "数据组": "密码应用业务功能列表", "数据属性": "密码应用业务功能列表名称、密码应用业务功能列表类型、密码应用业务功能列表ID", "CFP": 1}, {"子过程描述": "返回密码应用业务功能列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用业务功能列表查询结果", "数据属性": "密码应用业务功能列表名称、密码应用业务功能列表类型、密码应用业务功能列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用业务管理", "功能过程": "新增密码应用业务功能", "功能描述": "删除应用业务和密码服务集群的绑定关系", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用业务功能列表点击新增按钮", "子过程": [{"子过程描述": "输入密码应用业务功能新增信息", "数据移动类型": "E", "数据组": "密码应用业务功能新增信息", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID", "CFP": 1}, {"子过程描述": "密码应用业务功能重复性校验", "数据移动类型": "R", "数据组": "密码应用业务功能校验信息", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID、校验规则", "CFP": 1}, {"子过程描述": "密码应用业务功能新增入库", "数据移动类型": "W", "数据组": "密码应用业务功能新增内容", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID、新增结果", "CFP": 1}, {"子过程描述": "返回展示密码应用业务功能新增内容", "数据移动类型": "X", "数据组": "密码应用业务功能新增内容", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID、新增时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用管理", "三级功能模块": "密码应用业务管理", "功能过程": "删除密码应用业务功能", "功能描述": "删除应用业务和密码服务集群的绑定关系", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用业务功能列表点击删除按钮", "子过程": [{"子过程描述": "输入密码应用业务功能删除信息", "数据移动类型": "E", "数据组": "密码应用业务功能删除信息", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID", "CFP": 1}, {"子过程描述": "判断密码应用业务功能是否被引用", "数据移动类型": "R", "数据组": "密码应用业务功能关联信息", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID、关联数", "CFP": 1}, {"子过程描述": "密码应用业务功能删除保存", "数据移动类型": "W", "数据组": "密码应用业务功能删除结果", "数据属性": "密码应用业务功能名称、密码应用业务功能类型、密码应用业务功能ID、删除结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用场景管理", "三级功能模块": "密码应用场景管理", "功能过程": "密码应用场景分页列表查询", "功能描述": "删除密码应用场景", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用场景分页列表查询", "子过程": [{"子过程描述": "输入密码应用场景分页列表查询条件", "数据移动类型": "E", "数据组": "密码应用场景分页列表查询请求", "数据属性": "页码、单页数量、密码应用场景分页列表查询条件", "CFP": 1}, {"子过程描述": "读取密码应用场景分页列表", "数据移动类型": "R", "数据组": "密码应用场景分页列表", "数据属性": "密码应用场景分页列表名称、密码应用场景分页列表类型、密码应用场景分页列表ID", "CFP": 1}, {"子过程描述": "返回密码应用场景分页列表查询结果", "数据移动类型": "X", "数据组": "密码应用场景分页列表查询结果", "数据属性": "密码应用场景分页列表名称、密码应用场景分页列表类型、密码应用场景分页列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码应用场景分页列表查询记录", "数据移动类型": "W", "数据组": "密码应用场景分页列表查询记录", "数据属性": "密码应用场景分页列表名称、密码应用场景分页列表类型、密码应用场景分页列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用场景管理", "三级功能模块": "密码应用场景管理", "功能过程": "新建密码应用场景", "功能描述": "删除密码应用场景", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击新建密码应用场景", "子过程": [{"子过程描述": "输入新建密码应用场景信息", "数据移动类型": "E", "数据组": "新建密码应用场景信息", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID", "CFP": 1}, {"子过程描述": "密码应用场景重复性校验", "数据移动类型": "R", "数据组": "密码应用场景校验信息", "数据属性": "密码应用场景名称、主键、非空校验、判重、密码应用场景类型", "CFP": 1}, {"子过程描述": "密码应用场景入库", "数据移动类型": "W", "数据组": "新建密码应用场景入库信息", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、入库时间", "CFP": 1}, {"子过程描述": "返回展示新建密码应用场景", "数据移动类型": "X", "数据组": "新建密码应用场景展示信息", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、操作结果", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用场景管理", "三级功能模块": "密码应用场景管理", "功能过程": "编辑密码应用场景", "功能描述": "删除密码应用场景", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击编辑密码应用场景", "子过程": [{"子过程描述": "输入编辑密码应用场景信息", "数据移动类型": "E", "数据组": "编辑密码应用场景信息", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID", "CFP": 1}, {"子过程描述": "获取密码应用场景编辑项", "数据移动类型": "R", "数据组": "密码应用场景编辑项", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、编辑项", "CFP": 1}, {"子过程描述": "密码应用场景编辑保存", "数据移动类型": "W", "数据组": "密码应用场景编辑结果", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、编辑结果", "CFP": 1}, {"子过程描述": "输出密码应用场景编辑结果", "数据移动类型": "X", "数据组": "密码应用场景编辑结果展示信息", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用场景管理", "三级功能模块": "密码应用场景管理", "功能过程": "删除密码应用场景", "功能描述": "删除密码应用场景", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击删除密码应用场景", "子过程": [{"子过程描述": "发起密码应用场景删除请求", "数据移动类型": "E", "数据组": "密码应用场景删除请求", "数据属性": "密码应用场景删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码应用场景是否可删除", "数据移动类型": "R", "数据组": "密码应用场景删除权限", "数据属性": "密码应用场景名称、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "密码应用场景删除保存", "数据移动类型": "W", "数据组": "密码应用场景删除结果", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码应用场景删除内容", "数据移动类型": "X", "数据组": "密码应用场景删除内容", "数据属性": "密码应用场景名称、密码应用场景类型、密码应用场景ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用改造厂商管理", "三级功能模块": "密码应用改造厂商管理", "功能过程": "密码应用改造厂商分页列表查询", "功能描述": "删除密码应用改造的厂商信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用改造厂商管理模块", "触发事件": "用户点击密码应用改造厂商列表查询按钮", "子过程": [{"子过程描述": "输入密码应用改造厂商列表查询条件", "数据移动类型": "E", "数据组": "密码应用改造厂商列表查询请求", "数据属性": "密码应用改造厂商列表查询条件、密码应用改造厂商列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用改造厂商列表", "数据移动类型": "R", "数据组": "密码应用改造厂商列表", "数据属性": "密码应用改造厂商列表名称、密码应用改造厂商列表类型、密码应用改造厂商列表ID", "CFP": 1}, {"子过程描述": "返回密码应用改造厂商列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用改造厂商列表查询结果", "数据属性": "密码应用改造厂商列表名称、密码应用改造厂商列表类型、密码应用改造厂商列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码应用改造厂商列表查询记录", "数据移动类型": "W", "数据组": "密码应用改造厂商列表查询记录", "数据属性": "密码应用改造厂商列表名称、密码应用改造厂商列表类型、密码应用改造厂商列表ID、操作人、系统时间", "CFP": 1}], "预估工作量": 4}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用改造厂商管理", "三级功能模块": "密码应用改造厂商管理", "功能过程": "新增密码应用改造厂商", "功能描述": "删除密码应用改造的厂商信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用改造厂商管理模块", "触发事件": "用户点击新增密码应用改造厂商", "子过程": [{"子过程描述": "输入新增密码应用改造厂商信息", "数据移动类型": "E", "数据组": "密码应用改造厂商新增信息", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID", "CFP": 1}, {"子过程描述": "密码应用改造厂商重复性校验", "数据移动类型": "R", "数据组": "密码应用改造厂商校验信息", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、判重规则", "CFP": 1}, {"子过程描述": "密码应用改造厂商新增入库", "数据移动类型": "W", "数据组": "密码应用改造厂商新增入库信息", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示密码应用改造厂商新增内容", "数据移动类型": "X", "数据组": "密码应用改造厂商新增内容", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、新增结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用改造厂商管理", "三级功能模块": "密码应用改造厂商管理", "功能过程": "编辑密码应用改造厂商", "功能描述": "删除密码应用改造的厂商信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用改造厂商管理模块", "触发事件": "用户点击编辑密码应用改造厂商", "子过程": [{"子过程描述": "输入编辑密码应用改造厂商指令", "数据移动类型": "E", "数据组": "密码应用改造厂商编辑指令", "数据属性": "密码应用改造厂商编辑指令", "CFP": 1}, {"子过程描述": "获取密码应用改造厂商编辑项", "数据移动类型": "R", "数据组": "密码应用改造厂商编辑项", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、编辑项", "CFP": 1}, {"子过程描述": "密码应用改造厂商编辑保存", "数据移动类型": "W", "数据组": "密码应用改造厂商编辑结果", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、编辑结果", "CFP": 1}, {"子过程描述": "返回展示密码应用改造厂商编辑结果", "数据移动类型": "X", "数据组": "密码应用改造厂商编辑结果", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、编辑内容", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用数据管理", "二级功能模块": "密码应用改造厂商管理", "三级功能模块": "密码应用改造厂商管理", "功能过程": "删除密码应用改造厂商", "功能描述": "删除密码应用改造的厂商信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码应用改造厂商管理模块", "触发事件": "用户点击删除密码应用改造厂商", "子过程": [{"子过程描述": "发起密码应用改造厂商删除请求", "数据移动类型": "E", "数据组": "密码应用改造厂商删除请求", "数据属性": "密码应用改造厂商删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码应用改造厂商是否可删除", "数据移动类型": "R", "数据组": "密码应用改造厂商删除权限", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、操作员权限", "CFP": 1}, {"子过程描述": "密码应用改造厂商删除保存", "数据移动类型": "W", "数据组": "密码应用改造厂商删除结果", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码应用改造厂商删除内容", "数据移动类型": "X", "数据组": "密码应用改造厂商删除内容", "数据属性": "密码应用改造厂商名称、密码应用改造厂商类型、密码应用改造厂商ID、删除内容", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "密码服务列表", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户点击密码服务列表菜单", "子过程": [{"子过程描述": "输入密码服务列表查询条件", "数据移动类型": "E", "数据组": "密码服务列表查询请求", "数据属性": "密码服务列表查询条件、密码服务列表查询项", "CFP": 1}, {"子过程描述": "读取密码服务列表", "数据移动类型": "R", "数据组": "密码服务列表", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号、密码服务ID", "CFP": 1}, {"子过程描述": "返回密码服务列表查询结果展示", "数据移动类型": "X", "数据组": "密码服务列表查询结果", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号、密码服务ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码服务列表查询记录", "数据移动类型": "W", "数据组": "密码服务列表查询记录", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号、密码服务ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "密码服务查询", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击查询按钮", "子过程": [{"子过程描述": "输入密码服务查询条件", "数据移动类型": "E", "数据组": "密码服务查询条件", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号", "CFP": 1}, {"子过程描述": "获取密码服务", "数据移动类型": "R", "数据组": "密码服务", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号、密码服务ID", "CFP": 1}, {"子过程描述": "返回密码服务查询结果展示", "数据移动类型": "X", "数据组": "密码服务查询结果", "数据属性": "密码服务名称、密码服务类型、IP地址、端口号、密码服务ID、分页数", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "密码服务状态检测", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "密码服务状态检测", "子过程": [{"子过程描述": "获取密码服务状态检测任务", "数据移动类型": "R", "数据组": "密码服务状态检测任务", "数据属性": "密码服务状态检测周期、密码服务状态检测方式", "CFP": 1}, {"子过程描述": "调用密码服务状态检测接口", "数据移动类型": "R", "数据组": "密码服务状态检测接口", "数据属性": "密码服务状态检测接口名称、密码服务状态检测接口类型", "CFP": 1}, {"子过程描述": "更新密码服务状态", "数据移动类型": "W", "数据组": "密码服务状态检测结果", "数据属性": "密码服务状态检测时间、密码服务状态检测结果", "CFP": 1}, {"子过程描述": "返回展示密码服务状态检测结果", "数据移动类型": "X", "数据组": "密码服务状态检测内容", "数据属性": "密码服务状态检测时间、密码服务状态检测结果、密码服务状态检测内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "新建密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 10, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击新增按钮", "子过程": [{"子过程描述": "输入密码服务新增信息", "数据移动类型": "E", "数据组": "密码服务新增信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务重复性校验", "数据移动类型": "R", "数据组": "密码服务校验信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "生成密码服务新增ID", "数据移动类型": "R", "数据组": "密码服务新增ID", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增入库", "数据移动类型": "W", "数据组": "密码服务新增入库信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增数据同步", "数据移动类型": "X", "数据组": "密码服务新增同步信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增数据归档", "数据移动类型": "W", "数据组": "密码服务新增归档信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增数据推送", "数据移动类型": "X", "数据组": "密码服务新增推送信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增数据统计", "数据移动类型": "R", "数据组": "密码服务新增统计维度", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "密码服务新增数据埋点", "数据移动类型": "R", "数据组": "密码服务新增埋点信息", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}, {"子过程描述": "返回展示密码服务新增内容", "数据移动类型": "X", "数据组": "密码服务新增内容", "数据属性": "密码服务名称、区域、密码服务类型、密码服务集群、设备集群、服务规格、IP、端口、备注", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "编辑密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击编辑按钮", "子过程": [{"子过程描述": "输入密码服务编辑信息", "数据移动类型": "E", "数据组": "密码服务编辑信息", "数据属性": "密码服务名称、备注", "CFP": 1}, {"子过程描述": "获取密码服务编辑项", "数据移动类型": "R", "数据组": "密码服务编辑项", "数据属性": "密码服务名称、备注、非空校验、密码服务名称长度校验", "CFP": 1}, {"子过程描述": "密码服务编辑保存", "数据移动类型": "W", "数据组": "密码服务编辑结果", "数据属性": "编辑时间、密码服务名称、备注", "CFP": 1}, {"子过程描述": "返回展示密码服务编辑结果", "数据移动类型": "X", "数据组": "密码服务编辑结果展示", "数据属性": "密码服务名称、备注、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "重启密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击重启按钮", "子过程": [{"子过程描述": "输入密码服务重启指令", "数据移动类型": "E", "数据组": "密码服务重启指令", "数据属性": "密码服务重启请求", "CFP": 1}, {"子过程描述": "调用密码服务重启接口", "数据移动类型": "R", "数据组": "密码服务重启接口", "数据属性": "密码服务重启参数", "CFP": 1}, {"子过程描述": "更新密码服务重启状态", "数据移动类型": "W", "数据组": "密码服务重启状态", "数据属性": "密码服务重启结果", "CFP": 1}, {"子过程描述": "返回展示密码服务重启结果", "数据移动类型": "X", "数据组": "密码服务重启内容", "数据属性": "密码服务重启结果展示", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "启动密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击启动按钮", "子过程": [{"子过程描述": "输入密码服务启动指令", "数据移动类型": "E", "数据组": "密码服务启动指令", "数据属性": "密码服务启动请求", "CFP": 1}, {"子过程描述": "调用密码服务启动接口", "数据移动类型": "R", "数据组": "密码服务启动接口", "数据属性": "密码服务启动参数", "CFP": 1}, {"子过程描述": "更新密码服务启动状态", "数据移动类型": "W", "数据组": "密码服务启动状态", "数据属性": "密码服务启动结果", "CFP": 1}, {"子过程描述": "返回展示密码服务启动结果", "数据移动类型": "X", "数据组": "密码服务启动内容", "数据属性": "密码服务启动结果展示", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "停止密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击停止按钮", "子过程": [{"子过程描述": "输入密码服务停止指令", "数据移动类型": "E", "数据组": "密码服务停止指令", "数据属性": "密码服务停止请求", "CFP": 1}, {"子过程描述": "调用密码服务停止接口", "数据移动类型": "R", "数据组": "密码服务停止接口", "数据属性": "密码服务停止参数", "CFP": 1}, {"子过程描述": "更新密码服务停止状态", "数据移动类型": "W", "数据组": "密码服务停止状态", "数据属性": "密码服务停止结果", "CFP": 1}, {"子过程描述": "返回展示密码服务停止结果", "数据移动类型": "X", "数据组": "密码服务停止内容", "数据属性": "密码服务停止结果展示", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "更新密码服务规格", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 8, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击更新按钮", "子过程": [{"子过程描述": "输入密码服务规格更新信息", "数据移动类型": "E", "数据组": "密码服务规格更新信息", "数据属性": "密码服务规格更新参数", "CFP": 1}, {"子过程描述": "判断密码服务规格更新是否需要扩容", "数据移动类型": "R", "数据组": "密码服务规格更新阈值", "数据属性": "密码服务规格更新阈值", "CFP": 1}, {"子过程描述": "密码服务规格更新", "数据移动类型": "W", "数据组": "密码服务规格更新结果", "数据属性": "密码服务规格更新时间、密码服务规格更新结果", "CFP": 1}, {"子过程描述": "返回展示密码服务规格更新结果", "数据移动类型": "X", "数据组": "密码服务规格更新内容", "数据属性": "密码服务规格更新结果展示", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务管理", "功能过程": "删除密码服务", "功能描述": "删除停止的密码服务", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务管理模块", "触发事件": "用户在密码服务列表页面点击删除按钮", "子过程": [{"子过程描述": "输入密码服务删除指令", "数据移动类型": "E", "数据组": "密码服务删除指令", "数据属性": "密码服务删除请求", "CFP": 1}, {"子过程描述": "判断密码服务是否可删除", "数据移动类型": "R", "数据组": "密码服务删除约束", "数据属性": "密码服务名称、密码服务类型、密码服务数量、密码服务ID", "CFP": 1}, {"子过程描述": "密码服务删除保存", "数据移动类型": "W", "数据组": "密码服务删除结果", "数据属性": "密码服务名称、密码服务类型、密码服务数量、密码服务ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码服务删除内容", "数据移动类型": "X", "数据组": "密码服务删除内容", "数据属性": "密码服务名称、密码服务类型、密码服务数量、密码服务ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务组管理", "功能过程": "密码服务服务组新增", "功能描述": "从服务组释放密码服务，", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码资产名称管理-密码服务组管理", "触发事件": "用户在密码服务服务组新增页面点击新增按钮", "子过程": [{"子过程描述": "输入密码服务服务组新增信息", "数据移动类型": "E", "数据组": "密码服务服务组新增信息", "数据属性": "密码服务服务组新增ID、密码服务服务组新增名称", "CFP": 1}, {"子过程描述": "密码服务服务组重复性校验", "数据移动类型": "R", "数据组": "密码服务服务组校验信息", "数据属性": "密码服务服务组名称、主键、非空校验、判重、密码服务服务组类型", "CFP": 1}, {"子过程描述": "密码服务服务组新增入库", "数据移动类型": "W", "数据组": "密码服务服务组新增入库信息", "数据属性": "密码服务服务组名称、密码服务服务组类型、密码服务服务组ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示密码服务服务组新增内容", "数据移动类型": "X", "数据组": "密码服务服务组新增内容", "数据属性": "密码服务服务组名称、密码服务服务组类型、密码服务服务组ID、新增结果", "CFP": 1}, {"子过程描述": "密码服务服务组新增数据同步", "数据移动类型": "X", "数据组": "密码服务服务组新增数据", "数据属性": "密码服务服务组名称、密码服务服务组类型、密码服务服务组ID、新增数据", "CFP": 1}, {"子过程描述": "密码服务服务组新增日志记录", "数据移动类型": "W", "数据组": "密码服务服务组新增日志", "数据属性": "密码服务服务组新增操作员名称、密码服务服务组新增时间、密码服务服务组新增结果、操作员ID、密码服务服务组新增内容等", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务组管理", "功能过程": "密码服务服务组列表", "功能描述": "从服务组释放密码服务，", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码资产名称管理-密码服务组管理", "触发事件": "用户在密码服务服务组列表页面发起密码服务服务组列表请求", "子过程": [{"子过程描述": "输入密码服务服务组列表查询条件", "数据移动类型": "E", "数据组": "密码服务服务组列表查询请求", "数据属性": "密码服务服务组列表查询条件、密码服务服务组列表查询项", "CFP": 1}, {"子过程描述": "获取密码服务服务组列表", "数据移动类型": "R", "数据组": "密码服务服务组列表", "数据属性": "密码服务服务组列表名称、密码服务服务组列表类型、密码服务服务组列表ID", "CFP": 1}, {"子过程描述": "返回密码服务服务组列表展示", "数据移动类型": "X", "数据组": "密码服务服务组列表查询结果", "数据属性": "密码服务服务组列表名称、密码服务服务组列表类型、密码服务服务组列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码服务服务组列表查询记录", "数据移动类型": "W", "数据组": "密码服务服务组列表查询记录", "数据属性": "密码服务服务组列表名称、密码服务服务组列表类型、密码服务服务组列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务组管理", "功能过程": "密码服务服务组编辑", "功能描述": "从服务组释放密码服务，", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码资产名称管理-密码服务组管理", "触发事件": "用户在密码服务服务组编辑页面点击编辑按钮", "子过程": [{"子过程描述": "输入密码服务服务组编辑信息", "数据移动类型": "E", "数据组": "密码服务服务组编辑条件", "数据属性": "密码服务服务组ID、密码服务服务组编辑项", "CFP": 1}, {"子过程描述": "获取密码服务服务组编辑项", "数据移动类型": "R", "数据组": "密码服务服务组编辑项", "数据属性": "密码服务服务组约束条件、密码服务服务组名称、密码服务服务组数量", "CFP": 1}, {"子过程描述": "密码服务服务组编辑保存", "数据移动类型": "W", "数据组": "密码服务服务组编辑结果", "数据属性": "编辑时间、密码服务服务组名称、密码服务服务组类型、密码服务服务组ID", "CFP": 1}, {"子过程描述": "返回展示密码服务服务组编辑内容", "数据移动类型": "X", "数据组": "密码服务服务组编辑内容", "数据属性": "密码服务服务组名称、密码服务服务组类型、密码服务服务组ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务组管理", "功能过程": "密码服务管理列表", "功能描述": "从服务组释放密码服务，", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码资产名称管理-密码服务组管理", "触发事件": "用户在密码服务管理列表页面点击密码服务管理列表", "子过程": [{"子过程描述": "输入密码服务管理列表查询请求", "数据移动类型": "E", "数据组": "密码服务管理列表查询请求", "数据属性": "密码服务管理列表查询条件", "CFP": 1}, {"子过程描述": "读取密码服务管理列表", "数据移动类型": "R", "数据组": "密码服务管理列表", "数据属性": "密码服务管理列表名称、密码服务管理列表类型、密码服务管理列表ID", "CFP": 1}, {"子过程描述": "返回密码服务管理列表查询结果展示", "数据移动类型": "X", "数据组": "密码服务管理列表查询结果", "数据属性": "密码服务管理列表名称、密码服务管理列表类型、密码服务管理列表ID、分页数", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务组管理", "功能过程": "密码服务释放", "功能描述": "从服务组释放密码服务，", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码资产名称管理-密码服务组管理", "触发事件": "用户在密码服务释放页面点击密码服务释放按钮", "子过程": [{"子过程描述": "发起密码服务释放请求", "数据移动类型": "E", "数据组": "密码服务释放请求", "数据属性": "密码服务释放指令", "CFP": 1}, {"子过程描述": "获取密码服务", "数据移动类型": "R", "数据组": "密码服务", "数据属性": "密码服务名称、密码服务类型、密码服务ID", "CFP": 1}, {"子过程描述": "密码服务释放", "数据移动类型": "W", "数据组": "密码服务释放结果", "数据属性": "密码服务释放时间、密码服务释放结果", "CFP": 1}, {"子过程描述": "返回展示密码服务释放结果", "数据移动类型": "X", "数据组": "密码服务释放内容", "数据属性": "密码服务释放名称、密码服务释放类型、密码服务释放ID、释放内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像列表", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像列表", "子过程": [{"子过程描述": "输入密码服务镜像列表查询条件", "数据移动类型": "E", "数据组": "密码服务镜像列表查询请求", "数据属性": "页码、单页数量、密码服务镜像列表查询条件", "CFP": 1}, {"子过程描述": "读取密码服务镜像列表", "数据移动类型": "R", "数据组": "密码服务镜像列表", "数据属性": "密码服务镜像列表名称、密码服务镜像列表类型、密码服务镜像列表ID", "CFP": 1}, {"子过程描述": "返回密码服务镜像列表查询结果展示", "数据移动类型": "X", "数据组": "密码服务镜像列表查询结果", "数据属性": "密码服务镜像列表名称、密码服务镜像列表类型、密码服务镜像列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码服务镜像列表查询记录", "数据移动类型": "W", "数据组": "密码服务镜像列表查询记录", "数据属性": "密码服务镜像列表名称、密码服务镜像列表类型、密码服务镜像列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像上传", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像上传", "子过程": [{"子过程描述": "发起密码服务镜像上传请求", "数据移动类型": "E", "数据组": "密码服务镜像上传请求", "数据属性": "密码服务镜像上传目的、密码服务镜像上传方式", "CFP": 1}, {"子过程描述": "密码服务镜像上传", "数据移动类型": "X", "数据组": "密码服务镜像上传结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像大小、上传时间", "CFP": 1}, {"子过程描述": "密码服务镜像上传内容稽核", "数据移动类型": "R", "数据组": "密码服务镜像上传内容", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像大小、稽核规则", "CFP": 1}, {"子过程描述": "密码服务镜像上传保存", "数据移动类型": "W", "数据组": "密码服务镜像上传结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像大小、上传结果", "CFP": 1}, {"子过程描述": "返回展示密码服务镜像上传内容", "数据移动类型": "X", "数据组": "密码服务镜像上传内容", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像大小、上传内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像编辑", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像编辑", "子过程": [{"子过程描述": "输入密码服务镜像编辑信息", "数据移动类型": "E", "数据组": "密码服务镜像编辑信息", "数据属性": "密码服务镜像ID、密码服务镜像编辑项", "CFP": 1}, {"子过程描述": "密码服务镜像编辑保存", "数据移动类型": "W", "数据组": "密码服务镜像编辑结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像查询", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像查询", "子过程": [{"子过程描述": "输入密码服务镜像查询条件", "数据移动类型": "E", "数据组": "密码服务镜像查询请求", "数据属性": "密码服务镜像查询条件", "CFP": 1}, {"子过程描述": "获取密码服务镜像", "数据移动类型": "R", "数据组": "密码服务镜像", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像启用", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像启用", "子过程": [{"子过程描述": "发起密码服务镜像启用请求", "数据移动类型": "E", "数据组": "密码服务镜像启用请求", "数据属性": "密码服务镜像启用目的、密码服务镜像启用方式", "CFP": 1}, {"子过程描述": "密码服务镜像启用保存", "数据移动类型": "W", "数据组": "密码服务镜像启用结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、启用结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像禁用", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像禁用", "子过程": [{"子过程描述": "发起密码服务镜像禁用请求", "数据移动类型": "E", "数据组": "密码服务镜像禁用请求", "数据属性": "密码服务镜像禁用目的、密码服务镜像禁用方式", "CFP": 1}, {"子过程描述": "密码服务镜像禁用保存", "数据移动类型": "W", "数据组": "密码服务镜像禁用结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、禁用结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产名称管理", "三级功能模块": "密码服务镜像管理", "功能过程": "密码服务镜像删除", "功能描述": "删除密码服务镜像", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块", "触发事件": "用户点击密码服务镜像删除", "子过程": [{"子过程描述": "发起密码服务镜像删除请求", "数据移动类型": "E", "数据组": "密码服务镜像删除请求", "数据属性": "密码服务镜像删除目的、密码服务镜像删除方式", "CFP": 1}, {"子过程描述": "判断密码服务镜像是否可删除", "数据移动类型": "R", "数据组": "密码服务镜像删除约束", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、删除权限", "CFP": 1}, {"子过程描述": "密码服务镜像删除保存", "数据移动类型": "W", "数据组": "密码服务镜像删除结果", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码服务镜像删除内容", "数据移动类型": "X", "数据组": "密码服务镜像删除内容", "数据属性": "密码服务镜像名称、密码服务镜像类型、密码服务镜像ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库管理", "功能过程": "密码服务数据库新增", "功能描述": "列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码服务数据库管理页面点击新增按钮", "子过程": [{"子过程描述": "输入新增密码服务数据库信息", "数据移动类型": "E", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "密码服务数据库重复性校验", "数据移动类型": "R", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "新增密码服务数据库入库", "数据移动类型": "W", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "返回展示新增密码服务数据库内容", "数据移动类型": "X", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库管理", "功能过程": "密码服务数据库编辑", "功能描述": "列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码服务数据库管理页面点击编辑按钮", "子过程": [{"子过程描述": "输入编辑密码服务数据库信息", "数据移动类型": "E", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "获取密码服务数据库编辑项", "数据移动类型": "R", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "密码服务数据库编辑保存", "数据移动类型": "W", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "返回展示编辑密码服务数据库内容", "数据移动类型": "X", "数据组": "密码服务数据库信息", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库管理", "功能过程": "密码服务数据库删除", "功能描述": "列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码服务数据库管理页面点击删除按钮", "子过程": [{"子过程描述": "发起密码服务数据库删除请求", "数据移动类型": "E", "数据组": "密码服务数据库删除请求", "数据属性": "密码服务数据库删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码服务数据库是否可删除", "数据移动类型": "R", "数据组": "密码服务数据库删除权限", "数据属性": "密码服务数据库名称、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "密码服务数据库删除保存", "数据移动类型": "W", "数据组": "密码服务数据库删除结果", "数据属性": "密码服务数据库名称、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码服务数据库删除内容", "数据移动类型": "X", "数据组": "密码服务数据库删除内容", "数据属性": "密码服务数据库名称、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库管理", "功能过程": "密码服务数据库列表", "功能描述": "列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码服务数据库管理页面点击列表按钮", "子过程": [{"子过程描述": "输入密码服务数据库列表查询条件", "数据移动类型": "E", "数据组": "密码服务数据库列表查询请求", "数据属性": "密码服务数据库列表查询条件、密码服务数据库列表查询项", "CFP": 1}, {"子过程描述": "读取密码服务数据库列表", "数据移动类型": "R", "数据组": "密码服务数据库列表", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码", "CFP": 1}, {"子过程描述": "返回密码服务数据库列表展示", "数据移动类型": "X", "数据组": "密码服务数据库列表查询结果", "数据属性": "密码服务数据库名称、密码服务数据库类型、密码服务数据库ID、密码服务数据库IP、密码服务数据库端口、密码服务数据库账号、密码服务数据库密码、分页数", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库模式管理", "功能过程": "密码服务数据库模式列表", "功能描述": "新增数据库模式", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击密码服务数据库模式列表", "子过程": [{"子过程描述": "输入密码服务数据库模式列表查询条件", "数据移动类型": "E", "数据组": "密码服务数据库模式列表查询请求", "数据属性": "密码服务数据库模式列表查询条件、密码服务数据库模式列表查询项", "CFP": 1}, {"子过程描述": "读取密码服务数据库模式列表", "数据移动类型": "R", "数据组": "密码服务数据库模式列表", "数据属性": "密码服务数据库模式列表名称、密码服务数据库模式列表类型、密码服务数据库模式列表ID", "CFP": 1}, {"子过程描述": "返回密码服务数据库模式列表查询结果展示", "数据移动类型": "X", "数据组": "密码服务数据库模式列表查询结果", "数据属性": "密码服务数据库模式列表名称、密码服务数据库模式列表类型、密码服务数据库模式列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码服务数据库模式列表查询记录", "数据移动类型": "W", "数据组": "密码服务数据库模式列表操作记录", "数据属性": "密码服务数据库模式列表名称、密码服务数据库模式列表类型、密码服务数据库模式列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库模式管理", "功能过程": "密码服务数据库模式删除", "功能描述": "新增数据库模式", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击密码服务数据库模式删除", "子过程": [{"子过程描述": "发起密码服务数据库模式删除请求", "数据移动类型": "E", "数据组": "密码服务数据库模式删除请求", "数据属性": "密码服务数据库模式删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码服务数据库模式是否可删除", "数据移动类型": "R", "数据组": "密码服务数据库模式删除权限", "数据属性": "密码服务数据库模式名称、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "密码服务数据库模式删除保存", "数据移动类型": "W", "数据组": "密码服务数据库模式删除结果", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码服务数据库模式删除内容", "数据移动类型": "X", "数据组": "密码服务数据库模式删除内容", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库模式管理", "功能过程": "密码服务数据库模式查询", "功能描述": "新增数据库模式", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击密码服务数据库模式查询", "子过程": [{"子过程描述": "输入密码服务数据库模式查询条件", "数据移动类型": "E", "数据组": "密码服务数据库模式查询请求", "数据属性": "密码服务数据库模式查询条件、密码服务数据库模式查询项", "CFP": 1}, {"子过程描述": "读取密码服务数据库模式", "数据移动类型": "R", "数据组": "密码服务数据库模式", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID", "CFP": 1}, {"子过程描述": "返回密码服务数据库模式查询结果", "数据移动类型": "X", "数据组": "密码服务数据库模式查询结果", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID、分页数、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码服务数据库模式管理", "功能过程": "密码服务数据库模式新增", "功能描述": "新增数据库模式", "预估工作量（人天）": 2, "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击密码服务数据库模式新增", "子过程": [{"子过程描述": "输入密码服务数据库模式新增信息", "数据移动类型": "E", "数据组": "密码服务数据库模式新增信息", "数据属性": "密码服务数据库模式新增ID、密码服务数据库模式新增名称", "CFP": 1}, {"子过程描述": "密码服务数据库模式重复性校验", "数据移动类型": "R", "数据组": "密码服务数据库模式校验信息", "数据属性": "密码服务数据库模式名称、主键、非空校验、判重、密码服务数据库模式类型", "CFP": 1}, {"子过程描述": "密码服务数据库模式新增入库", "数据移动类型": "W", "数据组": "密码服务数据库模式新增入库信息", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID、新增结果", "CFP": 1}, {"子过程描述": "返回展示密码服务数据库模式新增内容", "数据移动类型": "X", "数据组": "密码服务数据库模式新增内容", "数据属性": "密码服务数据库模式名称、密码服务数据库模式类型、密码服务数据库模式ID、新增内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "API网关管理", "功能过程": "API网关列表", "功能描述": "删除网关信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击网关列表菜单", "子过程": [{"子过程描述": "输入网关列表查询条件", "数据移动类型": "E", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID、分页数、分页大小", "CFP": 1}, {"子过程描述": "读取网关列表", "数据移动类型": "R", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID、分页数、分页大小", "CFP": 1}, {"子过程描述": "返回网关列表", "数据移动类型": "X", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID、分页数、分页大小", "CFP": 1}, {"子过程描述": "保存网关列表", "数据移动类型": "W", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID、分页数、分页大小", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "API网关管理", "功能过程": "API网关初始化", "功能描述": "删除网关信息", "预估工作量（人天）": "3", "功能用户": "发起者：平台，接收者：密码服务平台", "触发事件": "平台部署成功", "子过程": [{"子过程描述": "获取平台部署信息", "数据移动类型": "E", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID", "CFP": 1}, {"子过程描述": "保存网关信息", "数据移动类型": "W", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID", "CFP": 1}, {"子过程描述": "返回网关初始化结果", "数据移动类型": "X", "数据组": "API网关信息", "数据属性": "网关名称、所属区域、网关类型、网关ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "API网关管理", "功能过程": "API网关新增", "功能描述": "删除网关信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击新增按钮", "子过程": [{"子过程描述": "输入新增网关信息", "数据移动类型": "E", "数据组": "API网关信息", "数据属性": "网关名称、所属需求、网关标识、网关类型、管理端口", "CFP": 1}, {"子过程描述": "保存新增网关信息", "数据移动类型": "W", "数据组": "API网关信息", "数据属性": "网关名称、所属需求、网关标识、网关类型、管理端口", "CFP": 1}, {"子过程描述": "返回新增网关信息", "数据移动类型": "X", "数据组": "API网关信息", "数据属性": "网关名称、所属需求、网关标识、网关类型、管理端口", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "API网关管理", "功能过程": "API网关编辑", "功能描述": "删除网关信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击编辑按钮", "子过程": [{"子过程描述": "输入编辑网关信息", "数据移动类型": "E", "数据组": "API网关信息", "数据属性": "网关名称、管理端口", "CFP": 1}, {"子过程描述": "更新网关信息", "数据移动类型": "W", "数据组": "API网关信息", "数据属性": "网关名称、管理端口、修改时间", "CFP": 1}, {"子过程描述": "返回编辑网关信息", "数据移动类型": "X", "数据组": "API网关信息", "数据属性": "网关名称、管理端口", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "API网关管理", "功能过程": "API网关删除", "功能描述": "删除网关信息", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击删除按钮", "子过程": [{"子过程描述": "输入删除网关信息", "数据移动类型": "E", "数据组": "API网关信息", "数据属性": "网关名称、网关ID", "CFP": 1}, {"子过程描述": "删除网关信息", "数据移动类型": "W", "数据组": "API网关信息", "数据属性": "网关名称、网关ID", "CFP": 1}, {"子过程描述": "返回删除网关信息", "数据移动类型": "X", "数据组": "API网关信息", "数据属性": "网关名称、网关ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "网关路由管理", "功能过程": "路由管理列表", "功能描述": "展示路由管理详情，包含服务列表信息、应用信息", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：网关路由管理模块", "触发事件": "用户点击路由管理菜单", "子过程": [{"子过程描述": "输入查询条件", "数据移动类型": "E", "数据组": "查询条件信息", "数据属性": "页码、单页数量、路由名称", "CFP": 1}, {"子过程描述": "读取路由列表", "数据移动类型": "R", "数据组": "路由列表信息", "数据属性": "路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间", "CFP": 1}, {"子过程描述": "返回路由列表", "数据移动类型": "X", "数据组": "路由列表信息", "数据属性": "路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间", "CFP": 1}, {"子过程描述": "保存路由列表", "数据移动类型": "W", "数据组": "路由列表信息", "数据属性": "路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "网关路由管理", "功能过程": "路由管理详情", "功能描述": "展示路由管理详情，包含服务列表信息、应用信息", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：网关路由管理模块", "触发事件": "用户点击路由详情", "子过程": [{"子过程描述": "输入路由ID", "数据移动类型": "E", "数据组": "路由ID", "数据属性": "路由ID", "CFP": 1}, {"子过程描述": "读取路由详情", "数据移动类型": "R", "数据组": "路由详情信息", "数据属性": "路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间", "CFP": 1}, {"子过程描述": "读取服务列表", "数据移动类型": "R", "数据组": "服务列表信息", "数据属性": "服务名称、服务类型、服务ID", "CFP": 1}, {"子过程描述": "返回路由详情", "数据移动类型": "X", "数据组": "路由详情信息", "数据属性": "路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型展示", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型列表", "子过程": [{"子过程描述": "输入设备类型查询条件", "数据移动类型": "E", "数据组": "设备类型查询请求", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、页码、单页数量", "CFP": 1}, {"子过程描述": "读取设备类型信息", "数据移动类型": "R", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "读取设备类型监控信息", "数据移动类型": "R", "数据组": "设备类型监控信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、监控方式", "CFP": 1}, {"子过程描述": "返回设备类型列表展示", "数据移动类型": "X", "数据组": "设备类型列表", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型初始化", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "平台部署时", "子过程": [{"子过程描述": "输入设备类型初始化指令", "数据移动类型": "E", "数据组": "设备类型初始化指令", "数据属性": "设备类型初始化参数", "CFP": 1}, {"子过程描述": "读取设备类型初始化参数", "数据移动类型": "R", "数据组": "设备类型初始化参数", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "初始化设备类型信息", "数据移动类型": "W", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "返回设备类型初始化结果", "数据移动类型": "X", "数据组": "设备类型初始化结果", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、初始化结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型新增", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型新增按钮", "子过程": [{"子过程描述": "输入设备类型新增信息", "数据移动类型": "E", "数据组": "设备类型新增信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "校验设备类型是否重复", "数据移动类型": "R", "数据组": "设备类型校验信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "保存设备类型新增信息", "数据移动类型": "W", "数据组": "设备类型新增信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "返回设备类型新增内容", "数据移动类型": "X", "数据组": "设备类型新增内容", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、新增结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型编辑", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型编辑按钮", "子过程": [{"子过程描述": "输入设备类型编辑信息", "数据移动类型": "E", "数据组": "设备类型编辑信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "读取设备类型信息", "数据移动类型": "R", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "更新设备类型信息", "数据移动类型": "W", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "返回设备类型编辑结果", "数据移动类型": "X", "数据组": "设备类型编辑结果", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型停用", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型停用按钮", "子过程": [{"子过程描述": "输入设备类型停用指令", "数据移动类型": "E", "数据组": "设备类型停用指令", "数据属性": "设备类型停用参数", "CFP": 1}, {"子过程描述": "停用设备类型", "数据移动类型": "W", "数据组": "设备类型停用信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、停用时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型启用", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型启用按钮", "子过程": [{"子过程描述": "输入设备类型启用指令", "数据移动类型": "E", "数据组": "设备类型启用指令", "数据属性": "设备类型启用参数", "CFP": 1}, {"子过程描述": "读取设备类型信息", "数据移动类型": "R", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "启用设备类型", "数据移动类型": "W", "数据组": "设备类型启用信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、启用结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "设备类型删除", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击设备类型删除按钮", "子过程": [{"子过程描述": "输入设备类型删除指令", "数据移动类型": "E", "数据组": "设备类型删除指令", "数据属性": "设备类型删除参数", "CFP": 1}, {"子过程描述": "读取设备类型信息", "数据移动类型": "R", "数据组": "设备类型信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口", "CFP": 1}, {"子过程描述": "删除设备类型", "数据移动类型": "W", "数据组": "设备类型删除信息", "数据属性": "设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、删除结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "监控信息配置查看", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击监控信息配置查询按钮", "子过程": [{"子过程描述": "输入监控信息配置查询条件", "数据移动类型": "E", "数据组": "监控信息配置查询请求", "数据属性": "监控信息配置查询条件", "CFP": 1}, {"子过程描述": "读取监控信息配置", "数据移动类型": "R", "数据组": "监控信息配置", "数据属性": "监控方式、监控信息", "CFP": 1}, {"子过程描述": "返回监控信息配置查询结果", "数据移动类型": "X", "数据组": "监控信息配置查询结果", "数据属性": "监控方式、监控信息、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "设备类型管理", "功能过程": "监控信息配置", "功能描述": "配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-设备类型管理模块", "触发事件": "用户点击监控信息配置保存按钮", "子过程": [{"子过程描述": "输入监控信息配置", "数据移动类型": "E", "数据组": "监控信息配置", "数据属性": "监控方式、监控信息", "CFP": 1}, {"子过程描述": "保存监控信息配置", "数据移动类型": "W", "数据组": "监控信息配置", "数据属性": "监控方式、监控信息", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "密码设备集群列表", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击密码设备集群列表", "子过程": [{"子过程描述": "输入密码设备集群列表查询条件", "数据移动类型": "E", "数据组": "密码设备集群列表查询请求", "数据属性": "密码设备集群列表查询条件、密码设备集群列表查询项", "CFP": 1}, {"子过程描述": "读取密码设备集群列表", "数据移动类型": "R", "数据组": "密码设备集群列表", "数据属性": "密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、密码设备集群列表数量", "CFP": 1}, {"子过程描述": "返回密码设备集群列表查询结果展示", "数据移动类型": "X", "数据组": "密码设备集群列表查询结果", "数据属性": "密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、密码设备集群列表数量、分页数", "CFP": 1}, {"子过程描述": "保存密码设备集群列表查询记录", "数据移动类型": "W", "数据组": "密码设备集群列表查询记录", "数据属性": "密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "密码设备集群新增", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击密码设备集群新增", "子过程": [{"子过程描述": "输入密码设备集群新增信息", "数据移动类型": "E", "数据组": "密码设备集群新增信息", "数据属性": "密码设备集群新增ID、密码设备集群新增名称、密码设备集群新增类型", "CFP": 1}, {"子过程描述": "密码设备集群重复性校验", "数据移动类型": "R", "数据组": "密码设备集群校验信息", "数据属性": "密码设备集群名称、主键、非空校验、判重、密码设备集群类型", "CFP": 1}, {"子过程描述": "密码设备集群新增入库", "数据移动类型": "W", "数据组": "密码设备集群新增入库信息", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示密码设备集群新增内容", "数据移动类型": "X", "数据组": "密码设备集群新增内容", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、新增结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "密码设备集群编辑", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击密码设备集群编辑", "子过程": [{"子过程描述": "输入密码设备集群编辑信息", "数据移动类型": "E", "数据组": "密码设备集群编辑条件", "数据属性": "密码设备集群ID、密码设备集群编辑项", "CFP": 1}, {"子过程描述": "获取密码设备集群编辑项", "数据移动类型": "R", "数据组": "密码设备集群编辑项", "数据属性": "密码设备集群约束条件、密码设备集群名称、密码设备集群数量、密码设备集群ID", "CFP": 1}, {"子过程描述": "密码设备集群编辑保存", "数据移动类型": "W", "数据组": "密码设备集群编辑结果", "数据属性": "编辑时间、密码设备集群名称、密码设备集群类型、密码设备集群ID", "CFP": 1}, {"子过程描述": "输出密码设备集群编辑结果", "数据移动类型": "X", "数据组": "密码设备集群编辑结果展示信息", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、编辑内容、编辑结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "密码设备集群删除", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击密码设备集群删除", "子过程": [{"子过程描述": "发起密码设备集群删除请求", "数据移动类型": "E", "数据组": "密码设备集群删除请求", "数据属性": "密码设备集群删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码设备集群是否可删除", "数据移动类型": "R", "数据组": "密码设备集群删除权限", "数据属性": "密码设备集群名称、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "密码设备集群删除保存", "数据移动类型": "W", "数据组": "密码设备集群删除结果", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码设备集群删除内容", "数据移动类型": "X", "数据组": "密码设备集群删除内容", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "绑定密码设备", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击绑定密码设备", "子过程": [{"子过程描述": "输入绑定密码设备指令", "数据移动类型": "E", "数据组": "绑定密码设备指令", "数据属性": "绑定密码设备请求", "CFP": 1}, {"子过程描述": "获取需要绑定的密码设备", "数据移动类型": "R", "数据组": "密码设备", "数据属性": "密码设备名称、密码设备类型、密码设备ID", "CFP": 1}, {"子过程描述": "获取密码设备集群", "数据移动类型": "R", "数据组": "密码设备集群", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID", "CFP": 1}, {"子过程描述": "判断是否需要进行保护密钥的创建和同步", "数据移动类型": "R", "数据组": "密码设备集群配置", "数据属性": "是否需要进行保护密钥的创建和同步", "CFP": 1}, {"子过程描述": "绑定密码设备", "数据移动类型": "W", "数据组": "绑定密码设备结果", "数据属性": "绑定密码设备名称、绑定密码设备类型、绑定密码设备ID、操作人", "CFP": 1}, {"子过程描述": "返回展示绑定密码设备结果", "数据移动类型": "X", "数据组": "绑定密码设备结果展示信息", "数据属性": "绑定密码设备名称、绑定密码设备类型、绑定密码设备ID、绑定时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "密码设备集群管理", "功能过程": "释放密码设备", "功能描述": "释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块", "触发事件": "用户点击释放密码设备", "子过程": [{"子过程描述": "发起释放密码设备请求", "数据移动类型": "E", "数据组": "释放密码设备请求", "数据属性": "释放密码设备参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码设备集群是否可释放", "数据移动类型": "R", "数据组": "释放密码设备权限", "数据属性": "密码设备集群名称、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "释放密码设备", "数据移动类型": "W", "数据组": "释放密码设备结果", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、释放结果", "CFP": 1}, {"子过程描述": "返回展示释放密码设备内容", "数据移动类型": "X", "数据组": "释放密码设备内容", "数据属性": "密码设备集群名称、密码设备集群类型、密码设备集群ID、释放内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机管理", "功能过程": "云密码机列表", "功能描述": "云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：云密码机管理模块", "触发事件": "用户点击云密码机列表菜单", "子过程": [{"子过程描述": "输入云密码机列表查询条件", "数据移动类型": "E", "数据组": "云密码机列表查询请求", "数据属性": "云密码机列表查询条件、云密码机列表查询项", "CFP": 1}, {"子过程描述": "读取云密码机列表", "数据移动类型": "R", "数据组": "云密码机列表", "数据属性": "云密码机名称、云密码机ID、管理IP、管理端口、操作员、操作时间", "CFP": 1}, {"子过程描述": "返回云密码机列表查询结果展示", "数据移动类型": "X", "数据组": "云密码机列表查询结果", "数据属性": "云密码机名称、云密码机ID、管理IP、管理端口、分页数、分页大小、查询时间", "CFP": 1}, {"子过程描述": "保存云密码机列表查询记录", "数据移动类型": "W", "数据组": "云密码机列表查询记录", "数据属性": "云密码机列表查询条件、云密码机列表查询结果、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机管理", "功能过程": "云密码机新建", "功能描述": "云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：云密码机管理模块", "触发事件": "用户点击云密码机新建按钮", "子过程": [{"子过程描述": "输入云密码机信息", "数据移动类型": "E", "数据组": "云密码机新建信息", "数据属性": "云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口", "CFP": 1}, {"子过程描述": "云密码机重复性校验", "数据移动类型": "R", "数据组": "云密码机校验信息", "数据属性": "云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、校验规则", "CFP": 1}, {"子过程描述": "云密码机新建保存", "数据移动类型": "W", "数据组": "云密码机新建结果", "数据属性": "云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、新建结果", "CFP": 1}, {"子过程描述": "返回展示云密码机新建内容", "数据移动类型": "X", "数据组": "云密码机新建内容", "数据属性": "云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、新建内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机管理", "功能过程": "云密码机编辑", "功能描述": "云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：云密码机管理模块", "触发事件": "用户点击云密码机编辑按钮", "子过程": [{"子过程描述": "输入云密码机编辑信息", "数据移动类型": "E", "数据组": "云密码机编辑条件", "数据属性": "云密码机名称、云密码机类型、云密码机ID、编辑项", "CFP": 1}, {"子过程描述": "获取云密码机编辑项", "数据移动类型": "R", "数据组": "云密码机编辑项", "数据属性": "云密码机名称、云密码机类型、云密码机ID、编辑项、编辑项类型", "CFP": 1}, {"子过程描述": "云密码机编辑保存", "数据移动类型": "W", "数据组": "云密码机编辑结果", "数据属性": "云密码机名称、云密码机类型、云密码机ID、编辑结果", "CFP": 1}, {"子过程描述": "返回展示云密码机编辑内容", "数据移动类型": "X", "数据组": "云密码机编辑内容", "数据属性": "云密码机名称、云密码机类型、云密码机ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机管理", "功能过程": "云密码机删除", "功能描述": "云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：云密码机管理模块", "触发事件": "用户点击云密码机删除按钮", "子过程": [{"子过程描述": "发起云密码机删除请求", "数据移动类型": "E", "数据组": "云密码机删除请求", "数据属性": "云密码机删除参数", "CFP": 1}, {"子过程描述": "获取云密码机是否可删除", "数据移动类型": "R", "数据组": "云密码机删除约束", "数据属性": "云密码机名称、云密码机类型、云密码机ID、是否可删除", "CFP": 1}, {"子过程描述": "云密码机删除保存", "数据移动类型": "W", "数据组": "云密码机删除结果", "数据属性": "云密码机名称、云密码机类型、云密码机ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示云密码机删除内容", "数据移动类型": "X", "数据组": "云密码机删除内容", "数据属性": "云密码机名称、云密码机类型、云密码机ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机管理", "功能过程": "云密码机详情", "功能描述": "云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：云密码机管理模块", "触发事件": "用户点击云密码机详情按钮", "子过程": [{"子过程描述": "发起云密码机详情请求", "数据移动类型": "E", "数据组": "云密码机详情请求", "数据属性": "云密码机详情查询条件", "CFP": 1}, {"子过程描述": "获取云密码机详情", "数据移动类型": "R", "数据组": "云密码机详情", "数据属性": "云密码机名称、云密码机类型、云密码机ID、详情", "CFP": 1}, {"子过程描述": "返回展示云密码机详情", "数据移动类型": "X", "数据组": "云密码机详情查询结果", "数据属性": "云密码机名称、云密码机类型、云密码机ID、分页数、分页大小、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机虚机网络管理", "功能过程": "网络配置列表", "功能描述": "配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口", "预估工作量（人天）": "2", "功能用户": "发起者：用户，接收者：云密码机虚机网络管理模块", "触发事件": "用户点击网络配置列表", "子过程": [{"子过程描述": "读取网络配置信息", "数据移动类型": "R", "数据组": "网络配置信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、创建时间", "CFP": 1}, {"子过程描述": "展示网络配置列表", "数据移动类型": "X", "数据组": "网络配置信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、创建时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "云密码机虚机网络管理", "功能过程": "新增虚拟机网络配置", "功能描述": "配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口", "预估工作量（人天）": "6", "功能用户": "发起者：用户，接收者：云密码机虚机网络管理模块", "触发事件": "用户点击新增网络配置", "子过程": [{"子过程描述": "输入新增网络配置信息", "数据移动类型": "E", "数据组": "新增网络配置信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围", "CFP": 1}, {"子过程描述": "读取网络配置信息", "数据移动类型": "R", "数据组": "网络配置信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围", "CFP": 1}, {"子过程描述": "新增网络配置信息", "数据移动类型": "W", "数据组": "新增网络配置信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围", "CFP": 1}, {"子过程描述": "返回新增网络配置结果", "数据移动类型": "X", "数据组": "新增网络配置结果", "数据属性": "新增结果", "CFP": 1}, {"子过程描述": "记录新增网络配置日志", "数据移动类型": "W", "数据组": "新增网络配置日志", "数据属性": "新增结果、新增时间", "CFP": 1}, {"子过程描述": "新增网络配置数据归档", "数据移动类型": "W", "数据组": "新增网络配置归档信息", "数据属性": "网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、归档时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "批量创建虚拟机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击批量创建虚拟机", "子过程": [{"子过程描述": "输入批量创建虚拟机请求", "数据移动类型": "E", "数据组": "虚拟密码机创建请求", "数据属性": "虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "调用云密码机0088标准创建虚拟机", "数据移动类型": "R", "数据组": "虚拟密码机创建请求", "数据属性": "虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "调用云密码机0088标准创建虚拟机", "数据移动类型": "W", "数据组": "虚拟密码机创建结果", "数据属性": "虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "返回批量创建虚拟机结果", "数据移动类型": "X", "数据组": "虚拟密码机创建结果", "数据属性": "虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "获取虚拟机网络配置", "数据移动类型": "R", "数据组": "虚拟密码机网络配置", "数据属性": "虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID", "CFP": 1}, {"子过程描述": "保存虚拟机网络配置", "数据移动类型": "W", "数据组": "虚拟密码机网络配置", "数据属性": "虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID", "CFP": 1}, {"子过程描述": "获取虚拟机资源", "数据移动类型": "R", "数据组": "虚拟密码机资源", "数据属性": "虚拟密码机资源名称、虚拟密码机资源类型、虚拟密码机资源ID", "CFP": 1}, {"子过程描述": "保存虚拟机资源", "数据移动类型": "W", "数据组": "虚拟密码机资源", "数据属性": "虚拟密码机资源名称、虚拟密码机资源类型、虚拟密码机资源ID", "CFP": 1}, {"子过程描述": "获取虚拟机网络", "数据移动类型": "R", "数据组": "虚拟密码机网络", "数据属性": "虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID", "CFP": 1}, {"子过程描述": "保存虚拟机网络", "数据移动类型": "W", "数据组": "虚拟密码机网络", "数据属性": "虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "虚拟密码机列表", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击虚拟密码机列表", "子过程": [{"子过程描述": "输入虚拟密码机列表查询条件", "数据移动类型": "E", "数据组": "虚拟密码机列表查询请求", "数据属性": "虚拟密码机列表查询条件、虚拟密码机列表查询项", "CFP": 1}, {"子过程描述": "读取虚拟密码机列表", "数据移动类型": "R", "数据组": "虚拟密码机列表", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID", "CFP": 1}, {"子过程描述": "返回虚拟密码机列表查询结果展示", "数据移动类型": "X", "数据组": "虚拟密码机列表查询结果", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存虚拟密码机列表查询记录", "数据移动类型": "W", "数据组": "虚拟密码机列表查询记录", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "虚拟密码机列表查询", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击虚拟密码机列表查询", "子过程": [{"子过程描述": "输入虚拟密码机列表查询条件", "数据移动类型": "E", "数据组": "虚拟密码机列表查询条件", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID", "CFP": 1}, {"子过程描述": "获取虚拟密码机列表", "数据移动类型": "R", "数据组": "虚拟密码机列表", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID", "CFP": 1}, {"子过程描述": "返回虚拟密码机列表查询结果", "数据移动类型": "X", "数据组": "虚拟密码机列表查询结果", "数据属性": "虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、分页数、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "创建虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击创建虚拟密码机", "子过程": [{"子过程描述": "输入创建虚拟密码机信息", "数据移动类型": "E", "数据组": "虚拟密码机创建信息", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "调用云密码机0088标准创建虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机创建信息", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "返回创建虚拟密码机结果", "数据移动类型": "X", "数据组": "虚拟密码机创建结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、创建结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "虚拟密码机详情", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击虚拟密码机详情", "子过程": [{"子过程描述": "发起虚拟密码机详情请求", "数据移动类型": "E", "数据组": "虚拟密码机详情请求", "数据属性": "虚拟密码机详情查询条件", "CFP": 1}, {"子过程描述": "获取虚拟密码机详情", "数据移动类型": "R", "数据组": "虚拟密码机详情", "数据属性": "虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID", "CFP": 1}, {"子过程描述": "返回虚拟密码机详情", "数据移动类型": "X", "数据组": "虚拟密码机详情查询结果", "数据属性": "虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID、分页数、查询时间", "CFP": 1}, {"子过程描述": "保存虚拟密码机详情", "数据移动类型": "W", "数据组": "虚拟密码机详情", "数据属性": "虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID", "CFP": 1}, {"子过程描述": "更新虚拟密码机详情", "数据移动类型": "W", "数据组": "虚拟密码机详情", "数据属性": "虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "编辑虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击编辑虚拟密码机", "子过程": [{"子过程描述": "发起编辑虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机编辑请求", "数据属性": "虚拟密码机编辑参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机编辑项", "数据移动类型": "R", "数据组": "虚拟密码机编辑项", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "编辑虚拟密码机名称、连接密码", "数据移动类型": "E", "数据组": "虚拟密码机编辑内容", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "动态下发虚拟密码机名称、连接密码", "数据移动类型": "X", "数据组": "虚拟密码机编辑内容", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "删除虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击删除虚拟密码机", "子过程": [{"子过程描述": "发起删除虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机删除请求", "数据属性": "虚拟密码机删除参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "删除虚拟密码机", "数据移动类型": "W", "数据组": "虚拟密码机删除结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、删除结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "启动虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击启动虚拟密码机", "子过程": [{"子过程描述": "发起启动虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机启动请求", "数据属性": "虚拟密码机启动参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "启动虚拟密码机", "数据移动类型": "W", "数据组": "虚拟密码机启动结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、启动结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "停止虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击停止虚拟密码机", "子过程": [{"子过程描述": "发起停止虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机停止请求", "数据属性": "虚拟密码机停止参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "停止虚拟密码机", "数据移动类型": "W", "数据组": "虚拟密码机停止结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、停止结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "重启虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击重启虚拟密码机", "子过程": [{"子过程描述": "发起重启虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机重启请求", "数据属性": "虚拟密码机重启参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "重启虚拟密码机", "数据移动类型": "W", "数据组": "虚拟密码机重启结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、重启结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "强制删除虚拟密码机", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击强制删除虚拟密码机", "子过程": [{"子过程描述": "发起强制删除虚拟密码机请求", "数据移动类型": "E", "数据组": "虚拟密码机强制删除请求", "数据属性": "虚拟密码机强制删除参数", "CFP": 1}, {"子过程描述": "获取虚拟密码机", "数据移动类型": "R", "数据组": "虚拟密码机", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID", "CFP": 1}, {"子过程描述": "强制删除虚拟密码机", "数据移动类型": "W", "数据组": "虚拟密码机强制删除结果", "数据属性": "虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、强制删除结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "生成虚机影像", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击生成虚机影像", "子过程": [{"子过程描述": "发起生成虚机影像请求", "数据移动类型": "E", "数据组": "虚机影像生成请求", "数据属性": "虚机影像生成参数", "CFP": 1}, {"子过程描述": "获取虚机影像生成信息", "数据移动类型": "R", "数据组": "虚机影像生成信息", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID", "CFP": 1}, {"子过程描述": "生成虚机影像", "数据移动类型": "W", "数据组": "虚机影像生成结果", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID、生成结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "下载虚机影像", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击下载虚机影像", "子过程": [{"子过程描述": "发起下载虚机影像请求", "数据移动类型": "E", "数据组": "虚机影像下载请求", "数据属性": "虚机影像下载参数", "CFP": 1}, {"子过程描述": "获取虚机影像", "数据移动类型": "R", "数据组": "虚机影像", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID", "CFP": 1}, {"子过程描述": "下载虚机影像", "数据移动类型": "X", "数据组": "虚机影像下载结果", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID、下载结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "虚拟密码机管理", "功能过程": "导入虚机影像", "功能描述": "导入虚机影像，还原虚机影像", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：虚拟密码机", "触发事件": "用户点击导入虚机影像", "子过程": [{"子过程描述": "发起导入虚机影像请求", "数据移动类型": "E", "数据组": "虚机影像导入请求", "数据属性": "虚机影像导入参数", "CFP": 1}, {"子过程描述": "获取虚机影像", "数据移动类型": "R", "数据组": "虚机影像", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID", "CFP": 1}, {"子过程描述": "导入虚机影像", "数据移动类型": "W", "数据组": "虚机影像导入结果", "数据属性": "虚机影像名称、虚机影像类型、虚机影像ID、导入结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "物理密码机列表", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机列表页", "子过程": [{"子过程描述": "输入物理密码机列表查询条件", "数据移动类型": "E", "数据组": "物理密码机列表查询请求", "数据属性": "物理密码机列表查询条件、物理密码机列表查询项", "CFP": 1}, {"子过程描述": "读取物理密码机列表", "数据移动类型": "R", "数据组": "物理密码机列表", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回物理密码机列表查询结果展示", "数据移动类型": "X", "数据组": "物理密码机列表查询结果", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、分页数", "CFP": 1}, {"子过程描述": "保存物理密码机列表查询记录", "数据移动类型": "W", "数据组": "物理密码机列表查询记录", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "物理密码机新建", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机新建按钮", "子过程": [{"子过程描述": "输入物理密码机新建信息", "数据移动类型": "E", "数据组": "物理密码机新建信息", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "物理密码机重复性校验", "数据移动类型": "R", "数据组": "物理密码机校验信息", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "物理密码机新建保存", "数据移动类型": "W", "数据组": "物理密码机新建结果", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回展示物理密码机新建内容", "数据移动类型": "X", "数据组": "物理密码机新建内容", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "记录物理密码机新建日志", "数据移动类型": "W", "数据组": "物理密码机新建日志", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "物理密码机编辑", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机编辑按钮", "子过程": [{"子过程描述": "输入物理密码机编辑信息", "数据移动类型": "E", "数据组": "物理密码机编辑条件", "数据属性": "物理密码机ID、物理密码机编辑项", "CFP": 1}, {"子过程描述": "获取物理密码机编辑项", "数据移动类型": "R", "数据组": "物理密码机编辑项", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "物理密码机编辑保存", "数据移动类型": "W", "数据组": "物理密码机编辑结果", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回展示物理密码机编辑内容", "数据移动类型": "X", "数据组": "物理密码机编辑内容", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "物理密码机删除", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机删除按钮", "子过程": [{"子过程描述": "发起物理密码机删除请求", "数据移动类型": "E", "数据组": "物理密码机删除请求", "数据属性": "物理密码机删除参数", "CFP": 1}, {"子过程描述": "获取物理密码机", "数据移动类型": "R", "数据组": "物理密码机", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "物理密码机删除保存", "数据移动类型": "W", "数据组": "物理密码机删除结果", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回展示物理密码机删除内容", "数据移动类型": "X", "数据组": "物理密码机删除内容", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "物理密码机详情", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机详情按钮", "子过程": [{"子过程描述": "发起物理密码机详情请求", "数据移动类型": "E", "数据组": "物理密码机详情请求", "数据属性": "物理密码机详情参数", "CFP": 1}, {"子过程描述": "获取物理密码机详情", "数据移动类型": "R", "数据组": "物理密码机详情", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回展示物理密码机详情", "数据移动类型": "X", "数据组": "物理密码机详情展示信息", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "保存物理密码机详情", "数据移动类型": "W", "数据组": "物理密码机详情", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "强制删除", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机强制删除按钮", "子过程": [{"子过程描述": "发起物理密码机强制删除请求", "数据移动类型": "E", "数据组": "物理密码机强制删除请求", "数据属性": "物理密码机强制删除参数", "CFP": 1}, {"子过程描述": "获取物理密码机", "数据移动类型": "R", "数据组": "物理密码机", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "物理密码机强制删除保存", "数据移动类型": "W", "数据组": "物理密码机强制删除结果", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}, {"子过程描述": "返回展示物理密码机强制删除内容", "数据移动类型": "X", "数据组": "物理密码机强制删除内容", "数据属性": "物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "物理密码机管理", "功能过程": "管理页面跳转", "功能描述": "支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码资产数据管理-物理密码机管理模块", "触发事件": "用户点击物理密码机管理页面跳转按钮", "子过程": [{"子过程描述": "发起物理密码机管理页面跳转请求", "数据移动类型": "E", "数据组": "物理密码机管理页面跳转请求", "数据属性": "物理密码机管理页面跳转参数", "CFP": 1}, {"子过程描述": "获取物理密码机管理页面跳转地址", "数据移动类型": "R", "数据组": "物理密码机管理页面跳转地址", "数据属性": "物理密码机管理页面跳转地址", "CFP": 1}, {"子过程描述": "物理密码机管理页面跳转", "数据移动类型": "X", "数据组": "物理密码机管理页面跳转结果", "数据属性": "物理密码机管理页面跳转结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "保护主密钥管理", "功能过程": "保护主密钥创建", "功能描述": "还原设备内保护主密钥", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-保护主密钥模块", "触发事件": "用户在保护主密钥管理页面点击保护主密钥创建按钮", "子过程": [{"子过程描述": "输入保护主密钥创建信息", "数据移动类型": "E", "数据组": "保护主密钥创建信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "获取设备信息", "数据移动类型": "R", "数据组": "设备信息", "数据属性": "设备名称、设备类型、设备ID", "CFP": 1}, {"子过程描述": "生成保护主密钥", "数据移动类型": "X", "数据组": "保护主密钥生成信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、生成时间", "CFP": 1}, {"子过程描述": "保存保护主密钥", "数据移动类型": "W", "数据组": "保护主密钥信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、设备ID、生成结果", "CFP": 1}, {"子过程描述": "返回展示保护主密钥创建结果", "数据移动类型": "X", "数据组": "保护主密钥创建结果", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、创建结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "保护主密钥管理", "功能过程": "保护主密钥同步", "功能描述": "还原设备内保护主密钥", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-保护主密钥模块", "触发事件": "用户在保护主密钥管理页面点击保护主密钥同步按钮", "子过程": [{"子过程描述": "输入保护主密钥同步信息", "数据移动类型": "E", "数据组": "保护主密钥同步信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "获取设备信息", "数据移动类型": "R", "数据组": "设备信息", "数据属性": "设备名称、设备类型、设备ID", "CFP": 1}, {"子过程描述": "获取保护主密钥", "数据移动类型": "R", "数据组": "保护主密钥信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "保护主密钥同步", "数据移动类型": "X", "数据组": "保护主密钥同步信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、同步时间", "CFP": 1}, {"子过程描述": "记录保护主密钥同步信息", "数据移动类型": "W", "数据组": "保护主密钥同步记录", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "保护主密钥管理", "功能过程": "保护主密钥备份", "功能描述": "还原设备内保护主密钥", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-保护主密钥模块", "触发事件": "用户在保护主密钥管理页面点击保护主密钥备份按钮", "子过程": [{"子过程描述": "输入保护主密钥备份信息", "数据移动类型": "E", "数据组": "保护主密钥备份信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、备份类型", "CFP": 1}, {"子过程描述": "获取保护主密钥", "数据移动类型": "R", "数据组": "保护主密钥信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "生成保护主密钥备份文件", "数据移动类型": "X", "数据组": "保护主密钥备份文件", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、文件格式、文件大小", "CFP": 1}, {"子过程描述": "保存保护主密钥备份记录", "数据移动类型": "W", "数据组": "保护主密钥备份记录", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、备份结果", "CFP": 1}, {"子过程描述": "返回展示保护主密钥备份结果", "数据移动类型": "X", "数据组": "保护主密钥备份结果", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、备份结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码资产数据管理", "三级功能模块": "保护主密钥管理", "功能过程": "保护主密钥还原", "功能描述": "还原设备内保护主密钥", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密服平台-保护主密钥模块", "触发事件": "用户在保护主密钥管理页面点击保护主密钥还原按钮", "子过程": [{"子过程描述": "输入保护主密钥还原信息", "数据移动类型": "E", "数据组": "保护主密钥还原信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "获取保护主密钥", "数据移动类型": "R", "数据组": "保护主密钥信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID", "CFP": 1}, {"子过程描述": "保护主密钥还原", "数据移动类型": "X", "数据组": "保护主密钥还原信息", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、还原时间", "CFP": 1}, {"子过程描述": "更新保护主密钥状态", "数据移动类型": "W", "数据组": "保护主密钥状态", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、主密钥状态", "CFP": 1}, {"子过程描述": "返回展示保护主密钥还原结果", "数据移动类型": "X", "数据组": "保护主密钥还原结果", "数据属性": "保护主密钥名称、保护主密钥类型、保护主密钥ID、还原结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "用户证书管理", "功能过程": "用户证书导入", "功能描述": "删除用户证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在用户证书管理页面点击用户证书导入", "子过程": [{"子过程描述": "输入用户证书导入信息", "数据移动类型": "E", "数据组": "用户证书导入信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "读取用户证书", "数据移动类型": "R", "数据组": "用户证书", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "用户证书重复性校验", "数据移动类型": "R", "数据组": "用户证书约束", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "用户证书入库", "数据移动类型": "W", "数据组": "用户证书入库信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、入库时间", "CFP": 1}, {"子过程描述": "返回展示用户证书导入结果", "数据移动类型": "X", "数据组": "用户证书导入结果", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、导入结果", "CFP": 1}, {"子过程描述": "记录用户证书导入日志", "数据移动类型": "W", "数据组": "用户证书导入日志", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、系统时间、操作人", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "用户证书管理", "功能过程": "用户证书列表", "功能描述": "删除用户证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在用户证书管理页面点击用户证书列表", "子过程": [{"子过程描述": "输入用户证书列表查询条件", "数据移动类型": "E", "数据组": "用户证书列表查询请求", "数据属性": "用户证书列表查询条件、用户证书列表查询项", "CFP": 1}, {"子过程描述": "读取用户证书列表", "数据移动类型": "R", "数据组": "用户证书列表", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "返回用户证书列表查询结果展示", "数据移动类型": "X", "数据组": "用户证书列表查询结果", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "用户证书管理", "功能过程": "用户证书停用", "功能描述": "删除用户证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在用户证书管理页面点击用户证书停用", "子过程": [{"子过程描述": "发起用户证书停用请求", "数据移动类型": "E", "数据组": "用户证书停用请求", "数据属性": "用户证书停用参数", "CFP": 1}, {"子过程描述": "获取用户证书停用信息", "数据移动类型": "R", "数据组": "用户证书停用信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "用户证书停用保存", "数据移动类型": "W", "数据组": "用户证书停用结果", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、停用结果", "CFP": 1}, {"子过程描述": "返回展示用户证书停用结果", "数据移动类型": "X", "数据组": "用户证书停用结果展示信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、停用内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "用户证书管理", "功能过程": "用户证书启用", "功能描述": "删除用户证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在用户证书管理页面点击用户证书启用", "子过程": [{"子过程描述": "发起用户证书启用请求", "数据移动类型": "E", "数据组": "用户证书启用请求", "数据属性": "用户证书启用参数", "CFP": 1}, {"子过程描述": "获取用户证书启用信息", "数据移动类型": "R", "数据组": "用户证书启用信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "用户证书启用保存", "数据移动类型": "W", "数据组": "用户证书启用结果", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、启用结果", "CFP": 1}, {"子过程描述": "返回展示用户证书启用结果", "数据移动类型": "X", "数据组": "用户证书启用结果展示信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、启用内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "用户证书管理", "功能过程": "用户证书删除", "功能描述": "删除用户证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在用户证书管理页面点击用户证书删除", "子过程": [{"子过程描述": "发起用户证书删除请求", "数据移动类型": "E", "数据组": "用户证书删除请求", "数据属性": "用户证书删除参数", "CFP": 1}, {"子过程描述": "获取用户证书删除信息", "数据移动类型": "R", "数据组": "用户证书删除信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件", "CFP": 1}, {"子过程描述": "用户证书删除保存", "数据移动类型": "W", "数据组": "用户证书删除结果", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、删除结果", "CFP": 1}, {"子过程描述": "返回展示用户证书删除结果", "数据移动类型": "X", "数据组": "用户证书删除结果展示信息", "数据属性": "用户证书名称、用户证书类型、用户证书ID、用户证书文件、删除内容", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书创建", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书创建按钮", "子过程": [{"子过程描述": "输入应用证书创建信息", "数据移动类型": "E", "数据组": "应用证书创建信息", "数据属性": "应用证书名称、应用证书类型、应用证书ID", "CFP": 1}, {"子过程描述": "应用证书重复性校验", "数据移动类型": "R", "数据组": "应用证书约束条件", "数据属性": "应用证书名称、应用证书类型、应用证书ID、非空校验、判重规则", "CFP": 1}, {"子过程描述": "应用证书创建入库", "数据移动类型": "W", "数据组": "应用证书创建结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、创建结果", "CFP": 1}, {"子过程描述": "应用证书创建结果数据生成", "数据移动类型": "R", "数据组": "应用证书创建内容", "数据属性": "应用证书名称、应用证书类型、应用证书ID、创建时间", "CFP": 1}, {"子过程描述": "返回展示应用证书创建内容", "数据移动类型": "X", "数据组": "应用证书创建内容", "数据属性": "应用证书名称、应用证书类型、应用证书ID、创建时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "下载应用证书证书请求", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击下载应用证书证书请求按钮", "子过程": [{"子过程描述": "发起应用证书证书请求下载指令", "数据移动类型": "E", "数据组": "应用证书证书请求下载指令", "数据属性": "应用证书证书请求下载指令", "CFP": 1}, {"子过程描述": "获取应用证书证书请求", "数据移动类型": "R", "数据组": "应用证书证书请求", "数据属性": "应用证书证书请求名称、应用证书证书请求类型、应用证书证书请求ID", "CFP": 1}, {"子过程描述": "应用证书证书请求文件生成", "数据移动类型": "X", "数据组": "应用证书证书请求下载内容", "数据属性": "应用证书证书请求名称、应用证书证书请求类型、应用证书证书请求ID、文件格式", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书导入", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书导入按钮", "子过程": [{"子过程描述": "发起应用证书导入请求", "数据移动类型": "E", "数据组": "应用证书导入请求", "数据属性": "应用证书名称、应用证书类型、应用证书ID", "CFP": 1}, {"子过程描述": "获取应用证书请求", "数据移动类型": "R", "数据组": "应用证书请求", "数据属性": "应用证书名称、应用证书类型、应用证书ID", "CFP": 1}, {"子过程描述": "应用证书导入保存", "数据移动类型": "W", "数据组": "应用证书导入结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、导入结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "导入应用证书和密钥", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击导入应用证书和密钥按钮", "子过程": [{"子过程描述": "输入导入应用证书和密钥信息", "数据移动类型": "E", "数据组": "应用证书和密钥信息", "数据属性": "签名证书、证书口令、加密证书、加密私钥", "CFP": 1}, {"子过程描述": "应用证书和密钥重复性校验", "数据移动类型": "R", "数据组": "应用证书和密钥约束条件", "数据属性": "签名证书、证书口令、加密证书、加密私钥、非空校验、判重规则", "CFP": 1}, {"子过程描述": "应用证书和密钥导入保存", "数据移动类型": "W", "数据组": "应用证书和密钥入库信息", "数据属性": "签名证书、证书口令、加密证书、加密私钥、导入结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书列表查询", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书列表查询按钮", "子过程": [{"子过程描述": "输入应用证书列表查询条件", "数据移动类型": "E", "数据组": "应用证书列表查询请求", "数据属性": "应用证书列表查询条件、应用证书列表查询项", "CFP": 1}, {"子过程描述": "读取应用证书列表", "数据移动类型": "R", "数据组": "应用证书列表", "数据属性": "应用证书名称、应用证书类型、应用证书ID", "CFP": 1}, {"子过程描述": "返回应用证书列表查询结果展示", "数据移动类型": "X", "数据组": "应用证书列表查询结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、查询时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书停用", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书停用按钮", "子过程": [{"子过程描述": "发起应用证书停用请求", "数据移动类型": "E", "数据组": "应用证书停用请求", "数据属性": "应用证书停用参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断应用证书是否可停用", "数据移动类型": "R", "数据组": "应用证书停用权限", "数据属性": "应用证书名称、应用证书类型、应用证书ID、操作员权限", "CFP": 1}, {"子过程描述": "应用证书停用保存", "数据移动类型": "W", "数据组": "应用证书停用结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、停用结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书启用", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书启用按钮", "子过程": [{"子过程描述": "发起应用证书启用请求", "数据移动类型": "E", "数据组": "应用证书启用请求", "数据属性": "应用证书启用参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断应用证书是否可启用", "数据移动类型": "R", "数据组": "应用证书启用权限", "数据属性": "应用证书名称、应用证书类型、应用证书ID、操作员权限", "CFP": 1}, {"子过程描述": "应用证书启用保存", "数据移动类型": "W", "数据组": "应用证书启用结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、启用结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码产品证书及编号管理", "三级功能模块": "应用证书管理", "功能过程": "应用证书删除", "功能描述": "删除应用证书", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在应用证书管理页面点击应用证书删除按钮", "子过程": [{"子过程描述": "发起应用证书删除请求", "数据移动类型": "E", "数据组": "应用证书删除请求", "数据属性": "应用证书删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断应用证书是否可删除", "数据移动类型": "R", "数据组": "应用证书删除权限", "数据属性": "应用证书名称、应用证书类型、应用证书ID、操作员权限", "CFP": 1}, {"子过程描述": "应用证书删除保存", "数据移动类型": "W", "数据组": "应用证书删除结果", "数据属性": "应用证书名称、应用证书类型、应用证书ID、删除结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "新增密钥", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击新增密钥按钮", "子过程": [{"子过程描述": "输入新增密钥信息", "数据移动类型": "E", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "校验密钥信息", "数据移动类型": "R", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "生成密钥", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "返回新增密钥结果", "数据移动类型": "X", "数据组": "新增密钥结果", "数据属性": "新增密钥结果", "CFP": 1}, {"子过程描述": "记录新增密钥日志", "数据移动类型": "W", "数据组": "新增密钥日志", "数据属性": "新增密钥结果、新增时间", "CFP": 1}, {"子过程描述": "新增密钥入库", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "新增密钥同步", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "新增密钥数据归档", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "新增密钥数据备份", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "新增密钥数据同步", "数据移动类型": "W", "数据组": "密钥信息", "数据属性": "密钥ID、密钥名称、密钥类型、密钥算法、密钥长度", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥信息列表", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥信息列表", "子过程": [{"子过程描述": "输入密钥信息列表查询条件", "数据移动类型": "E", "数据组": "密钥信息列表查询请求", "数据属性": "页码、单页数量", "CFP": 1}, {"子过程描述": "获取密钥信息列表", "数据移动类型": "R", "数据组": "密钥信息列表", "数据属性": "应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "返回展示密钥信息列表", "数据移动类型": "X", "数据组": "密钥信息列表查询结果", "数据属性": "应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度、页码、单页数量、总条数", "CFP": 1}, {"子过程描述": "保存密钥信息列表查询记录", "数据移动类型": "W", "数据组": "密钥信息列表查询记录", "数据属性": "应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥查询", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥查询", "子过程": [{"子过程描述": "输入密钥查询条件", "数据移动类型": "E", "数据组": "密钥查询请求", "数据属性": "应用名称、密钥id、密钥名称", "CFP": 1}, {"子过程描述": "获取密钥查询结果", "数据移动类型": "R", "数据组": "密钥查询结果", "数据属性": "应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度", "CFP": 1}, {"子过程描述": "返回展示密钥查询结果", "数据移动类型": "X", "数据组": "密钥查询结果", "数据属性": "应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥详情", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥详情", "子过程": [{"子过程描述": "发起密钥详情请求", "数据移动类型": "E", "数据组": "密钥详情请求", "数据属性": "密钥详情请求", "CFP": 1}, {"子过程描述": "获取密钥详情", "数据移动类型": "R", "数据组": "密钥详情", "数据属性": "密钥摘要值、密钥来源、密钥用途、是否可导出", "CFP": 1}, {"子过程描述": "返回展示密钥详情", "数据移动类型": "X", "数据组": "密钥详情", "数据属性": "密钥摘要值、密钥来源、密钥用途、是否可导出", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥链接", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥链接", "子过程": [{"子过程描述": "发起密钥链接请求", "数据移动类型": "E", "数据组": "密钥链接请求", "数据属性": "密钥链接请求", "CFP": 1}, {"子过程描述": "获取密钥链接", "数据移动类型": "R", "数据组": "密钥链接", "数据属性": "密钥链接类型、密钥链接ID、密钥链接名称", "CFP": 1}, {"子过程描述": "返回展示密钥链接", "数据移动类型": "X", "数据组": "密钥链接", "数据属性": "密钥链接类型、密钥链接ID、密钥链接名称", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥历史版本", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥历史版本", "子过程": [{"子过程描述": "发起密钥历史版本请求", "数据移动类型": "E", "数据组": "密钥历史版本请求", "数据属性": "密钥历史版本请求", "CFP": 1}, {"子过程描述": "获取密钥历史版本", "数据移动类型": "R", "数据组": "密钥历史版本", "数据属性": "密钥历史版本号、密钥历史版本类型、密钥历史版本ID", "CFP": 1}, {"子过程描述": "返回展示密钥历史版本", "数据移动类型": "X", "数据组": "密钥历史版本", "数据属性": "密钥历史版本号、密钥历史版本类型、密钥历史版本ID", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥翻新", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥翻新", "子过程": [{"子过程描述": "发起密钥翻新请求", "数据移动类型": "E", "数据组": "密钥翻新请求", "数据属性": "密钥翻新请求", "CFP": 1}, {"子过程描述": "获取密钥翻新结果", "数据移动类型": "R", "数据组": "密钥翻新结果", "数据属性": "密钥翻新结果", "CFP": 1}, {"子过程描述": "返回展示密钥翻新结果", "数据移动类型": "X", "数据组": "密钥翻新结果", "数据属性": "密钥翻新结果", "CFP": 1}, {"子过程描述": "保存密钥翻新结果", "数据移动类型": "W", "数据组": "密钥翻新结果", "数据属性": "密钥翻新结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥自动翻新", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥自动翻新", "子过程": [{"子过程描述": "发起密钥自动翻新请求", "数据移动类型": "E", "数据组": "密钥自动翻新请求", "数据属性": "密钥自动翻新请求", "CFP": 1}, {"子过程描述": "获取密钥自动翻新结果", "数据移动类型": "R", "数据组": "密钥自动翻新结果", "数据属性": "密钥自动翻新结果", "CFP": 1}, {"子过程描述": "返回展示密钥自动翻新结果", "数据移动类型": "X", "数据组": "密钥自动翻新结果", "数据属性": "密钥自动翻新结果", "CFP": 1}, {"子过程描述": "保存密钥自动翻新结果", "数据移动类型": "W", "数据组": "密钥自动翻新结果", "数据属性": "密钥自动翻新结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥归档", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥归档", "子过程": [{"子过程描述": "发起密钥归档请求", "数据移动类型": "E", "数据组": "密钥归档请求", "数据属性": "密钥归档请求", "CFP": 1}, {"子过程描述": "获取密钥归档结果", "数据移动类型": "R", "数据组": "密钥归档结果", "数据属性": "密钥归档结果", "CFP": 1}, {"子过程描述": "返回展示密钥归档结果", "数据移动类型": "X", "数据组": "密钥归档结果", "数据属性": "密钥归档结果", "CFP": 1}, {"子过程描述": "保存密钥归档结果", "数据移动类型": "W", "数据组": "密钥归档结果", "数据属性": "密钥归档结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥恢复", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥恢复", "子过程": [{"子过程描述": "发起密钥恢复请求", "数据移动类型": "E", "数据组": "密钥恢复请求", "数据属性": "密钥恢复请求", "CFP": 1}, {"子过程描述": "获取密钥恢复结果", "数据移动类型": "R", "数据组": "密钥恢复结果", "数据属性": "密钥恢复结果", "CFP": 1}, {"子过程描述": "返回展示密钥恢复结果", "数据移动类型": "X", "数据组": "密钥恢复结果", "数据属性": "密钥恢复结果", "CFP": 1}, {"子过程描述": "保存密钥恢复结果", "数据移动类型": "W", "数据组": "密钥恢复结果", "数据属性": "密钥恢复结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥注销", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥注销", "子过程": [{"子过程描述": "发起密钥注销请求", "数据移动类型": "E", "数据组": "密钥注销请求", "数据属性": "密钥注销请求", "CFP": 1}, {"子过程描述": "获取密钥注销结果", "数据移动类型": "R", "数据组": "密钥注销结果", "数据属性": "密钥注销结果", "CFP": 1}, {"子过程描述": "返回展示密钥注销结果", "数据移动类型": "X", "数据组": "密钥注销结果", "数据属性": "密钥注销结果", "CFP": 1}, {"子过程描述": "保存密钥注销结果", "数据移动类型": "W", "数据组": "密钥注销结果", "数据属性": "密钥注销结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥销毁", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥销毁", "子过程": [{"子过程描述": "发起密钥销毁请求", "数据移动类型": "E", "数据组": "密钥销毁请求", "数据属性": "密钥销毁请求", "CFP": 1}, {"子过程描述": "获取密钥销毁结果", "数据移动类型": "R", "数据组": "密钥销毁结果", "数据属性": "密钥销毁结果", "CFP": 1}, {"子过程描述": "返回展示密钥销毁结果", "数据移动类型": "X", "数据组": "密钥销毁结果", "数据属性": "密钥销毁结果", "CFP": 1}, {"子过程描述": "保存密钥销毁结果", "数据移动类型": "W", "数据组": "密钥销毁结果", "数据属性": "密钥销毁结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密钥信息管理", "三级功能模块": "密钥及生命周期管理", "功能过程": "密钥删除", "功能描述": "将密钥做删除处理", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台-密钥及生命周期管理模块", "触发事件": "用户在密钥及生命周期管理页面点击密钥删除", "子过程": [{"子过程描述": "发起密钥删除请求", "数据移动类型": "E", "数据组": "密钥删除请求", "数据属性": "密钥删除请求", "CFP": 1}, {"子过程描述": "获取密钥删除结果", "数据移动类型": "R", "数据组": "密钥删除结果", "数据属性": "密钥删除结果", "CFP": 1}, {"子过程描述": "返回展示密钥删除结果", "数据移动类型": "X", "数据组": "密钥删除结果", "数据属性": "密钥删除结果", "CFP": 1}, {"子过程描述": "保存密钥删除结果", "数据移动类型": "W", "数据组": "密钥删除结果", "数据属性": "密钥删除结果", "CFP": 1}]}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "添加密码知识库数据", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击添加密码知识库数据", "子过程": [{"子过程描述": "输入密码知识库数据", "数据移动类型": "E", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID", "CFP": 1}, {"子过程描述": "密码知识库数据重复性校验", "数据移动类型": "R", "数据组": "密码知识库约束", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、非空校验、判重、密码知识库数量限制", "CFP": 1}, {"子过程描述": "密码知识库数据保存", "数据移动类型": "W", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、操作人、系统时间", "CFP": 1}, {"子过程描述": "返回展示密码知识库数据", "数据移动类型": "X", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、操作结果", "CFP": 1}], "功能点个数": 4}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "编辑密码知识库数据", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击编辑密码知识库数据", "子过程": [{"子过程描述": "输入密码知识库数据", "数据移动类型": "E", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID", "CFP": 1}, {"子过程描述": "密码知识库数据重复性校验", "数据移动类型": "R", "数据组": "密码知识库约束", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、非空校验、判重、密码知识库数量限制", "CFP": 1}, {"子过程描述": "密码知识库数据更新", "数据移动类型": "W", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、更新时间", "CFP": 1}, {"子过程描述": "返回展示密码知识库数据", "数据移动类型": "X", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、更新结果", "CFP": 1}], "功能点个数": 4}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "删除密码知识库数据", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击删除密码知识库数据", "子过程": [{"子过程描述": "发起密码知识库数据删除请求", "数据移动类型": "E", "数据组": "密码知识库数据删除请求", "数据属性": "密码知识库数据删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码知识库数据是否可删除", "数据移动类型": "R", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、操作员权限", "CFP": 1}, {"子过程描述": "密码知识库数据删除保存", "数据移动类型": "W", "数据组": "密码知识库数据删除结果", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、删除人、系统时间", "CFP": 1}, {"子过程描述": "返回展示密码知识库数据删除内容", "数据移动类型": "X", "数据组": "密码知识库数据删除内容", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、删除内容", "CFP": 1}], "功能点个数": 4}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "查询密码知识库数据", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击查询密码知识库数据", "子过程": [{"子过程描述": "输入密码知识库数据查询条件", "数据移动类型": "E", "数据组": "密码知识库数据查询请求", "数据属性": "密码知识库数据查询条件、密码知识库数据查询项", "CFP": 1}, {"子过程描述": "读取密码知识库数据", "数据移动类型": "R", "数据组": "密码知识库数据", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID", "CFP": 1}, {"子过程描述": "返回密码知识库数据查询结果展示", "数据移动类型": "X", "数据组": "密码知识库数据查询结果", "数据属性": "密码知识库名称、密码知识库类型、密码知识库ID、查询时间", "CFP": 1}], "功能点个数": 3}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "显示/隐藏知识库信息", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击显示/隐藏知识库信息", "子过程": [{"子过程描述": "输入显示/隐藏知识库信息", "数据移动类型": "E", "数据组": "显示/隐藏知识库信息", "数据属性": "显示/隐藏知识库信息", "CFP": 1}, {"子过程描述": "更新显示/隐藏知识库信息", "数据移动类型": "W", "数据组": "显示/隐藏知识库信息", "数据属性": "显示/隐藏知识库信息", "CFP": 1}, {"子过程描述": "返回显示/隐藏知识库信息", "数据移动类型": "X", "数据组": "显示/隐藏知识库信息", "数据属性": "显示/隐藏知识库信息", "CFP": 1}], "功能点个数": 3}, {"一级功能模块": "密码资产数据管理", "二级功能模块": "密码文档信息管理", "三级功能模块": "密码文档信息管理", "功能过程": "预览知识库信息", "功能描述": "点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码资产数据管理-密码文档信息管理模块", "触发事件": "用户点击预览知识库信息", "子过程": [{"子过程描述": "发起知识库信息预览请求", "数据移动类型": "E", "数据组": "知识库信息预览请求", "数据属性": "知识库信息预览请求", "CFP": 1}, {"子过程描述": "获取知识库信息预览内容", "数据移动类型": "R", "数据组": "知识库信息预览内容", "数据属性": "知识库信息名称、知识库信息类型、知识库信息ID", "CFP": 1}, {"子过程描述": "返回知识库信息预览结果", "数据移动类型": "X", "数据组": "知识库信息预览结果", "数据属性": "知识库信息名称、知识库信息类型、知识库信息ID、预览结果", "CFP": 1}], "功能点个数": 3}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "密码应用测评改造阶段分页列表", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评改造阶段分页列表", "子过程": [{"子过程描述": "输入密码应用测评改造阶段分页信息", "数据移动类型": "E", "数据组": "密码应用测评改造阶段分页信息", "数据属性": "页码、单页数量", "CFP": 1}, {"子过程描述": "获取密码应用测评改造阶段列表", "数据移动类型": "R", "数据组": "密码应用测评改造阶段信息", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "返回密码应用测评改造阶段分页列表展示", "数据移动类型": "X", "数据组": "密码应用测评改造阶段分页列表", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码、页码、单页数量", "CFP": 1}, {"子过程描述": "保存密码应用测评改造阶段分页列表", "数据移动类型": "W", "数据组": "密码应用测评改造阶段分页列表", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码、页码、单页数量", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "密码应用测评改造阶段过滤查询", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评改造阶段过滤查询", "子过程": [{"子过程描述": "输入密码应用测评改造阶段过滤信息", "数据移动类型": "E", "数据组": "密码应用测评改造阶段过滤信息", "数据属性": "密码应用测评改造阶段编码、密码应用测评改造阶段名称", "CFP": 1}, {"子过程描述": "获取密码应用测评改造阶段列表", "数据移动类型": "R", "数据组": "密码应用测评改造阶段信息", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "返回密码应用测评改造阶段过滤查询结果", "数据移动类型": "X", "数据组": "密码应用测评改造阶段过滤查询结果", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "新增密码应用测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击新增密码应用测评改造阶段", "子过程": [{"子过程描述": "输入新增密码应用测评改造阶段信息", "数据移动类型": "E", "数据组": "新增密码应用测评改造阶段信息", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "密码应用测评改造阶段重复性校验", "数据移动类型": "R", "数据组": "密码应用测评改造阶段约束条件", "数据属性": "密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码、非空校验、判重、密码应用测评改造阶段名称长度", "CFP": 1}, {"子过程描述": "新增密码应用测评改造阶段入库", "数据移动类型": "W", "数据组": "新增密码应用测评改造阶段入库信息", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码、新增时间", "CFP": 1}, {"子过程描述": "返回展示新增密码应用测评改造阶段", "数据移动类型": "X", "数据组": "新增密码应用测评改造阶段", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "编辑密码应用测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击编辑密码应用测评改造阶段", "子过程": [{"子过程描述": "输入编辑密码应用测评改造阶段信息", "数据移动类型": "E", "数据组": "编辑密码应用测评改造阶段信息", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "获取密码应用测评改造阶段", "数据移动类型": "R", "数据组": "密码应用测评改造阶段", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "密码应用测评改造阶段编辑保存", "数据移动类型": "W", "数据组": "密码应用测评改造阶段编辑结果", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码、编辑人", "CFP": 1}, {"子过程描述": "返回展示编辑密码应用测评改造阶段", "数据移动类型": "X", "数据组": "密码应用测评改造阶段编辑内容", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "删除密码应用测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击删除密码应用测评改造阶段", "子过程": [{"子过程描述": "发起删除密码应用测评改造阶段请求", "数据移动类型": "E", "数据组": "密码应用测评改造阶段删除请求", "数据属性": "密码应用测评改造阶段ID、删除人", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码应用测评改造阶段是否可删除", "数据移动类型": "R", "数据组": "密码应用测评改造阶段删除权限", "数据属性": "密码应用测评改造阶段ID、操作员权限、操作员ID", "CFP": 1}, {"子过程描述": "密码应用测评改造阶段删除保存", "数据移动类型": "W", "数据组": "密码应用测评改造阶段删除结果", "数据属性": "密码应用测评改造阶段ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示删除后的密码应用测评改造阶段", "数据移动类型": "X", "数据组": "密码应用测评改造阶段删除内容", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "密码应用设置测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用设置测评改造阶段", "子过程": [{"子过程描述": "发起密码应用设置测评改造阶段请求", "数据移动类型": "E", "数据组": "密码应用设置测评改造阶段请求", "数据属性": "密码应用设置测评改造阶段ID", "CFP": 1}, {"子过程描述": "获取密码应用设置测评改造阶段", "数据移动类型": "R", "数据组": "密码应用设置测评改造阶段", "数据属性": "密码应用设置测评改造阶段ID、密码应用设置测评改造阶段名称、密码应用设置测评改造阶段类型、密码应用设置测评改造阶段编码", "CFP": 1}, {"子过程描述": "密码应用设置测评改造阶段设置保存", "数据移动类型": "W", "数据组": "密码应用设置测评改造阶段设置结果", "数据属性": "密码应用设置测评改造阶段ID、密码应用设置测评改造阶段名称、密码应用设置测评改造阶段类型、密码应用设置测评改造阶段编码、设置结果", "CFP": 1}, {"子过程描述": "返回展示密码应用设置测评改造阶段设置内容", "数据移动类型": "X", "数据组": "密码应用设置测评改造阶段设置内容", "数据属性": "密码应用设置测评改造阶段ID、密码应用设置测评改造阶段名称、密码应用设置测评改造阶段类型、密码应用设置测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "密码应用修改测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用修改测评改造阶段", "子过程": [{"子过程描述": "发起密码应用修改测评改造阶段请求", "数据移动类型": "E", "数据组": "密码应用修改测评改造阶段请求", "数据属性": "密码应用修改测评改造阶段ID", "CFP": 1}, {"子过程描述": "获取密码应用修改测评改造阶段", "数据移动类型": "R", "数据组": "密码应用修改测评改造阶段", "数据属性": "密码应用修改测评改造阶段ID、密码应用修改测评改造阶段名称、密码应用修改测评改造阶段类型、密码应用修改测评改造阶段编码", "CFP": 1}, {"子过程描述": "密码应用修改测评改造阶段修改保存", "数据移动类型": "W", "数据组": "密码应用修改测评改造阶段修改结果", "数据属性": "密码应用修改测评改造阶段ID、密码应用修改测评改造阶段名称、密码应用修改测评改造阶段类型、密码应用修改测评改造阶段编码、修改结果", "CFP": 1}, {"子过程描述": "返回展示密码应用修改测评改造阶段修改内容", "数据移动类型": "X", "数据组": "密码应用修改测评改造阶段修改内容", "数据属性": "密码应用修改测评改造阶段ID、密码应用修改测评改造阶段名称、密码应用修改测评改造阶段类型、密码应用修改测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "查询密码应用测评改造阶段", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击查询密码应用测评改造阶段", "子过程": [{"子过程描述": "发起密码应用测评改造阶段查询请求", "数据移动类型": "E", "数据组": "密码应用测评改造阶段查询请求", "数据属性": "密码应用测评改造阶段查询条件", "CFP": 1}, {"子过程描述": "获取密码应用测评改造阶段", "数据移动类型": "R", "数据组": "密码应用测评改造阶段", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}, {"子过程描述": "返回密码应用测评改造阶段查询结果展示", "数据移动类型": "X", "数据组": "密码应用测评改造阶段查询结果", "数据属性": "密码应用测评改造阶段ID、密码应用测评改造阶段名称、密码应用测评改造阶段类型、密码应用测评改造阶段编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "改造阶段管理", "三级功能模块": "密码应用测评管理", "功能过程": "测评改造阶段的应用分布", "功能描述": "根据测评改造阶段查询包含的应用数量", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击测评改造阶段的应用分布", "子过程": [{"子过程描述": "输入测评改造阶段的应用分布查询条件", "数据移动类型": "E", "数据组": "测评改造阶段的应用分布查询请求", "数据属性": "测评改造阶段的应用分布查询条件", "CFP": 1}, {"子过程描述": "读取测评改造阶段的应用分布", "数据移动类型": "R", "数据组": "测评改造阶段的应用分布", "数据属性": "测评改造阶段的应用分布ID、测评改造阶段的应用分布名称", "CFP": 1}, {"子过程描述": "返回展示测评改造阶段的应用分布", "数据移动类型": "X", "数据组": "测评改造阶段的应用分布查询结果", "数据属性": "测评改造阶段的应用分布ID、测评改造阶段的应用分布名称、测评改造阶段的应用分布类型、测评改造阶段的应用分布编码", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "应用测评报告分页列表查询", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击分页查询", "子过程": [{"子过程描述": "输入应用测评报告分页列表查询条件", "数据移动类型": "E", "数据组": "应用测评报告分页列表查询请求", "数据属性": "应用测评报告分页列表查询条件、应用测评报告分页列表查询项", "CFP": 1}, {"子过程描述": "读取应用测评报告分页列表", "数据移动类型": "R", "数据组": "应用测评报告分页列表", "数据属性": "应用测评报告分页列表名称、应用测评报告分页列表类型、应用测评报告分页列表ID、分页数、单页数量", "CFP": 1}, {"子过程描述": "返回应用测评报告分页列表查询结果展示", "数据移动类型": "X", "数据组": "应用测评报告分页列表查询结果", "数据属性": "应用测评报告分页列表名称、应用测评报告分页列表类型、应用测评报告分页列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存应用测评报告分页列表查询记录", "数据移动类型": "W", "数据组": "应用测评报告分页列表查询记录", "数据属性": "应用测评报告分页列表名称、应用测评报告分页列表类型、应用测评报告分页列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "新增应用测评报告对象", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击新增按钮", "子过程": [{"子过程描述": "输入新增应用测评报告对象信息", "数据移动类型": "E", "数据组": "应用测评报告新增信息", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID", "CFP": 1}, {"子过程描述": "应用测评报告对象重复性校验", "数据移动类型": "R", "数据组": "应用测评报告约束条件", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、非空校验、判重、应用测评报告数量", "CFP": 1}, {"子过程描述": "新增应用测评报告对象入库", "数据移动类型": "W", "数据组": "应用测评报告新增入库信息", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示新增的应用测评报告对象", "数据移动类型": "X", "数据组": "应用测评报告新增内容", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、新增结果", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "应用测评报告文件上传", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击上传按钮", "子过程": [{"子过程描述": "发起应用测评报告文件上传请求", "数据移动类型": "E", "数据组": "应用测评报告文件上传请求", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、目的路径", "CFP": 1}, {"子过程描述": "应用测评报告文件上传", "数据移动类型": "X", "数据组": "应用测评报告文件上传内容", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、上传结果", "CFP": 1}, {"子过程描述": "应用测评报告文件上传结果回写", "数据移动类型": "W", "数据组": "应用测评报告文件上传结果", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、上传时间", "CFP": 1}, {"子过程描述": "返回展示应用测评报告文件上传结果", "数据移动类型": "X", "数据组": "应用测评报告文件上传结果展示信息", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、上传内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "应用测评报告文件预览", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击预览按钮", "子过程": [{"子过程描述": "发起应用测评报告文件预览请求", "数据移动类型": "E", "数据组": "应用测评报告文件预览请求", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID", "CFP": 1}, {"子过程描述": "获取应用测评报告文件预览信息", "数据移动类型": "R", "数据组": "应用测评报告文件预览信息", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、预览方式", "CFP": 1}, {"子过程描述": "返回应用测评报告文件预览内容", "数据移动类型": "X", "数据组": "应用测评报告文件预览内容", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、预览内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "应用测评报告文件下载", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击下载按钮", "子过程": [{"子过程描述": "发起应用测评报告文件下载请求", "数据移动类型": "E", "数据组": "应用测评报告文件下载请求", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID", "CFP": 1}, {"子过程描述": "获取应用测评报告文件下载信息", "数据移动类型": "R", "数据组": "应用测评报告文件下载信息", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、下载方式", "CFP": 1}, {"子过程描述": "应用测评报告文件下载", "数据移动类型": "X", "数据组": "应用测评报告文件下载内容", "数据属性": "应用测评报告文件名称、应用测评报告文件类型、应用测评报告文件ID、下载内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "编辑应用测评报告对象", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击编辑按钮", "子过程": [{"子过程描述": "输入编辑应用测评报告对象信息", "数据移动类型": "E", "数据组": "应用测评报告编辑条件", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、编辑项", "CFP": 1}, {"子过程描述": "应用测评报告对象编辑保存", "数据移动类型": "W", "数据组": "应用测评报告编辑结果", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、编辑结果", "CFP": 1}, {"子过程描述": "返回展示编辑的应用测评报告对象", "数据移动类型": "X", "数据组": "应用测评报告编辑内容", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "应用测评报告、测评分数管理", "三级功能模块": "应用测评管理", "功能过程": "删除应用测评报告对象", "功能描述": "删除测评报告数据和对应文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用测评管理系统", "触发事件": "用户在应用测评报告列表页面点击删除按钮", "子过程": [{"子过程描述": "发起应用测评报告对象删除请求", "数据移动类型": "E", "数据组": "应用测评报告删除请求", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID", "CFP": 1}, {"子过程描述": "应用测评报告对象删除保存", "数据移动类型": "W", "数据组": "应用测评报告删除结果", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示删除的应用测评报告对象", "数据移动类型": "X", "数据组": "应用测评报告删除内容", "数据属性": "应用测评报告名称、应用测评报告类型、应用测评报告ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评方案管理", "功能过程": "密码应用测评方案分页列表", "功能描述": "为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。\n", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用测评管理模块", "触发事件": "用户点击密码应用测评方案分页列表", "子过程": [{"子过程描述": "输入密码应用测评方案分页列表查询条件", "数据移动类型": "E", "数据组": "密码应用测评方案分页列表查询请求", "数据属性": "密码应用测评方案分页列表查询条件、密码应用测评方案分页列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用测评方案分页列表", "数据移动类型": "R", "数据组": "密码应用测评方案分页列表", "数据属性": "密码应用测评方案分页列表名称、密码应用测评方案分页列表类型、密码应用测评方案分页列表ID", "CFP": 1}, {"子过程描述": "返回密码应用测评方案分页列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用测评方案分页列表查询结果", "数据属性": "密码应用测评方案分页列表名称、密码应用测评方案分页列表类型、密码应用测评方案分页列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码应用测评方案分页列表查询记录", "数据移动类型": "W", "数据组": "密码应用测评方案分页列表查询记录", "数据属性": "密码应用测评方案分页列表名称、密码应用测评方案分页列表类型、密码应用测评方案分页列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评方案管理", "功能过程": "新建密码应用测评方案", "功能描述": "为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。\n", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用测评管理模块", "触发事件": "用户点击新增密码应用测评方案", "子过程": [{"子过程描述": "输入密码应用测评方案新增信息", "数据移动类型": "E", "数据组": "密码应用测评方案新增信息", "数据属性": "密码应用测评方案名称、密码应用测评方案类型、密码应用测评方案ID", "CFP": 1}, {"子过程描述": "密码应用测评方案重复性校验", "数据移动类型": "R", "数据组": "密码应用测评方案校验信息", "数据属性": "密码应用测评方案名称、密码应用测评方案类型、密码应用测评方案ID、非空校验、判重校验、密码应用测评方案名称长度校验、密码应用测评方案名称是否包含敏感词", "CFP": 1}, {"子过程描述": "密码应用测评方案新增入库", "数据移动类型": "W", "数据组": "密码应用测评方案新增入库信息", "数据属性": "密码应用测评方案名称、密码应用测评方案类型、密码应用测评方案ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示密码应用测评方案新增内容", "数据移动类型": "X", "数据组": "密码应用测评方案新增内容", "数据属性": "密码应用测评方案名称、密码应用测评方案类型、密码应用测评方案ID、新增结果", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评模板管理", "功能过程": "绑定密码应用测评进度模板", "功能描述": "编辑密码应用测评方案对象绑定的测评要求模板", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码应用测评进度模板绑定页面点击绑定按钮", "子过程": [{"子过程描述": "输入密码应用测评进度模板绑定信息", "数据移动类型": "E", "数据组": "密码应用测评进度模板绑定信息", "数据属性": "密码应用测评进度模板绑定ID、密码应用测评进度模板绑定名称", "CFP": 1}, {"子过程描述": "获取密码应用测评进度模板", "数据移动类型": "R", "数据组": "密码应用测评进度模板", "数据属性": "密码应用测评进度模板ID、密码应用测评进度模板名称", "CFP": 1}, {"子过程描述": "密码应用测评进度模板绑定保存", "数据移动类型": "W", "数据组": "密码应用测评进度模板绑定结果", "数据属性": "密码应用测评进度模板绑定结果", "CFP": 1}, {"子过程描述": "返回展示密码应用测评进度模板绑定结果", "数据移动类型": "X", "数据组": "密码应用测评进度模板绑定结果", "数据属性": "密码应用测评进度模板绑定结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评模板管理", "功能过程": "绑定密码应用测评要求模板", "功能描述": "编辑密码应用测评方案对象绑定的测评要求模板", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码应用测评要求模板绑定页面点击绑定按钮", "子过程": [{"子过程描述": "输入密码应用测评要求模板绑定信息", "数据移动类型": "E", "数据组": "密码应用测评要求模板绑定信息", "数据属性": "密码应用测评要求模板绑定ID、密码应用测评要求模板绑定名称", "CFP": 1}, {"子过程描述": "获取密码应用测评要求模板", "数据移动类型": "R", "数据组": "密码应用测评要求模板", "数据属性": "密码应用测评要求模板ID、密码应用测评要求模板名称", "CFP": 1}, {"子过程描述": "密码应用测评要求模板绑定保存", "数据移动类型": "W", "数据组": "密码应用测评要求模板绑定结果", "数据属性": "密码应用测评要求模板绑定结果", "CFP": 1}, {"子过程描述": "返回展示密码应用测评要求模板绑定结果", "数据移动类型": "X", "数据组": "密码应用测评要求模板绑定结果", "数据属性": "密码应用测评要求模板绑定结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评模板管理", "功能过程": "密码应用测评进度模板编辑", "功能描述": "编辑密码应用测评方案对象绑定的测评要求模板", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码应用测评进度模板编辑页面点击编辑按钮", "子过程": [{"子过程描述": "输入密码应用测评进度模板编辑信息", "数据移动类型": "E", "数据组": "密码应用测评进度模板编辑条件", "数据属性": "密码应用测评进度模板编辑项", "CFP": 1}, {"子过程描述": "获取密码应用测评进度模板编辑项", "数据移动类型": "R", "数据组": "密码应用测评进度模板编辑项", "数据属性": "密码应用测评进度模板约束条件、密码应用测评进度模板名称、密码应用测评进度模板类型、密码应用测评进度模板ID", "CFP": 1}, {"子过程描述": "密码应用测评进度模板编辑保存", "数据移动类型": "W", "数据组": "密码应用测评进度模板编辑结果", "数据属性": "密码应用测评进度模板编辑结果", "CFP": 1}, {"子过程描述": "返回展示密码应用测评进度模板编辑结果", "数据移动类型": "X", "数据组": "密码应用测评进度模板编辑结果", "数据属性": "密码应用测评进度模板编辑结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "应用测评模板管理", "功能过程": "密码应用测评要求模板编辑", "功能描述": "编辑密码应用测评方案对象绑定的测评要求模板", "预估工作量（人天）": "3", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密码应用测评要求模板编辑页面点击编辑按钮", "子过程": [{"子过程描述": "输入密码应用测评要求模板编辑信息", "数据移动类型": "E", "数据组": "密码应用测评要求模板编辑条件", "数据属性": "密码应用测评要求模板编辑项", "CFP": 1}, {"子过程描述": "获取密码应用测评要求模板编辑项", "数据移动类型": "R", "数据组": "密码应用测评要求模板编辑项", "数据属性": "密码应用测评要求模板约束条件、密码应用测评要求模板名称、密码应用测评要求模板类型、密码应用测评要求模板ID", "CFP": 1}, {"子过程描述": "密码应用测评要求模板编辑保存", "数据移动类型": "W", "数据组": "密码应用测评要求模板编辑结果", "数据属性": "密码应用测评要求模板编辑结果", "CFP": 1}, {"子过程描述": "返回展示密码应用测评要求模板编辑结果", "数据移动类型": "X", "数据组": "密码应用测评要求模板编辑结果", "数据属性": "密码应用测评要求模板编辑结果", "CFP": 1}], "预估工作量": 3}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密评进度推进管理", "功能过程": "密码应用测评要求推进", "功能描述": "测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务平台-密评进度推进模块", "触发事件": "用户在密评进度推进页面点击测评要求推进按钮", "子过程": [{"子过程描述": "输入测评要求", "数据移动类型": "E", "数据组": "测评要求", "数据属性": "测评要求ID、测评要求名称、测评要求类型", "CFP": 1}, {"子过程描述": "获取测评要求", "数据移动类型": "R", "数据组": "测评要求", "数据属性": "测评要求ID、测评要求名称、测评要求类型", "CFP": 1}, {"子过程描述": "获取测评要求调研选项", "数据移动类型": "R", "数据组": "测评要求调研选项", "数据属性": "测评要求调研选项ID、测评要求调研选项名称", "CFP": 1}, {"子过程描述": "获取测评要求改进建议", "数据移动类型": "R", "数据组": "测评要求改进建议", "数据属性": "测评要求改进建议ID、测评要求改进建议名称", "CFP": 1}, {"子过程描述": "测评要求是否满足", "数据移动类型": "W", "数据组": "测评结果", "数据属性": "测评要求ID、测评要求名称、是否满足", "CFP": 1}, {"子过程描述": "测评要求满足情况", "数据移动类型": "X", "数据组": "测评结果", "数据属性": "测评要求ID、测评要求名称、是否满足", "CFP": 1}, {"子过程描述": "保存测评要求", "数据移动类型": "W", "数据组": "测评要求", "数据属性": "测评要求ID、测评要求名称、测评要求类型", "CFP": 1}], "功能过程ID": "1"}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密评进度推进管理", "功能过程": "密码应用测评进度推进", "功能描述": "测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。", "预估工作量（人天）": "5", "功能用户": "发起者：用户，接收者：密码服务平台-密评进度推进模块", "触发事件": "用户在密评进度推进页面点击测评进度推进按钮", "子过程": [{"子过程描述": "输入测评进度", "数据移动类型": "E", "数据组": "测评进度", "数据属性": "测评进度ID、测评进度名称", "CFP": 1}, {"子过程描述": "获取测评进度", "数据移动类型": "R", "数据组": "测评进度", "数据属性": "测评进度ID、测评进度名称", "CFP": 1}, {"子过程描述": "测评进度更新", "数据移动类型": "W", "数据组": "测评进度", "数据属性": "测评进度ID、测评进度名称", "CFP": 1}, {"子过程描述": "测评进度更新结果", "数据移动类型": "X", "数据组": "测评进度", "数据属性": "测评进度ID、测评进度名称", "CFP": 1}, {"子过程描述": "测评进度更新记录", "数据移动类型": "W", "数据组": "测评进度更新记录", "数据属性": "测评进度更新记录ID、测评进度更新记录名称", "CFP": 1}], "功能过程ID": "2"}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密评进度跟踪报告管理", "功能过程": "密码应用测评进度跟踪报告展示", "功能描述": "下载密码应用测评进度的跟踪报告文件", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码应用测评管理模块", "触发事件": "用户点击密码应用测评进度跟踪报告展示", "子过程": [{"子过程描述": "读取密码应用测评进度跟踪报告模板数据", "数据移动类型": "R", "数据组": "密码应用测评进度跟踪报告模板数据", "数据属性": "密码应用测评进度跟踪报告模板ID、密码应用测评进度跟踪报告模板名称、密码应用测评进度跟踪报告模板类型", "CFP": 1}, {"子过程描述": "生成密码应用测评进度跟踪报告列表", "数据移动类型": "X", "数据组": "密码应用测评进度跟踪报告列表", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告数量", "CFP": 1}, {"子过程描述": "编辑密码应用测评进度跟踪报告列表", "数据移动类型": "E", "数据组": "密码应用测评进度跟踪报告列表", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告数量", "CFP": 1}, {"子过程描述": "保存密码应用测评进度跟踪报告列表", "数据移动类型": "W", "数据组": "密码应用测评进度跟踪报告列表", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告数量", "CFP": 1}, {"子过程描述": "返回展示密码应用测评进度跟踪报告列表", "数据移动类型": "X", "数据组": "密码应用测评进度跟踪报告列表", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告数量", "CFP": 1}], "预估工作量": 4}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密评进度跟踪报告管理", "功能过程": "密码应用测评进度跟踪报告编辑", "功能描述": "下载密码应用测评进度的跟踪报告文件", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码应用测评管理模块", "触发事件": "用户点击密码应用测评进度跟踪报告编辑", "子过程": [{"子过程描述": "输入密码应用测评进度跟踪报告编辑请求", "数据移动类型": "E", "数据组": "密码应用测评进度跟踪报告编辑请求", "数据属性": "密码应用测评进度跟踪报告编辑参数", "CFP": 1}, {"子过程描述": "获取密码应用测评进度跟踪报告编辑信息", "数据移动类型": "R", "数据组": "密码应用测评进度跟踪报告编辑信息", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称", "CFP": 1}, {"子过程描述": "密码应用测评进度跟踪报告编辑保存", "数据移动类型": "W", "数据组": "密码应用测评进度跟踪报告编辑结果", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、编辑时间", "CFP": 1}, {"子过程描述": "返回展示密码应用测评进度跟踪报告编辑内容", "数据移动类型": "X", "数据组": "密码应用测评进度跟踪报告编辑内容", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、编辑内容", "CFP": 1}, {"子过程描述": "记录密码应用测评进度跟踪报告编辑日志", "数据移动类型": "W", "数据组": "密码应用测评进度跟踪报告编辑日志", "数据属性": "密码应用测评进度跟踪报告ID、密码应用测评进度跟踪报告名称、操作人", "CFP": 1}], "预估工作量": 5}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密评进度跟踪报告管理", "功能过程": "密码应用测评进度跟踪报告下载", "功能描述": "下载密码应用测评进度的跟踪报告文件", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码应用测评管理模块", "触发事件": "用户点击密码应用测评进度跟踪报告下载", "子过程": [{"子过程描述": "发起密码应用测评进度跟踪报告下载请求", "数据移动类型": "E", "数据组": "密码应用测评进度跟踪报告下载请求", "数据属性": "密码应用测评进度跟踪报告下载指令", "CFP": 1}, {"子过程描述": "获取密码应用测评进度跟踪报告", "数据移动类型": "R", "数据组": "密码应用测评进度跟踪报告下载内容", "数据属性": "密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告大小", "CFP": 1}, {"子过程描述": "密码应用测评进度跟踪报告下载", "数据移动类型": "X", "数据组": "密码应用测评进度跟踪报告下载结果", "数据属性": "密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告大小、下载时间", "CFP": 1}, {"子过程描述": "记录密码应用测评进度跟踪报告下载记录", "数据移动类型": "W", "数据组": "密码应用测评进度跟踪报告下载记录", "数据属性": "密码应用测评进度跟踪报告名称、密码应用测评进度跟踪报告类型、密码应用测评进度跟踪报告大小、下载人", "CFP": 1}], "预估工作量": 4}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密码应用测评差距管理", "功能过程": "密码应用测评差距分析内容展示", "功能描述": "导出下载密码应用测评过程中当前的差距分析报告文件", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评差距分析内容展示", "子过程": [{"子过程描述": "输入密码应用测评差距分析内容查询条件", "数据移动类型": "E", "数据组": "密码应用测评差距分析内容查询请求", "数据属性": "密码应用测评差距分析内容查询条件、密码应用测评差距分析内容查询项", "CFP": 1}, {"子过程描述": "读取密码应用测评差距分析内容", "数据移动类型": "R", "数据组": "密码应用测评差距分析内容", "数据属性": "密码应用测评差距分析内容名称、密码应用测评差距分析内容类型、密码应用测评差距分析内容ID", "CFP": 1}, {"子过程描述": "密码应用测评差距分析内容分页处理", "数据移动类型": "R", "数据组": "密码应用测评差距分析内容分页数", "数据属性": "密码应用测评差距分析内容分页数、密码应用测评差距分析内容分页大小、密码应用测评差距分析内容查询方式", "CFP": 1}, {"子过程描述": "返回密码应用测评差距分析内容查询结果展示", "数据移动类型": "X", "数据组": "密码应用测评差距分析内容查询结果", "数据属性": "密码应用测评差距分析内容名称、密码应用测评差距分析内容类型、密码应用测评差距分析内容ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码应用测评差距分析内容查询记录", "数据移动类型": "W", "数据组": "密码应用测评差距分析内容查询记录", "数据属性": "密码应用测评差距分析内容名称、密码应用测评差距分析内容类型、密码应用测评差距分析内容ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密码应用测评差距管理", "功能过程": "密码应用测评差距分析内容编辑", "功能描述": "导出下载密码应用测评过程中当前的差距分析报告文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评差距分析内容编辑", "子过程": [{"子过程描述": "输入密码应用测评差距分析内容编辑信息", "数据移动类型": "E", "数据组": "密码应用测评差距分析内容编辑信息", "数据属性": "密码应用测评差距分析内容编辑ID、密码应用测评差距分析内容编辑项", "CFP": 1}, {"子过程描述": "获取密码应用测评差距分析内容编辑项", "数据移动类型": "R", "数据组": "密码应用测评差距分析内容编辑项", "数据属性": "密码应用测评差距分析内容约束条件、密码应用测评差距分析内容名称、密码应用测评差距分析内容数量、密码应用测评差距分析内容ID", "CFP": 1}, {"子过程描述": "密码应用测评差距分析内容编辑保存", "数据移动类型": "W", "数据组": "密码应用测评差距分析内容编辑结果", "数据属性": "密码应用测评差距分析内容名称、密码应用测评差距分析内容类型、密码应用测评差距分析内容ID、编辑结果", "CFP": 1}, {"子过程描述": "返回展示密码应用测评差距分析内容编辑结果", "数据移动类型": "X", "数据组": "密码应用测评差距分析内容编辑结果", "数据属性": "密码应用测评差距分析内容名称、密码应用测评差距分析内容类型、密码应用测评差距分析内容ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密码应用测评差距管理", "功能过程": "密码应用测评差距分析内容报告生成", "功能描述": "导出下载密码应用测评过程中当前的差距分析报告文件", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评差距分析内容报告生成", "子过程": [{"子过程描述": "输入密码应用测评差距分析内容报告生成指令", "数据移动类型": "E", "数据组": "密码应用测评差距分析内容报告生成指令", "数据属性": "密码应用测评差距分析内容报告生成参数", "CFP": 1}, {"子过程描述": "读取密码应用测评差距分析内容报告生成规则", "数据移动类型": "R", "数据组": "密码应用测评差距分析内容报告生成规则", "数据属性": "密码应用测评差距分析内容报告生成规则名称、密码应用测评差距分析内容报告生成规则类型、密码应用测评差距分析内容报告生成规则ID", "CFP": 1}, {"子过程描述": "密码应用测评差距分析内容报告生成保存", "数据移动类型": "W", "数据组": "密码应用测评差距分析内容报告生成结果", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、生成结果", "CFP": 1}, {"子过程描述": "输出密码应用测评差距分析内容报告生成内容", "数据移动类型": "X", "数据组": "密码应用测评差距分析内容报告生成内容", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、生成内容", "CFP": 1}, {"子过程描述": "记录密码应用测评差距分析内容报告生成日志", "数据移动类型": "W", "数据组": "密码应用测评差距分析内容报告生成日志", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、日志记录时间", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密码应用方案管理", "三级功能模块": "密码应用测评差距管理", "功能过程": "密码应用测评差距分析内容报告导出", "功能描述": "导出下载密码应用测评过程中当前的差距分析报告文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户点击密码应用测评差距分析内容报告导出", "子过程": [{"子过程描述": "发起密码应用测评差距分析内容报告导出请求", "数据移动类型": "E", "数据组": "密码应用测评差距分析内容报告导出请求", "数据属性": "密码应用测评差距分析内容报告导出参数", "CFP": 1}, {"子过程描述": "获取密码应用测评差距分析内容报告导出内容", "数据移动类型": "R", "数据组": "密码应用测评差距分析内容报告导出内容", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、文件格式", "CFP": 1}, {"子过程描述": "密码应用测评差距分析内容报告导出", "数据移动类型": "X", "数据组": "密码应用测评差距分析内容报告导出内容", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、导出时间", "CFP": 1}, {"子过程描述": "记录密码应用测评差距分析内容报告导出结果", "数据移动类型": "W", "数据组": "密码应用测评差距分析内容报告导出结果", "数据属性": "密码应用测评差距分析内容报告名称、密码应用测评差距分析内容报告类型、密码应用测评差距分析内容报告ID、系统时间、导出结果", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密评机构管理", "三级功能模块": "密评机构管理", "功能过程": "密评机构分页列表查询", "功能描述": "在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击密评机构列表菜单", "子过程": [{"子过程描述": "输入密评机构列表查询条件", "数据移动类型": "E", "数据组": "密评机构信息", "数据属性": "页码、单页数量、密评机构名称、密评机构ID", "CFP": 1}, {"子过程描述": "读取密评机构列表", "数据移动类型": "R", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "返回密评机构列表查询结果展示", "数据移动类型": "X", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱、页码、单页数量", "CFP": 1}, {"子过程描述": "保存密评机构列表查询记录", "数据移动类型": "W", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密评机构管理", "三级功能模块": "密评机构管理", "功能过程": "新增密评机构", "功能描述": "在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密评机构列表页面点击新增按钮", "子过程": [{"子过程描述": "输入新增密评机构信息", "数据移动类型": "E", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "判断密评机构名称是否重复", "数据移动类型": "R", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "新增密评机构信息入库", "数据移动类型": "W", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "返回展示新增密评机构信息", "数据移动类型": "X", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密评机构管理", "三级功能模块": "密评机构管理", "功能过程": "编辑密评机构", "功能描述": "在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密评机构列表页面点击编辑按钮", "子过程": [{"子过程描述": "选择要编辑的密评机构", "数据移动类型": "E", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID", "CFP": 1}, {"子过程描述": "输入编辑密评机构信息", "数据移动类型": "E", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "判断密评机构名称是否重复", "数据移动类型": "R", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "编辑密评机构信息入库", "数据移动类型": "W", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}, {"子过程描述": "返回展示编辑密评机构信息", "数据移动类型": "X", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、密评机构类型、密评机构地址、联系人姓名、联系人电话、联系人电子邮箱", "CFP": 1}]}, {"一级功能模块": "密码应用测评管理", "二级功能模块": "密评机构管理", "三级功能模块": "密评机构管理", "功能过程": "删除密评机构", "功能描述": "在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户在密评机构列表页面点击删除按钮", "子过程": [{"子过程描述": "输入删除密评机构指令", "数据移动类型": "E", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID", "CFP": 1}, {"子过程描述": "判断密评机构是否被其他业务引用", "数据移动类型": "R", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、引用情况", "CFP": 1}, {"子过程描述": "删除密评机构", "数据移动类型": "W", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID", "CFP": 1}, {"子过程描述": "返回展示删除密评机构结果", "数据移动类型": "X", "数据组": "密评机构信息", "数据属性": "密评机构名称、密评机构ID、删除结果", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码漏洞/安全事件类型管理", "三级功能模块": "密码漏洞/安全事件类型管理", "功能过程": "密码漏洞/安全事件类型分页列表展示", "功能描述": "系统部署时，初始化默认平台监控的密码漏洞/安全事件类型", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码漏洞/安全事件类型管理", "触发事件": "用户点击密码漏洞/安全事件类型列表", "子过程": [{"子过程描述": "输入密码漏洞/安全事件类型列表查询条件", "数据移动类型": "E", "数据组": "密码漏洞/安全事件类型列表查询请求", "数据属性": "密码漏洞/安全事件类型列表查询条件、密码漏洞/安全事件类型列表查询项", "CFP": 1}, {"子过程描述": "读取密码漏洞/安全事件类型列表", "数据移动类型": "R", "数据组": "密码漏洞/安全事件类型列表", "数据属性": "密码漏洞/安全事件类型列表名称、密码漏洞/安全事件类型列表类型、密码漏洞/安全事件类型列表ID", "CFP": 1}, {"子过程描述": "返回密码漏洞/安全事件类型列表查询结果展示", "数据移动类型": "X", "数据组": "密码漏洞/安全事件类型列表查询结果", "数据属性": "密码漏洞/安全事件类型列表名称、密码漏洞/安全事件类型列表类型、密码漏洞/安全事件类型列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码漏洞/安全事件类型列表查询记录", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型列表查询记录", "数据属性": "密码漏洞/安全事件类型列表名称、密码漏洞/安全事件类型列表类型、密码漏洞/安全事件类型列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码漏洞/安全事件类型管理", "三级功能模块": "密码漏洞/安全事件类型管理", "功能过程": "新增密码漏洞/安全事件类型", "功能描述": "系统部署时，初始化默认平台监控的密码漏洞/安全事件类型", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码漏洞/安全事件类型管理", "触发事件": "用户点击新增密码漏洞/安全事件类型", "子过程": [{"子过程描述": "输入新增密码漏洞/安全事件类型信息", "数据移动类型": "E", "数据组": "密码漏洞/安全事件类型新增信息", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID", "CFP": 1}, {"子过程描述": "密码漏洞/安全事件类型重复性校验", "数据移动类型": "R", "数据组": "密码漏洞/安全事件类型校验信息", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、判重规则", "CFP": 1}, {"子过程描述": "密码漏洞/安全事件类型新增入库", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型新增入库信息", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示密码漏洞/安全事件类型新增内容", "数据移动类型": "X", "数据组": "密码漏洞/安全事件类型新增内容", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、新增结果", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码漏洞/安全事件类型管理", "三级功能模块": "密码漏洞/安全事件类型管理", "功能过程": "编辑密码漏洞/安全事件类型", "功能描述": "系统部署时，初始化默认平台监控的密码漏洞/安全事件类型", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码漏洞/安全事件类型管理", "触发事件": "用户点击编辑密码漏洞/安全事件类型", "子过程": [{"子过程描述": "输入编辑密码漏洞/安全事件类型信息", "数据移动类型": "E", "数据组": "密码漏洞/安全事件类型编辑条件", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID", "CFP": 1}, {"子过程描述": "获取密码漏洞/安全事件类型编辑项", "数据移动类型": "R", "数据组": "密码漏洞/安全事件类型编辑项", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、编辑项", "CFP": 1}, {"子过程描述": "密码漏洞/安全事件类型编辑保存", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型编辑结果", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、编辑结果", "CFP": 1}, {"子过程描述": "返回展示密码漏洞/安全事件类型编辑结果", "数据移动类型": "X", "数据组": "密码漏洞/安全事件类型编辑结果", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码漏洞/安全事件类型管理", "三级功能模块": "密码漏洞/安全事件类型管理", "功能过程": "删除密码漏洞/安全事件类型", "功能描述": "系统部署时，初始化默认平台监控的密码漏洞/安全事件类型", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码漏洞/安全事件类型管理", "触发事件": "用户点击删除密码漏洞/安全事件类型", "子过程": [{"子过程描述": "发起密码漏洞/安全事件类型删除请求", "数据移动类型": "E", "数据组": "密码漏洞/安全事件类型删除请求", "数据属性": "密码漏洞/安全事件类型删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断密码漏洞/安全事件类型是否可删除", "数据移动类型": "R", "数据组": "密码漏洞/安全事件类型删除权限", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、操作员权限", "CFP": 1}, {"子过程描述": "密码漏洞/安全事件类型删除保存", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型删除结果", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示密码漏洞/安全事件类型删除内容", "数据移动类型": "X", "数据组": "密码漏洞/安全事件类型删除内容", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码漏洞/安全事件类型管理", "三级功能模块": "密码漏洞/安全事件类型管理", "功能过程": "初始化密码漏洞/安全事件类型", "功能描述": "系统部署时，初始化默认平台监控的密码漏洞/安全事件类型", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码漏洞/安全事件类型管理", "触发事件": "用户点击初始化密码漏洞/安全事件类型", "子过程": [{"子过程描述": "发起密码漏洞/安全事件类型初始化请求", "数据移动类型": "E", "数据组": "密码漏洞/安全事件类型初始化请求", "数据属性": "密码漏洞/安全事件类型初始化参数", "CFP": 1}, {"子过程描述": "获取密码漏洞/安全事件类型初始化信息", "数据移动类型": "R", "数据组": "密码漏洞/安全事件类型初始化信息", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID", "CFP": 1}, {"子过程描述": "密码漏洞/安全事件类型初始化保存", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型初始化结果", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、初始化结果", "CFP": 1}, {"子过程描述": "返回展示密码漏洞/安全事件类型初始化内容", "数据移动类型": "X", "数据组": "密码漏洞/安全事件类型初始化内容", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、初始化内容", "CFP": 1}, {"子过程描述": "记录密码漏洞/安全事件类型初始化日志", "数据移动类型": "W", "数据组": "密码漏洞/安全事件类型初始化日志", "数据属性": "密码漏洞/安全事件类型名称、密码漏洞/安全事件类型类型、密码漏洞/安全事件类型ID、初始化人", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件管理", "功能过程": "漏洞/安全事件告警分页列表展示", "功能描述": "删除平台监控的漏洞/安全事件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理模块", "触发事件": "用户点击漏洞/安全事件告警分页列表", "子过程": [{"子过程描述": "输入漏洞/安全事件告警分页列表查询条件", "数据移动类型": "E", "数据组": "漏洞/安全事件告警分页列表查询请求", "数据属性": "漏洞/安全事件告警分页列表查询条件、漏洞/安全事件告警分页列表查询项", "CFP": 1}, {"子过程描述": "读取漏洞/安全事件告警分页列表", "数据移动类型": "R", "数据组": "漏洞/安全事件告警分页列表", "数据属性": "漏洞/安全事件告警分页列表名称、漏洞/安全事件告警分页列表类型、漏洞/安全事件告警分页列表ID", "CFP": 1}, {"子过程描述": "返回漏洞/安全事件告警分页列表查询结果", "数据移动类型": "X", "数据组": "漏洞/安全事件告警分页列表查询结果", "数据属性": "漏洞/安全事件告警分页列表名称、漏洞/安全事件告警分页列表类型、漏洞/安全事件告警分页列表ID、查询时间", "CFP": 1}, {"子过程描述": "保存漏洞/安全事件告警分页列表查询记录", "数据移动类型": "W", "数据组": "漏洞/安全事件告警分页列表查询记录", "数据属性": "漏洞/安全事件告警分页列表名称、漏洞/安全事件告警分页列表类型、漏洞/安全事件告警分页列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件管理", "功能过程": "新增漏洞/安全事件告警", "功能描述": "删除平台监控的漏洞/安全事件", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理模块", "触发事件": "用户点击新增漏洞/安全事件告警", "子过程": [{"子过程描述": "输入新增漏洞/安全事件告警信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警新增信息", "数据属性": "漏洞/安全事件告警ID、漏洞/安全事件告警名称", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警重复性校验", "数据移动类型": "R", "数据组": "漏洞/安全事件告警校验信息", "数据属性": "漏洞/安全事件告警名称、主键、非空校验、判重、漏洞/安全事件告警类型", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警新增入库", "数据移动类型": "W", "数据组": "漏洞/安全事件告警新增入库信息", "数据属性": "漏洞/安全事件告警名称、漏洞/安全事件告警类型、漏洞/安全事件告警ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警新增内容", "数据移动类型": "X", "数据组": "漏洞/安全事件告警新增内容", "数据属性": "漏洞/安全事件告警名称、漏洞/安全事件告警类型、漏洞/安全事件告警ID、新增结果", "CFP": 1}, {"子过程描述": "记录漏洞/安全事件告警新增日志", "数据移动类型": "W", "数据组": "漏洞/安全事件告警新增日志", "数据属性": "漏洞/安全事件告警新增操作员名称、漏洞/安全事件告警新增时间、漏洞/安全事件告警新增结果、操作员ID、漏洞/安全事件告警新增内容等", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件管理", "功能过程": "漏洞/安全事件告警启用", "功能描述": "删除平台监控的漏洞/安全事件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理模块", "触发事件": "用户点击漏洞/安全事件告警启用", "子过程": [{"子过程描述": "输入漏洞/安全事件告警启用信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警启用信息", "数据属性": "漏洞/安全事件告警启用ID、漏洞/安全事件告警启用数量", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警启用保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警启用结果", "数据属性": "漏洞/安全事件告警启用时间、漏洞/安全事件告警启用结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警启用结果", "数据移动类型": "X", "数据组": "漏洞/安全事件告警启用结果展示信息", "数据属性": "漏洞/安全事件告警启用时间、漏洞/安全事件告警启用结果、漏洞/安全事件告警启用内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件管理", "功能过程": "漏洞/安全事件告警禁用", "功能描述": "删除平台监控的漏洞/安全事件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理模块", "触发事件": "用户点击漏洞/安全事件告警禁用", "子过程": [{"子过程描述": "输入漏洞/安全事件告警禁用信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警禁用信息", "数据属性": "漏洞/安全事件告警禁用ID、漏洞/安全事件告警禁用数量", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警禁用保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警禁用结果", "数据属性": "漏洞/安全事件告警禁用时间、漏洞/安全事件告警禁用结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警禁用结果", "数据移动类型": "X", "数据组": "漏洞/安全事件告警禁用结果展示信息", "数据属性": "漏洞/安全事件告警禁用时间、漏洞/安全事件告警禁用结果、漏洞/安全事件告警禁用内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件管理", "功能过程": "删除漏洞/安全事件告警", "功能描述": "删除平台监控的漏洞/安全事件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理模块", "触发事件": "用户点击漏洞/安全事件告警删除", "子过程": [{"子过程描述": "发起漏洞/安全事件告警删除请求", "数据移动类型": "E", "数据组": "漏洞/安全事件告警删除请求", "数据属性": "漏洞/安全事件告警删除参数", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警是否可删除", "数据移动类型": "R", "数据组": "漏洞/安全事件告警删除约束", "数据属性": "漏洞/安全事件告警名称、主键、非空校验、判重、漏洞/安全事件告警类型", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警删除保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警删除结果", "数据属性": "漏洞/安全事件告警名称、漏洞/安全事件告警类型、漏洞/安全事件告警ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警删除内容", "数据移动类型": "X", "数据组": "漏洞/安全事件告警删除内容", "数据属性": "漏洞/安全事件告警名称、漏洞/安全事件告警类型、漏洞/安全事件告警ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件通知人管理", "功能过程": "漏洞/安全事件告警通知人列表展示", "功能描述": "删除漏洞/安全事件触发时可通知的人员信息和通知方式", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码服务平台-漏洞/安全事件管理模块", "触发事件": "用户点击漏洞/安全事件告警通知人列表", "子过程": [{"子过程描述": "输入漏洞/安全事件告警通知人列表查询条件", "数据移动类型": "E", "数据组": "漏洞/安全事件告警通知人列表查询请求", "数据属性": "漏洞/安全事件告警通知人列表查询条件、漏洞/安全事件告警通知人列表查询项", "CFP": 1}, {"子过程描述": "读取漏洞/安全事件告警通知人列表", "数据移动类型": "R", "数据组": "漏洞/安全事件告警通知人列表", "数据属性": "漏洞/安全事件告警通知人列表名称、漏洞/安全事件告警通知人列表类型、漏洞/安全事件告警通知人列表ID、漏洞/安全事件告警通知人列表数量", "CFP": 1}, {"子过程描述": "返回漏洞/安全事件告警通知人列表查询结果展示", "数据移动类型": "X", "数据组": "漏洞/安全事件告警通知人列表查询结果", "数据属性": "漏洞/安全事件告警通知人列表名称、漏洞/安全事件告警通知人列表类型、漏洞/安全事件告警通知人列表ID、漏洞/安全事件告警通知人列表数量、分页数", "CFP": 1}, {"子过程描述": "保存漏洞/安全事件告警通知人列表查询记录", "数据移动类型": "W", "数据组": "漏洞/安全事件告警通知人列表查询记录", "数据属性": "漏洞/安全事件告警通知人列表名称、漏洞/安全事件告警通知人列表类型、漏洞/安全事件告警通知人列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件通知人管理", "功能过程": "绑定漏洞/安全事件告警通知人", "功能描述": "删除漏洞/安全事件触发时可通知的人员信息和通知方式", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务平台-漏洞/安全事件管理模块", "触发事件": "用户在漏洞/安全事件告警通知人列表点击绑定", "子过程": [{"子过程描述": "发起漏洞/安全事件告警通知人绑定请求", "数据移动类型": "E", "数据组": "漏洞/安全事件告警通知人绑定请求", "数据属性": "漏洞/安全事件告警通知人绑定参数", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警通知人重复性校验", "数据移动类型": "R", "数据组": "漏洞/安全事件告警通知人校验信息", "数据属性": "漏洞/安全事件告警通知人名称、主键、非空校验、判重、漏洞/安全事件告警通知人类型", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警通知人绑定保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警通知人绑定结果", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、绑定结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警通知人绑定内容", "数据移动类型": "X", "数据组": "漏洞/安全事件告警通知人绑定内容", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、绑定内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件通知人管理", "功能过程": "新增漏洞/安全事件告警通知人", "功能描述": "删除漏洞/安全事件触发时可通知的人员信息和通知方式", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务平台-漏洞/安全事件管理模块", "触发事件": "用户在漏洞/安全事件告警通知人列表点击新增", "子过程": [{"子过程描述": "输入新增漏洞/安全事件告警通知人信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警通知人新增信息", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警通知人重复性校验", "数据移动类型": "R", "数据组": "漏洞/安全事件告警通知人校验信息", "数据属性": "漏洞/安全事件告警通知人名称、主键、非空校验、判重、漏洞/安全事件告警通知人类型", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警通知人新增入库", "数据移动类型": "W", "数据组": "漏洞/安全事件告警通知人新增入库信息", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、新增时间", "CFP": 1}, {"子过程描述": "返回展示新增漏洞/安全事件告警通知人", "数据移动类型": "X", "数据组": "漏洞/安全事件告警通知人新增内容", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、新增内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "漏洞/安全事件通知人管理", "功能过程": "删除漏洞/安全事件告警通知人", "功能描述": "删除漏洞/安全事件触发时可通知的人员信息和通知方式", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务平台-漏洞/安全事件管理模块", "触发事件": "用户在漏洞/安全事件告警通知人列表点击删除", "子过程": [{"子过程描述": "发起漏洞/安全事件告警通知人删除请求", "数据移动类型": "E", "数据组": "漏洞/安全事件告警通知人删除请求", "数据属性": "漏洞/安全事件告警通知人删除参数", "CFP": 1}, {"子过程描述": "获取操作员权限并判断漏洞/安全事件告警通知人是否可删除", "数据移动类型": "R", "数据组": "漏洞/安全事件告警通知人删除权限", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、操作员权限", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警通知人删除保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警通知人删除结果", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、删除结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件告警通知人删除内容", "数据移动类型": "X", "数据组": "漏洞/安全事件告警通知人删除内容", "数据属性": "漏洞/安全事件告警通知人名称、漏洞/安全事件告警通知人类型、漏洞/安全事件告警通知人ID、删除内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "告警邮箱配置管理", "功能过程": "告警邮箱服务器配置提交", "功能描述": "配置漏洞/安全事件触发时告警信息发送的邮箱配置信息", "预估工作量（人天）": 4, "功能过程名称": "告警邮箱服务器配置提交", "子过程": [{"子过程描述": "输入告警邮箱服务器配置信息", "数据移动类型": "E", "数据组": "告警邮箱服务器配置信息", "数据属性": "告警邮箱服务器配置信息", "CFP": 1}, {"子过程描述": "告警邮箱服务器配置信息重复性校验", "数据移动类型": "R", "数据组": "告警邮箱服务器配置信息", "数据属性": "告警邮箱服务器配置信息", "CFP": 1}, {"子过程描述": "告警邮箱服务器配置信息保存", "数据移动类型": "W", "数据组": "告警邮箱服务器配置信息", "数据属性": "告警邮箱服务器配置信息", "CFP": 1}, {"子过程描述": "返回展示告警邮箱服务器配置结果", "数据移动类型": "X", "数据组": "告警邮箱服务器配置结果", "数据属性": "告警邮箱服务器配置结果", "CFP": 1}], "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-告警邮箱配置管理模块", "触发事件": "用户在告警邮箱服务器配置页面点击告警邮箱服务器配置提交按钮"}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "告警邮箱配置管理", "功能过程": "告警邮箱服务器配置查询", "功能描述": "查询漏洞/安全事件触发时告警信息发送的邮箱配置信息", "预估工作量（人天）": 3, "功能过程名称": "告警邮箱服务器配置查询", "子过程": [{"子过程描述": "输入告警邮箱服务器配置查询条件", "数据移动类型": "E", "数据组": "告警邮箱服务器配置查询请求", "数据属性": "告警邮箱服务器配置查询条件、告警邮箱服务器配置查询项", "CFP": 1}, {"子过程描述": "读取告警邮箱服务器配置", "数据移动类型": "R", "数据组": "告警邮箱服务器配置", "数据属性": "告警邮箱服务器配置名称、告警邮箱服务器配置类型、告警邮箱服务器配置ID", "CFP": 1}, {"子过程描述": "返回告警邮箱服务器配置查询结果展示", "数据移动类型": "X", "数据组": "告警邮箱服务器配置查询结果", "数据属性": "告警邮箱服务器配置名称、告警邮箱服务器配置类型、告警邮箱服务器配置ID、查询时间", "CFP": 1}], "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-告警邮箱配置管理模块", "触发事件": "用户在告警邮箱服务器配置页面点击告警邮箱服务器配置查询按钮"}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "告警邮箱配置管理", "功能过程": "告警邮箱服务器配置重置", "功能描述": "清空漏洞/安全事件触发时告警信息发送的邮箱配置信息", "预估工作量（人天）": 3, "功能过程名称": "告警邮箱服务器配置重置", "子过程": [{"子过程描述": "发起告警邮箱服务器配置重置请求", "数据移动类型": "E", "数据组": "告警邮箱服务器配置重置请求", "数据属性": "告警邮箱服务器配置重置指令", "CFP": 1}, {"子过程描述": "获取告警邮箱服务器配置", "数据移动类型": "R", "数据组": "告警邮箱服务器配置", "数据属性": "告警邮箱服务器配置名称、告警邮箱服务器配置类型、告警邮箱服务器配置ID", "CFP": 1}, {"子过程描述": "告警邮箱服务器配置重置", "数据移动类型": "W", "数据组": "告警邮箱服务器配置重置结果", "数据属性": "告警邮箱服务器配置名称、告警邮箱服务器配置类型、告警邮箱服务器配置ID、重置结果", "CFP": 1}, {"子过程描述": "返回展示告警邮箱服务器配置重置结果", "数据移动类型": "X", "数据组": "告警邮箱服务器配置重置结果", "数据属性": "告警邮箱服务器配置名称、告警邮箱服务器配置类型、告警邮箱服务器配置ID、重置结果", "CFP": 1}], "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-告警邮箱配置管理模块", "触发事件": "用户在告警邮箱服务器配置页面点击告警邮箱服务器配置重置按钮"}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件级别管理", "三级功能模块": "告警邮箱配置管理", "功能过程": "告警验证邮件发送", "功能描述": "测试配置的邮箱服务器是否正常，发送测试邮件", "预估工作量（人天）": 5, "功能过程名称": "告警验证邮件发送", "子过程": [{"子过程描述": "输入告警验证邮件发送请求", "数据移动类型": "E", "数据组": "告警验证邮件发送请求", "数据属性": "告警验证邮件发送请求", "CFP": 1}, {"子过程描述": "获取告警验证邮件发送参数", "数据移动类型": "R", "数据组": "告警验证邮件发送参数", "数据属性": "告警验证邮件发送参数", "CFP": 1}, {"子过程描述": "告警验证邮件发送", "数据移动类型": "X", "数据组": "告警验证邮件", "数据属性": "告警验证邮件名称、告警验证邮件类型、告警验证邮件ID", "CFP": 1}, {"子过程描述": "记录告警验证邮件发送日志", "数据移动类型": "W", "数据组": "告警验证邮件发送日志", "数据属性": "告警验证邮件名称、告警验证邮件类型、告警验证邮件ID、日志记录时间", "CFP": 1}], "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-告警邮箱配置管理模块", "触发事件": "用户在告警邮箱服务器配置页面点击告警验证邮件发送按钮"}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件详情管理", "三级功能模块": "漏洞/安全事件详情管理", "功能过程": "漏洞/安全事件基本信息展示", "功能描述": "配置漏洞/安全事件告警阈值的组合判定方式", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理", "触发事件": "用户点击漏洞/安全事件详情", "子过程": [{"子过程描述": "输入漏洞/安全事件查询条件", "数据移动类型": "E", "数据组": "漏洞/安全事件查询请求", "数据属性": "漏洞/安全事件查询条件、漏洞/安全事件查询项", "CFP": 1}, {"子过程描述": "读取漏洞/安全事件", "数据移动类型": "R", "数据组": "漏洞/安全事件信息", "数据属性": "漏洞/安全事件ID、漏洞/安全事件名称、漏洞/安全事件类型、漏洞/安全事件状态", "CFP": 1}, {"子过程描述": "输出漏洞/安全事件查询结果", "数据移动类型": "X", "数据组": "漏洞/安全事件查询结果", "数据属性": "漏洞/安全事件ID、漏洞/安全事件名称、漏洞/安全事件类型、漏洞/安全事件状态、查询时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件详情管理", "三级功能模块": "漏洞/安全事件详情管理", "功能过程": "漏洞/安全事件信息编辑", "功能描述": "配置漏洞/安全事件告警阈值的组合判定方式", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理", "触发事件": "用户点击漏洞/安全事件编辑", "子过程": [{"子过程描述": "输入漏洞/安全事件编辑信息", "数据移动类型": "E", "数据组": "漏洞/安全事件编辑信息", "数据属性": "漏洞/安全事件ID、漏洞/安全事件名称、漏洞/安全事件类型、漏洞/安全事件状态", "CFP": 1}, {"子过程描述": "漏洞/安全事件编辑保存", "数据移动类型": "W", "数据组": "漏洞/安全事件编辑结果", "数据属性": "漏洞/安全事件ID、漏洞/安全事件名称、漏洞/安全事件类型、漏洞/安全事件状态、编辑结果", "CFP": 1}, {"子过程描述": "返回展示漏洞/安全事件编辑内容", "数据移动类型": "X", "数据组": "漏洞/安全事件编辑内容", "数据属性": "漏洞/安全事件ID、漏洞/安全事件名称、漏洞/安全事件类型、漏洞/安全事件状态、编辑内容", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件详情管理", "三级功能模块": "漏洞/安全事件详情管理", "功能过程": "漏洞/安全事件告警阈值配置", "功能描述": "配置漏洞/安全事件告警阈值的组合判定方式", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理", "触发事件": "用户点击漏洞/安全事件告警阈值配置", "子过程": [{"子过程描述": "输入漏洞/安全事件告警阈值配置信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警阈值配置信息", "数据属性": "漏洞/安全事件告警阈值配置ID、漏洞/安全事件告警阈值配置名称", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警阈值配置保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警阈值配置结果", "数据属性": "漏洞/安全事件告警阈值配置ID、漏洞/安全事件告警阈值配置名称、漏洞/安全事件告警阈值配置类型、漏洞/安全事件告警阈值配置结果", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警阈值配置结果数据生成", "数据移动类型": "R", "数据组": "漏洞/安全事件告警阈值配置生成信息", "数据属性": "漏洞/安全事件告警阈值配置ID、漏洞/安全事件告警阈值配置名称、漏洞/安全事件告警阈值配置类型、漏洞/安全事件告警阈值配置结果", "CFP": 1}, {"子过程描述": "输出漏洞/安全事件告警阈值配置结果", "数据移动类型": "X", "数据组": "漏洞/安全事件告警阈值配置结果展示信息", "数据属性": "漏洞/安全事件告警阈值配置ID、漏洞/安全事件告警阈值配置名称、漏洞/安全事件告警阈值配置类型、漏洞/安全事件告警阈值配置结果、分页数、下钻信息", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警阈值配置数据归档", "数据移动类型": "W", "数据组": "漏洞/安全事件告警阈值配置归档信息", "数据属性": "漏洞/安全事件告警阈值配置ID、漏洞/安全事件告警阈值配置名称、漏洞/安全事件告警阈值配置类型、漏洞/安全事件告警阈值配置结果、归档时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件详情管理", "三级功能模块": "漏洞/安全事件详情管理", "功能过程": "漏洞/安全事件标签配置", "功能描述": "配置漏洞/安全事件告警阈值的组合判定方式", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理", "触发事件": "用户点击漏洞/安全事件标签配置", "子过程": [{"子过程描述": "输入漏洞/安全事件标签配置信息", "数据移动类型": "E", "数据组": "漏洞/安全事件标签配置信息", "数据属性": "漏洞/安全事件标签配置ID、漏洞/安全事件标签配置名称", "CFP": 1}, {"子过程描述": "漏洞/安全事件标签配置保存", "数据移动类型": "W", "数据组": "漏洞/安全事件标签配置结果", "数据属性": "漏洞/安全事件标签配置ID、漏洞/安全事件标签配置名称、漏洞/安全事件标签配置类型、漏洞/安全事件标签配置结果", "CFP": 1}, {"子过程描述": "漏洞/安全事件标签配置结果数据生成", "数据移动类型": "R", "数据组": "漏洞/安全事件标签配置生成信息", "数据属性": "漏洞/安全事件标签配置ID、漏洞/安全事件标签配置名称、漏洞/安全事件标签配置类型、漏洞/安全事件标签配置结果", "CFP": 1}, {"子过程描述": "输出漏洞/安全事件标签配置结果", "数据移动类型": "X", "数据组": "漏洞/安全事件标签配置结果展示信息", "数据属性": "漏洞/安全事件标签配置ID、漏洞/安全事件标签配置名称、漏洞/安全事件标签配置类型、漏洞/安全事件标签配置结果、分页数、下钻信息", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "漏洞/安全事件详情管理", "三级功能模块": "漏洞/安全事件详情管理", "功能过程": "漏洞/安全事件告警组合阈值配置", "功能描述": "配置漏洞/安全事件告警阈值的组合判定方式", "预估工作量（人天）": "7", "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理", "触发事件": "用户点击漏洞/安全事件告警组合阈值配置", "子过程": [{"子过程描述": "输入漏洞/安全事件告警组合阈值配置信息", "数据移动类型": "E", "数据组": "漏洞/安全事件告警组合阈值配置信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警组合阈值配置保存", "数据移动类型": "W", "数据组": "漏洞/安全事件告警组合阈值配置结果", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警组合阈值配置结果数据生成", "数据移动类型": "R", "数据组": "漏洞/安全事件告警组合阈值配置生成信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果", "CFP": 1}, {"子过程描述": "输出漏洞/安全事件告警组合阈值配置结果", "数据移动类型": "X", "数据组": "漏洞/安全事件告警组合阈值配置结果展示信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果、分页数、下钻信息", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警组合阈值配置数据归档", "数据移动类型": "W", "数据组": "漏洞/安全事件告警组合阈值配置归档信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果、归档时间", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警组合阈值配置数据同步", "数据移动类型": "W", "数据组": "漏洞/安全事件告警组合阈值配置同步信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果、同步时间", "CFP": 1}, {"子过程描述": "漏洞/安全事件告警组合阈值配置数据备份", "数据移动类型": "W", "数据组": "漏洞/安全事件告警组合阈值配置备份信息", "数据属性": "漏洞/安全事件告警组合阈值配置ID、漏洞/安全事件告警组合阈值配置名称、漏洞/安全事件告警组合阈值配置类型、漏洞/安全事件告警组合阈值配置结果、备份时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码产品监控范围管理", "三级功能模块": "密码产品监控范围管理", "功能过程": "密码产品监控列表分布展示", "功能描述": "以折线图信息显示监控的密码产品对象的监控历史数据", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-密码产品监控范围管理模块", "触发事件": "用户点击密码产品监控列表分布展示", "子过程": [{"子过程描述": "输入密码产品监控列表分布查询条件", "数据移动类型": "E", "数据组": "密码产品监控列表分布查询请求", "数据属性": "密码产品监控列表分布查询条件、密码产品监控列表分布查询项", "CFP": 1}, {"子过程描述": "读取密码产品监控列表分布", "数据移动类型": "R", "数据组": "密码产品监控列表分布", "数据属性": "密码产品监控列表分布名称、密码产品监控列表分布类型、密码产品监控列表分布ID", "CFP": 1}, {"子过程描述": "返回密码产品监控列表分布查询结果展示", "数据移动类型": "X", "数据组": "密码产品监控列表分布查询结果", "数据属性": "密码产品监控列表分布名称、密码产品监控列表分布类型、密码产品监控列表分布ID、查询时间", "CFP": 1}, {"子过程描述": "保存密码产品监控列表分布查询记录", "数据移动类型": "W", "数据组": "密码产品监控列表分布查询记录", "数据属性": "密码产品监控列表分布名称、密码产品监控列表分布类型、密码产品监控列表分布ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码产品监控范围管理", "三级功能模块": "密码产品监控范围管理", "功能过程": "密码产品当前监控数据列表展示", "功能描述": "以折线图信息显示监控的密码产品对象的监控历史数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-密码产品监控范围管理模块", "触发事件": "用户点击密码产品当前监控数据列表", "子过程": [{"子过程描述": "输入密码产品当前监控数据列表查询条件", "数据移动类型": "E", "数据组": "密码产品当前监控数据列表查询请求", "数据属性": "密码产品当前监控数据列表查询条件、密码产品当前监控数据列表查询项", "CFP": 1}, {"子过程描述": "获取密码产品当前监控数据列表", "数据移动类型": "R", "数据组": "密码产品当前监控数据列表", "数据属性": "密码产品当前监控数据列表名称、密码产品当前监控数据列表类型、密码产品当前监控数据列表ID", "CFP": 1}, {"子过程描述": "返回密码产品当前监控数据列表查询结果展示", "数据移动类型": "X", "数据组": "密码产品当前监控数据列表查询结果", "数据属性": "密码产品当前监控数据列表名称、密码产品当前监控数据列表类型、密码产品当前监控数据列表ID、分页数、查询时间", "CFP": 1}, {"子过程描述": "保存密码产品当前监控数据列表查询记录", "数据移动类型": "W", "数据组": "密码产品当前监控数据列表查询记录", "数据属性": "密码产品当前监控数据列表名称、密码产品当前监控数据列表类型、密码产品当前监控数据列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码产品监控范围管理", "三级功能模块": "密码产品监控范围管理", "功能过程": "密码产品监控历史数据列表展示", "功能描述": "以折线图信息显示监控的密码产品对象的监控历史数据", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-密码产品监控范围管理模块", "触发事件": "用户点击密码产品监控历史数据列表", "子过程": [{"子过程描述": "输入密码产品监控历史数据列表查询条件", "数据移动类型": "E", "数据组": "密码产品监控历史数据列表查询请求", "数据属性": "密码产品监控历史数据列表查询条件、密码产品监控历史数据列表查询项", "CFP": 1}, {"子过程描述": "获取密码产品监控历史数据列表", "数据移动类型": "R", "数据组": "密码产品监控历史数据列表", "数据属性": "密码产品监控历史数据列表名称、密码产品监控历史数据列表类型、密码产品监控历史数据列表ID", "CFP": 1}, {"子过程描述": "返回密码产品监控历史数据列表查询结果展示", "数据移动类型": "X", "数据组": "密码产品监控历史数据列表查询结果", "数据属性": "密码产品监控历史数据列表名称、密码产品监控历史数据列表类型、密码产品监控历史数据列表ID、分页数、查询时间", "CFP": 1}, {"子过程描述": "保存密码产品监控历史数据列表查询记录", "数据移动类型": "W", "数据组": "密码产品监控历史数据列表查询记录", "数据属性": "密码产品监控历史数据列表名称、密码产品监控历史数据列表类型、密码产品监控历史数据列表ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码产品监控范围管理", "三级功能模块": "密码产品监控范围管理", "功能过程": "密码产品当前监控数据折线图", "功能描述": "以折线图信息显示监控的密码产品对象的监控历史数据", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-密码产品监控范围管理模块", "触发事件": "用户点击密码产品当前监控数据折线图", "子过程": [{"子过程描述": "输入密码产品当前监控数据折线图查询条件", "数据移动类型": "E", "数据组": "密码产品当前监控数据折线图查询请求", "数据属性": "密码产品当前监控数据折线图查询条件、密码产品当前监控数据折线图查询项", "CFP": 1}, {"子过程描述": "获取密码产品当前监控数据折线图", "数据移动类型": "R", "数据组": "密码产品当前监控数据折线图", "数据属性": "密码产品当前监控数据折线图名称、密码产品当前监控数据折线图类型、密码产品当前监控数据折线图ID", "CFP": 1}, {"子过程描述": "返回密码产品当前监控数据折线图查询结果展示", "数据移动类型": "X", "数据组": "密码产品当前监控数据折线图查询结果", "数据属性": "密码产品当前监控数据折线图名称、密码产品当前监控数据折线图类型、密码产品当前监控数据折线图ID、分页数、查询时间", "CFP": 1}, {"子过程描述": "保存密码产品当前监控数据折线图查询记录", "数据移动类型": "W", "数据组": "密码产品当前监控数据折线图查询记录", "数据属性": "密码产品当前监控数据折线图名称、密码产品当前监控数据折线图类型、密码产品当前监控数据折线图ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "密码应用漏洞/安全事件管理", "二级功能模块": "密码产品监控范围管理", "三级功能模块": "密码产品监控范围管理", "功能过程": "密码产品监控历史数据折线图", "功能描述": "以折线图信息显示监控的密码产品对象的监控历史数据", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密码应用漏洞/安全事件管理-密码产品监控范围管理模块", "触发事件": "用户点击密码产品监控历史数据折线图", "子过程": [{"子过程描述": "输入密码产品监控历史数据折线图查询条件", "数据移动类型": "E", "数据组": "密码产品监控历史数据折线图查询请求", "数据属性": "密码产品监控历史数据折线图查询条件、密码产品监控历史数据折线图查询项", "CFP": 1}, {"子过程描述": "获取密码产品监控历史数据折线图", "数据移动类型": "R", "数据组": "密码产品监控历史数据折线图", "数据属性": "密码产品监控历史数据折线图名称、密码产品监控历史数据折线图类型、密码产品监控历史数据折线图ID", "CFP": 1}, {"子过程描述": "返回密码产品监控历史数据折线图查询结果展示", "数据移动类型": "X", "数据组": "密码产品监控历史数据折线图查询结果", "数据属性": "密码产品监控历史数据折线图名称、密码产品监控历史数据折线图类型、密码产品监控历史数据折线图ID、分页数、查询时间", "CFP": 1}, {"子过程描述": "保存密码产品监控历史数据折线图查询记录", "数据移动类型": "W", "数据组": "密码产品监控历史数据折线图查询记录", "数据属性": "密码产品监控历史数据折线图名称、密码产品监控历史数据折线图类型、密码产品监控历史数据折线图ID、操作人、系统时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用数据上报类接口", "三级功能模块": "密码应用数据上报管理", "功能过程": "密码应用上报数据采集", "功能描述": "根据配置的上报频率，定时上报更新密码应用数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密码应用数据上报页面点击密码应用上报数据采集按钮", "子过程": [{"子过程描述": "输入密码应用上报数据采集信息", "数据移动类型": "E", "数据组": "密码应用上报数据采集信息", "数据属性": "密码应用上报数据采集ID、密码应用上报数据采集名称", "CFP": 1}, {"子过程描述": "获取密码应用上报数据", "数据移动类型": "R", "数据组": "密码应用上报数据", "数据属性": "密码应用上报数据ID、密码应用上报数据名称、密码应用上报数据类型", "CFP": 1}, {"子过程描述": "密码应用上报数据采集入库", "数据移动类型": "W", "数据组": "密码应用上报数据采集结果", "数据属性": "密码应用上报数据采集时间、密码应用上报数据采集结果等", "CFP": 1}, {"子过程描述": "返回展示密码应用上报数据采集结果", "数据移动类型": "X", "数据组": "密码应用上报数据采集内容", "数据属性": "密码应用上报数据采集内容", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用数据上报类接口", "三级功能模块": "密码应用数据上报管理", "功能过程": "密码应用数据上报接口对接", "功能描述": "根据配置的上报频率，定时上报更新密码应用数据", "预估工作量（人天）": 6, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密码应用数据上报页面点击密码应用数据上报接口对接按钮", "子过程": [{"子过程描述": "发起密码应用数据上报接口请求", "数据移动类型": "E", "数据组": "密码应用数据上报接口请求", "数据属性": "密码应用数据上报接口名称、密码应用数据上报接口类型、密码应用数据上报接口ID", "CFP": 1}, {"子过程描述": "获取密码应用数据上报接口", "数据移动类型": "R", "数据组": "密码应用数据上报接口", "数据属性": "密码应用数据上报接口名称、密码应用数据上报接口类型、密码应用数据上报接口ID、密码应用数据上报接口状态", "CFP": 1}, {"子过程描述": "密码应用数据上报接口返回", "数据移动类型": "X", "数据组": "密码应用数据上报接口返回结果", "数据属性": "密码应用数据上报接口调用结果、密码应用数据上报接口名称", "CFP": 1}, {"子过程描述": "密码应用数据上报接口调用情况记录", "数据移动类型": "W", "数据组": "密码应用数据上报接口调用情况", "数据属性": "密码应用数据上报接口调用时间、密码应用数据上报接口调用结果、密码应用数据上报接口名称", "CFP": 1}, {"子过程描述": "密码应用数据上报接口调用结果展示", "数据移动类型": "X", "数据组": "密码应用数据上报接口调用结果", "数据属性": "密码应用数据上报接口调用结果、密码应用数据上报接口名称、密码应用数据上报接口类型", "CFP": 1}, {"子过程描述": "密码应用数据上报接口调用数据归档", "数据移动类型": "W", "数据组": "密码应用数据上报接口调用数据", "数据属性": "密码应用数据上报接口调用数据名称、密码应用数据上报接口调用数据类型、密码应用数据上报接口调用数据ID", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用数据上报类接口", "三级功能模块": "密码应用数据上报管理", "功能过程": "密码应用上报数据列表展示", "功能描述": "根据配置的上报频率，定时上报更新密码应用数据", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密码应用数据上报页面点击密码应用上报数据列表", "子过程": [{"子过程描述": "输入密码应用上报数据列表查询条件", "数据移动类型": "E", "数据组": "密码应用上报数据列表查询请求", "数据属性": "密码应用上报数据列表查询条件、密码应用上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用上报数据列表", "数据移动类型": "R", "数据组": "密码应用上报数据列表", "数据属性": "密码应用上报数据列表名称、密码应用上报数据列表类型、密码应用上报数据列表ID", "CFP": 1}, {"子过程描述": "返回密码应用上报数据列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用上报数据列表查询结果", "数据属性": "密码应用上报数据列表名称、密码应用上报数据列表类型、密码应用上报数据列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用数据上报类接口", "三级功能模块": "密码应用数据上报管理", "功能过程": "密码应用数据定时上报更新", "功能描述": "根据配置的上报频率，定时上报更新密码应用数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密码应用数据上报页面点击密码应用数据定时上报更新", "子过程": [{"子过程描述": "发起密码应用数据定时上报更新请求", "数据移动类型": "E", "数据组": "密码应用数据定时上报更新请求", "数据属性": "密码应用数据定时上报更新指令", "CFP": 1}, {"子过程描述": "获取密码应用数据定时上报更新任务", "数据移动类型": "R", "数据组": "密码应用数据定时上报更新任务", "数据属性": "密码应用数据定时上报更新任务名称、密码应用数据定时上报更新任务类型、密码应用数据定时上报更新任务ID", "CFP": 1}, {"子过程描述": "密码应用数据定时上报更新", "数据移动类型": "X", "数据组": "密码应用数据定时上报更新结果", "数据属性": "密码应用数据定时上报更新时间、密码应用数据定时上报更新结果等", "CFP": 1}, {"子过程描述": "密码应用数据定时上报更新数据归档入库", "数据移动类型": "W", "数据组": "密码应用数据定时上报更新结果", "数据属性": "密码应用数据定时上报更新结果名称、密码应用数据定时上报更新结果类型、密码应用数据定时上报更新结果ID", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码产品信息上报", "功能过程": "密码产品信息上报数据采集", "功能描述": "根据配置的上报频率，定时上报更新密码产品信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码产品信息上报页面点击数据采集按钮", "子过程": [{"子过程描述": "输入密码产品信息上报数据采集指令", "数据移动类型": "E", "数据组": "密码产品信息上报数据采集指令", "数据属性": "密码产品信息上报数据采集请求、密码产品信息上报数据采集参数", "CFP": 1}, {"子过程描述": "获取密码产品信息上报数据", "数据移动类型": "R", "数据组": "密码产品信息上报数据", "数据属性": "密码产品信息上报数据名称、密码产品信息上报数据类型、密码产品信息上报数据ID", "CFP": 1}, {"子过程描述": "密码产品信息上报数据采集入库", "数据移动类型": "W", "数据组": "密码产品信息上报数据采集结果", "数据属性": "密码产品信息上报数据名称、密码产品信息上报数据类型、密码产品信息上报数据ID、采集时间", "CFP": 1}, {"子过程描述": "返回展示密码产品信息上报数据采集结果", "数据移动类型": "X", "数据组": "密码产品信息上报数据采集内容", "数据属性": "密码产品信息上报数据名称、密码产品信息上报数据类型、密码产品信息上报数据ID、采集结果", "CFP": 1}], "功能过程ID": "1"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码产品信息上报", "功能过程": "密码产品信息上报接口对接", "功能描述": "根据配置的上报频率，定时上报更新密码产品信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码产品信息上报页面点击接口对接按钮", "子过程": [{"子过程描述": "发起密码产品信息上报接口对接请求", "数据移动类型": "E", "数据组": "密码产品信息上报接口对接请求", "数据属性": "密码产品信息上报接口对接指令", "CFP": 1}, {"子过程描述": "获取密码产品信息上报接口", "数据移动类型": "R", "数据组": "密码产品信息上报接口", "数据属性": "密码产品信息上报接口名称、密码产品信息上报接口类型、密码产品信息上报接口ID", "CFP": 1}, {"子过程描述": "密码产品信息上报接口对接", "数据移动类型": "X", "数据组": "密码产品信息上报接口对接结果", "数据属性": "密码产品信息上报接口名称、密码产品信息上报接口类型、密码产品信息上报接口ID、对接结果", "CFP": 1}, {"子过程描述": "密码产品信息上报接口对接数据归档", "数据移动类型": "W", "数据组": "密码产品信息上报接口对接数据", "数据属性": "密码产品信息上报接口名称、密码产品信息上报接口类型、密码产品信息上报接口ID、对接时间", "CFP": 1}, {"子过程描述": "密码产品信息上报接口对接数据同步", "数据移动类型": "X", "数据组": "密码产品信息上报接口对接数据", "数据属性": "密码产品信息上报接口名称、密码产品信息上报接口类型、密码产品信息上报接口ID、同步时间", "CFP": 1}, {"子过程描述": "密码产品信息上报接口对接数据统计", "数据移动类型": "X", "数据组": "密码产品信息上报接口对接数据", "数据属性": "密码产品信息上报接口名称、密码产品信息上报接口类型、密码产品信息上报接口ID、统计维度", "CFP": 1}], "功能过程ID": "2"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码产品信息上报", "功能过程": "密码产品信息上报数据列表展示", "功能描述": "根据配置的上报频率，定时上报更新密码产品信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码产品信息上报页面点击列表展示按钮", "子过程": [{"子过程描述": "输入密码产品信息上报数据列表查询条件", "数据移动类型": "E", "数据组": "密码产品信息上报数据列表查询请求", "数据属性": "密码产品信息上报数据列表查询条件、密码产品信息上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取密码产品信息上报数据列表", "数据移动类型": "R", "数据组": "密码产品信息上报数据列表", "数据属性": "密码产品信息上报数据列表名称、密码产品信息上报数据列表类型、密码产品信息上报数据列表ID", "CFP": 1}, {"子过程描述": "输出密码产品信息上报数据列表查询结果", "数据移动类型": "X", "数据组": "密码产品信息上报数据列表查询结果", "数据属性": "密码产品信息上报数据列表名称、密码产品信息上报数据列表类型、密码产品信息上报数据列表ID、查询时间", "CFP": 1}], "功能过程ID": "3"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码产品信息上报", "功能过程": "密码产品信息数据定时上报更新", "功能描述": "根据配置的上报频率，定时上报更新密码产品信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码产品信息上报页面点击定时上报更新按钮", "子过程": [{"子过程描述": "发起密码产品信息数据定时上报更新请求", "数据移动类型": "E", "数据组": "密码产品信息数据定时上报更新请求", "数据属性": "密码产品信息数据定时上报更新指令", "CFP": 1}, {"子过程描述": "获取密码产品信息数据定时上报更新内容", "数据移动类型": "R", "数据组": "密码产品信息数据定时上报更新内容", "数据属性": "密码产品信息数据定时上报更新时间、密码产品信息数据定时上报更新范围", "CFP": 1}, {"子过程描述": "密码产品信息数据定时上报更新", "数据移动类型": "X", "数据组": "密码产品信息数据定时上报更新结果", "数据属性": "密码产品信息数据定时上报更新时间、密码产品信息数据定时上报更新结果", "CFP": 1}, {"子过程描述": "密码产品信息数据定时上报更新数据归档", "数据移动类型": "W", "数据组": "密码产品信息数据定时上报更新结果", "数据属性": "密码产品信息数据定时上报更新时间、密码产品信息数据定时上报更新结果、归档时间", "CFP": 1}], "功能过程ID": "4"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密钥信息上报", "功能过程": "密钥信息上报数据采集", "功能描述": "根据配置的上报频率，定时上报更新密钥信息信息数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密钥信息上报页面点击密钥信息上报数据采集按钮", "子过程": [{"子过程描述": "输入密钥信息上报数据采集指令", "数据移动类型": "E", "数据组": "密钥信息上报数据采集指令", "数据属性": "密钥信息上报数据采集请求、密钥信息上报数据采集参数", "CFP": 1}, {"子过程描述": "获取密钥信息上报数据", "数据移动类型": "R", "数据组": "密钥信息上报数据", "数据属性": "密钥信息上报数据名称、密钥信息上报数据类型、密钥信息上报数据ID", "CFP": 1}, {"子过程描述": "密钥信息上报数据采集入库", "数据移动类型": "W", "数据组": "密钥信息上报数据采集结果", "数据属性": "密钥信息上报数据名称、密钥信息上报数据类型、密钥信息上报数据ID、采集结果", "CFP": 1}, {"子过程描述": "返回展示密钥信息上报数据采集结果", "数据移动类型": "X", "数据组": "密钥信息上报数据采集内容", "数据属性": "密钥信息上报数据名称、密钥信息上报数据类型、密钥信息上报数据ID、采集内容", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密钥信息上报", "功能过程": "密钥信息上报接口对接", "功能描述": "根据配置的上报频率，定时上报更新密钥信息信息数据", "预估工作量（人天）": 6, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密钥信息上报页面点击密钥信息上报接口对接按钮", "子过程": [{"子过程描述": "输入密钥信息上报接口对接指令", "数据移动类型": "E", "数据组": "密钥信息上报接口对接指令", "数据属性": "密钥信息上报接口对接请求、密钥信息上报接口对接参数", "CFP": 1}, {"子过程描述": "获取密钥信息上报接口", "数据移动类型": "R", "数据组": "密钥信息上报接口", "数据属性": "密钥信息上报接口名称、密钥信息上报接口类型、密钥信息上报接口ID", "CFP": 1}, {"子过程描述": "密钥信息上报接口对接", "数据移动类型": "X", "数据组": "密钥信息上报接口对接结果", "数据属性": "密钥信息上报接口名称、密钥信息上报接口类型、密钥信息上报接口ID、对接结果", "CFP": 1}, {"子过程描述": "密钥信息上报接口对接数据归档", "数据移动类型": "W", "数据组": "密钥信息上报接口对接数据", "数据属性": "密钥信息上报接口名称、密钥信息上报接口类型、密钥信息上报接口ID、归档时间", "CFP": 1}, {"子过程描述": "密钥信息上报接口对接数据同步", "数据移动类型": "X", "数据组": "密钥信息上报接口对接数据", "数据属性": "密钥信息上报接口名称、密钥信息上报接口类型、密钥信息上报接口ID、同步结果", "CFP": 1}, {"子过程描述": "密钥信息上报接口对接数据统计", "数据移动类型": "R", "数据组": "密钥信息上报接口对接数据", "数据属性": "密钥信息上报接口名称、密钥信息上报接口类型、密钥信息上报接口ID、统计维度", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密钥信息上报", "功能过程": "密钥信息上报数据列表展示", "功能描述": "根据配置的上报频率，定时上报更新密钥信息信息数据", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密钥信息上报页面点击密钥信息上报数据列表", "子过程": [{"子过程描述": "输入密钥信息上报数据列表查询条件", "数据移动类型": "E", "数据组": "密钥信息上报数据列表查询请求", "数据属性": "密钥信息上报数据列表查询条件、密钥信息上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取密钥信息上报数据列表", "数据移动类型": "R", "数据组": "密钥信息上报数据列表", "数据属性": "密钥信息上报数据列表名称、密钥信息上报数据列表类型、密钥信息上报数据列表ID", "CFP": 1}, {"子过程描述": "返回密钥信息上报数据列表查询结果展示", "数据移动类型": "X", "数据组": "密钥信息上报数据列表查询结果", "数据属性": "密钥信息上报数据列表名称、密钥信息上报数据列表类型、密钥信息上报数据列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密钥信息上报", "功能过程": "密钥信息数据定时上报更新", "功能描述": "根据配置的上报频率，定时上报更新密钥信息信息数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在密钥信息上报页面点击密钥信息数据定时上报更新按钮", "子过程": [{"子过程描述": "发起密钥信息数据定时上报更新请求", "数据移动类型": "E", "数据组": "密钥信息数据定时上报更新请求", "数据属性": "密钥信息数据定时上报更新指令", "CFP": 1}, {"子过程描述": "获取密钥信息数据定时上报更新内容", "数据移动类型": "R", "数据组": "密钥信息数据定时上报更新内容", "数据属性": "密钥信息数据定时上报更新时间、密钥信息数据定时上报更新内容", "CFP": 1}, {"子过程描述": "密钥信息数据定时上报更新", "数据移动类型": "X", "数据组": "密钥信息数据定时上报更新结果", "数据属性": "密钥信息数据定时上报更新时间、密钥信息数据定时上报更新结果", "CFP": 1}, {"子过程描述": "密钥信息数据定时上报更新数据归档入库", "数据移动类型": "W", "数据组": "密钥信息数据定时上报更新结果", "数据属性": "密钥信息数据定时上报更新时间、密钥信息数据定时上报更新结果、归档时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "证书信息上报", "功能过程": "证书信息上报数据采集", "功能描述": "根据配置的上报频率，定时上报更新证书信息信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击证书信息上报数据采集按钮", "子过程": [{"子过程描述": "输入证书信息上报数据采集指令", "数据移动类型": "E", "数据组": "证书信息上报数据采集指令", "数据属性": "证书信息上报数据采集请求、证书信息上报数据采集参数", "CFP": 1}, {"子过程描述": "获取需要上报的证书信息", "数据移动类型": "R", "数据组": "证书信息", "数据属性": "证书信息上报数据、证书信息上报数据类型", "CFP": 1}, {"子过程描述": "证书信息上报数据采集入库", "数据移动类型": "W", "数据组": "证书信息上报数据采集结果", "数据属性": "证书信息上报数据采集时间、证书信息上报数据采集结果等", "CFP": 1}, {"子过程描述": "返回展示证书信息上报数据采集结果", "数据移动类型": "X", "数据组": "证书信息上报数据采集内容", "数据属性": "证书信息上报数据采集内容", "CFP": 1}], "功能过程ID": "1"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "证书信息上报", "功能过程": "证书信息上报接口对接", "功能描述": "根据配置的上报频率，定时上报更新证书信息信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击证书信息上报接口对接按钮", "子过程": [{"子过程描述": "输入证书信息上报接口对接指令", "数据移动类型": "E", "数据组": "证书信息上报接口对接指令", "数据属性": "证书信息上报接口对接请求、证书信息上报接口对接参数", "CFP": 1}, {"子过程描述": "获取证书信息上报接口", "数据移动类型": "R", "数据组": "证书信息上报接口", "数据属性": "证书信息上报接口名称、证书信息上报接口类型、证书信息上报接口ID", "CFP": 1}, {"子过程描述": "证书信息上报接口对接", "数据移动类型": "X", "数据组": "证书信息上报接口对接结果", "数据属性": "证书信息上报接口对接结果、证书信息上报接口对接时间", "CFP": 1}, {"子过程描述": "保存证书信息上报接口对接数据", "数据移动类型": "W", "数据组": "证书信息上报接口对接数据", "数据属性": "证书信息上报接口对接结果、证书信息上报接口对接时间、证书信息上报接口对接内容等", "CFP": 1}, {"子过程描述": "证书信息上报接口对接数据归档", "数据移动类型": "W", "数据组": "证书信息上报接口对接数据", "数据属性": "证书信息上报接口对接结果、证书信息上报接口对接时间、证书信息上报接口对接内容等", "CFP": 1}, {"子过程描述": "返回展示证书信息上报接口对接结果", "数据移动类型": "X", "数据组": "证书信息上报接口对接展示数据", "数据属性": "证书信息上报接口对接内容、证书信息上报接口对接结果等", "CFP": 1}], "功能过程ID": "2"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "证书信息上报", "功能过程": "证书信息上报数据列表展示", "功能描述": "根据配置的上报频率，定时上报更新证书信息信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击证书信息上报数据列表", "子过程": [{"子过程描述": "输入证书信息上报数据列表查询条件", "数据移动类型": "E", "数据组": "证书信息上报数据列表查询请求", "数据属性": "证书信息上报数据列表查询条件、证书信息上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取证书信息上报数据列表", "数据移动类型": "R", "数据组": "证书信息上报数据列表", "数据属性": "证书信息上报数据列表名称、证书信息上报数据列表类型、证书信息上报数据列表ID", "CFP": 1}, {"子过程描述": "返回证书信息上报数据列表查询结果展示", "数据移动类型": "X", "数据组": "证书信息上报数据列表查询结果", "数据属性": "证书信息上报数据列表名称、证书信息上报数据列表类型、证书信息上报数据列表ID、查询时间", "CFP": 1}], "功能过程ID": "3"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "证书信息上报", "功能过程": "证书信息数据定时上报更新", "功能描述": "根据配置的上报频率，定时上报更新证书信息信息数据", "预估工作量（人天）": "4", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "定时任务触发证书信息上报更新", "子过程": [{"子过程描述": "获取证书信息上报更新任务", "数据移动类型": "R", "数据组": "证书信息上报更新任务", "数据属性": "证书信息上报更新任务名称、证书信息上报更新任务类型、证书信息上报更新任务ID", "CFP": 1}, {"子过程描述": "证书信息上报更新", "数据移动类型": "X", "数据组": "证书信息上报更新结果", "数据属性": "证书信息上报更新结果数据", "CFP": 1}, {"子过程描述": "证书信息上报更新结果数据归档入库", "数据移动类型": "W", "数据组": "证书信息上报更新结果", "数据属性": "证书信息上报更新结果名称、证书信息上报更新结果类型、证书信息上报更新结果ID", "CFP": 1}, {"子过程描述": "返回展示证书信息上报更新结果", "数据移动类型": "X", "数据组": "证书信息上报更新结果展示信息", "数据属性": "证书信息上报更新结果名称、证书信息上报更新结果类型、证书信息上报更新结果ID", "CFP": 1}], "功能过程ID": "4"}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码文档信息上报", "功能过程": "密码文档信息上报数据采集", "功能描述": "上传密码文档文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户点击密码文档信息上报数据采集", "子过程": [{"子过程描述": "输入密码文档信息上报数据采集信息", "数据移动类型": "E", "数据组": "密码文档信息上报数据采集信息", "数据属性": "密码文档信息上报数据采集ID、密码文档信息上报数据采集名称", "CFP": 1}, {"子过程描述": "获取密码文档信息上报数据", "数据移动类型": "R", "数据组": "密码文档信息上报数据", "数据属性": "密码文档信息上报数据ID、密码文档信息上报数据名称、密码文档信息上报数据类型", "CFP": 1}, {"子过程描述": "密码文档信息上报数据采集入库", "数据移动类型": "W", "数据组": "密码文档信息上报数据采集入库信息", "数据属性": "密码文档信息上报数据采集结果、密码文档信息上报数据采集时间", "CFP": 1}, {"子过程描述": "返回展示密码文档信息上报数据采集结果", "数据移动类型": "X", "数据组": "密码文档信息上报数据采集结果", "数据属性": "密码文档信息上报数据采集内容、密码文档信息上报数据采集结果等", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码文档信息上报", "功能过程": "密码文档信息上报接口对接", "功能描述": "上传密码文档文件", "预估工作量（人天）": 6, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户点击密码文档信息上报接口对接", "子过程": [{"子过程描述": "输入密码文档信息上报接口对接信息", "数据移动类型": "E", "数据组": "密码文档信息上报接口对接信息", "数据属性": "密码文档信息上报接口对接ID、密码文档信息上报接口对接名称", "CFP": 1}, {"子过程描述": "获取密码文档信息上报接口", "数据移动类型": "R", "数据组": "密码文档信息上报接口", "数据属性": "密码文档信息上报接口ID、密码文档信息上报接口名称、密码文档信息上报接口类型", "CFP": 1}, {"子过程描述": "密码文档信息上报接口参数验证", "数据移动类型": "R", "数据组": "密码文档信息上报接口参数", "数据属性": "密码文档信息上报接口参数ID、密码文档信息上报接口参数名称", "CFP": 1}, {"子过程描述": "密码文档信息上报接口对接", "数据移动类型": "X", "数据组": "密码文档信息上报接口对接结果", "数据属性": "密码文档信息上报接口对接内容、密码文档信息上报接口对接结果等", "CFP": 1}, {"子过程描述": "密码文档信息上报接口对接数据归档", "数据移动类型": "W", "数据组": "密码文档信息上报接口对接数据", "数据属性": "密码文档信息上报接口对接数据名称、密码文档信息上报接口对接数据类型、密码文档信息上报接口对接数据ID", "CFP": 1}, {"子过程描述": "输出密码文档信息上报接口对接结果", "数据移动类型": "X", "数据组": "密码文档信息上报接口对接内容", "数据属性": "密码文档信息上报接口对接内容、密码文档信息上报接口对接结果等", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码文档信息上报", "功能过程": "密码文档信息上报数据列表展示", "功能描述": "上传密码文档文件", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户点击密码文档信息上报数据列表", "子过程": [{"子过程描述": "输入密码文档信息上报数据列表查询条件", "数据移动类型": "E", "数据组": "密码文档信息上报数据列表查询请求", "数据属性": "密码文档信息上报数据列表查询条件、密码文档信息上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取密码文档信息上报数据列表", "数据移动类型": "R", "数据组": "密码文档信息上报数据列表", "数据属性": "密码文档信息上报数据列表名称、密码文档信息上报数据列表类型、密码文档信息上报数据列表ID", "CFP": 1}, {"子过程描述": "返回密码文档信息上报数据列表查询结果展示", "数据移动类型": "X", "数据组": "密码文档信息上报数据列表查询结果", "数据属性": "密码文档信息上报数据列表名称、密码文档信息上报数据列表类型、密码文档信息上报数据列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码文档信息上报", "功能过程": "密码文档信息上报数据定时上报更新", "功能描述": "上传密码文档文件", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户点击密码文档信息上报数据定时上报更新", "子过程": [{"子过程描述": "获取密码文档信息上报数据定时上报更新配置", "数据移动类型": "R", "数据组": "密码文档信息上报数据定时上报更新配置", "数据属性": "密码文档信息上报数据定时上报更新配置名称、密码文档信息上报数据定时上报更新配置类型、密码文档信息上报数据定时上报更新配置ID", "CFP": 1}, {"子过程描述": "获取需要定时上报更新的密码文档信息上报数据", "数据移动类型": "R", "数据组": "密码文档信息上报数据定时上报更新内容", "数据属性": "密码文档信息上报数据定时上报更新内容名称、密码文档信息上报数据定时上报更新内容类型、密码文档信息上报数据定时上报更新内容ID", "CFP": 1}, {"子过程描述": "密码文档信息上报数据定时上报更新", "数据移动类型": "X", "数据组": "密码文档信息上报数据定时上报更新结果", "数据属性": "密码文档信息上报数据定时上报更新结果名称、密码文档信息上报数据定时上报更新结果类型、密码文档信息上报数据定时上报更新结果ID", "CFP": 1}, {"子过程描述": "保存密码文档信息上报数据定时上报更新记录", "数据移动类型": "W", "数据组": "密码文档信息上报数据定时上报更新记录", "数据属性": "密码文档信息上报数据定时上报更新结果名称、密码文档信息上报数据定时上报更新结果类型、密码文档信息上报数据定时上报更新结果ID", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码资产数据上报类接口", "三级功能模块": "密码文档信息上报", "功能过程": "密码文档文件上传", "功能描述": "上传密码文档文件", "预估工作量（人天）": 5, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户点击密码文档文件上传", "子过程": [{"子过程描述": "发起密码文档文件上传请求", "数据移动类型": "E", "数据组": "密码文档文件上传请求", "数据属性": "密码文档文件上传请求", "CFP": 1}, {"子过程描述": "获取密码文档文件", "数据移动类型": "R", "数据组": "密码文档文件", "数据属性": "密码文档文件名称、密码文档文件类型、密码文档文件ID", "CFP": 1}, {"子过程描述": "密码文档文件上传", "数据移动类型": "W", "数据组": "密码文档文件上传结果", "数据属性": "密码文档文件名称、密码文档文件类型、密码文档文件上传时间", "CFP": 1}, {"子过程描述": "返回展示密码文档文件上传内容", "数据移动类型": "X", "数据组": "密码文档文件上传内容", "数据属性": "密码文档文件名称、密码文档文件类型、密码文档文件上传结果", "CFP": 1}, {"子过程描述": "记录密码文档文件上传日志", "数据移动类型": "W", "数据组": "密码文档文件上传日志", "数据属性": "密码文档文件名称、密码文档文件类型、密码文档文件上传时间、操作人", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用测评数据上报类接口", "三级功能模块": "应用测评信息上报", "功能过程": "密码应用测评上报数据采集", "功能描述": "根据配置的上报频率，定时上报更新密码应用测评数据信息数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码应用测评数据采集页面点击采集按钮", "子过程": [{"子过程描述": "输入密码应用测评数据采集指令", "数据移动类型": "E", "数据组": "密码应用测评数据采集指令", "数据属性": "密码应用测评数据采集指令", "CFP": 1}, {"子过程描述": "获取密码应用测评数据", "数据移动类型": "R", "数据组": "密码应用测评数据", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果等", "CFP": 1}, {"子过程描述": "密码应用测评数据采集入库", "数据移动类型": "W", "数据组": "密码应用测评数据采集结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、采集时间等", "CFP": 1}, {"子过程描述": "返回展示密码应用测评数据采集结果", "数据移动类型": "X", "数据组": "密码应用测评数据采集内容", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、采集内容等", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用测评数据上报类接口", "三级功能模块": "应用测评信息上报", "功能过程": "密码应用测评数据上报接口对接", "功能描述": "根据配置的上报频率，定时上报更新密码应用测评数据信息数据", "预估工作量（人天）": 6, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码应用测评数据上报页面点击上报按钮", "子过程": [{"子过程描述": "发起密码应用测评数据上报请求", "数据移动类型": "E", "数据组": "密码应用测评数据上报请求", "数据属性": "密码应用测评数据上报请求", "CFP": 1}, {"子过程描述": "获取密码应用测评数据", "数据移动类型": "R", "数据组": "密码应用测评数据", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果等", "CFP": 1}, {"子过程描述": "密码应用测评数据上报", "数据移动类型": "X", "数据组": "密码应用测评数据上报结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、上报时间等", "CFP": 1}, {"子过程描述": "密码应用测评数据上报结果入库", "数据移动类型": "W", "数据组": "密码应用测评数据上报结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、上报结果等", "CFP": 1}, {"子过程描述": "密码应用测评数据上报结果推送", "数据移动类型": "X", "数据组": "密码应用测评数据上报结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、推送内容等", "CFP": 1}, {"子过程描述": "记录密码应用测评数据上报日志", "数据移动类型": "W", "数据组": "密码应用测评数据上报日志", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、系统时间等", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用测评数据上报类接口", "三级功能模块": "应用测评信息上报", "功能过程": "密码应用测评上报数据列表展示", "功能描述": "根据配置的上报频率，定时上报更新密码应用测评数据信息数据", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "用户在密码应用测评数据列表页面点击列表", "子过程": [{"子过程描述": "输入密码应用测评数据列表查询条件", "数据移动类型": "E", "数据组": "密码应用测评数据列表查询请求", "数据属性": "密码应用测评数据列表查询条件、密码应用测评数据列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用测评数据列表", "数据移动类型": "R", "数据组": "密码应用测评数据列表", "数据属性": "密码应用测评数据列表名称、密码应用测评数据列表类型、密码应用测评数据列表ID", "CFP": 1}, {"子过程描述": "返回密码应用测评数据列表查询结果展示", "数据移动类型": "X", "数据组": "密码应用测评数据列表查询结果", "数据属性": "密码应用测评数据列表名称、密码应用测评数据列表类型、密码应用测评数据列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用测评数据上报类接口", "三级功能模块": "应用测评信息上报", "功能过程": "密码应用测评数据定时上报更新", "功能描述": "根据配置的上报频率，定时上报更新密码应用测评数据信息数据", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密服平台-数据上报接口模块", "触发事件": "定时任务触发密码应用测评数据上报", "子过程": [{"子过程描述": "定时任务触发密码应用测评数据上报", "数据移动类型": "E", "数据组": "密码应用测评数据定时任务", "数据属性": "密码应用测评数据定时任务名称、密码应用测评数据定时任务类型、密码应用测评数据定时任务ID", "CFP": 1}, {"子过程描述": "获取密码应用测评数据", "数据移动类型": "R", "数据组": "密码应用测评数据", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果等", "CFP": 1}, {"子过程描述": "密码应用测评数据上报", "数据移动类型": "X", "数据组": "密码应用测评数据上报结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、上报时间等", "CFP": 1}, {"子过程描述": "密码应用测评数据上报结果入库", "数据移动类型": "W", "数据组": "密码应用测评数据上报结果", "数据属性": "应用ID、应用名称、密评ID、密评名称、密评类型、密评结果、上报结果等", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "密码应用漏洞/安全事件上报数据采集", "功能描述": "补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用漏洞/安全事件上报页面点击数据采集按钮", "子过程": [{"子过程描述": "输入密码应用漏洞/安全事件上报数据采集指令", "数据移动类型": "E", "数据组": "密码应用漏洞/安全事件上报数据采集指令", "数据属性": "密码应用漏洞/安全事件上报数据采集请求、密码应用漏洞/安全事件上报数据采集参数", "CFP": 1}, {"子过程描述": "获取密码应用漏洞/安全事件上报数据", "数据移动类型": "R", "数据组": "密码应用漏洞/安全事件上报数据", "数据属性": "密码应用漏洞/安全事件上报数据名称、密码应用漏洞/安全事件上报数据类型、密码应用漏洞/安全事件上报数据ID", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件上报数据采集入库", "数据移动类型": "W", "数据组": "密码应用漏洞/安全事件上报数据采集结果", "数据属性": "密码应用漏洞/安全事件上报数据名称、密码应用漏洞/安全事件上报数据类型、密码应用漏洞/安全事件上报数据ID、采集时间", "CFP": 1}, {"子过程描述": "返回展示密码应用漏洞/安全事件上报数据采集结果", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件上报数据采集内容", "数据属性": "密码应用漏洞/安全事件上报数据名称、密码应用漏洞/安全事件上报数据类型、密码应用漏洞/安全事件上报数据ID、采集结果", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "密码应用漏洞/安全事件上报接口对接", "功能描述": "补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息", "预估工作量（人天）": 6, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用漏洞/安全事件上报页面点击接口对接按钮", "子过程": [{"子过程描述": "输入密码应用漏洞/安全事件上报接口对接指令", "数据移动类型": "E", "数据组": "密码应用漏洞/安全事件上报接口对接指令", "数据属性": "密码应用漏洞/安全事件上报接口对接请求、密码应用漏洞/安全事件上报接口对接参数", "CFP": 1}, {"子过程描述": "获取密码应用漏洞/安全事件上报接口", "数据移动类型": "R", "数据组": "密码应用漏洞/安全事件上报接口", "数据属性": "密码应用漏洞/安全事件上报接口名称、密码应用漏洞/安全事件上报接口类型、密码应用漏洞/安全事件上报接口ID", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件上报接口对接", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件上报接口对接结果", "数据属性": "密码应用漏洞/安全事件上报接口名称、密码应用漏洞/安全事件上报接口类型、密码应用漏洞/安全事件上报接口ID、对接结果", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件上报接口对接数据归档", "数据移动类型": "W", "数据组": "密码应用漏洞/安全事件上报接口对接数据", "数据属性": "密码应用漏洞/安全事件上报接口名称、密码应用漏洞/安全事件上报接口类型、密码应用漏洞/安全事件上报接口ID、归档时间", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件上报接口对接数据同步", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件上报接口对接数据", "数据属性": "密码应用漏洞/安全事件上报接口名称、密码应用漏洞/安全事件上报接口类型、密码应用漏洞/安全事件上报接口ID、同步时间", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件上报接口对接数据上报", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件上报接口对接数据", "数据属性": "密码应用漏洞/安全事件上报接口名称、密码应用漏洞/安全事件上报接口类型、密码应用漏洞/安全事件上报接口ID、上报时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "密码应用漏洞/安全事件上报数据列表展示", "功能描述": "补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息", "预估工作量（人天）": 3, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用漏洞/安全事件上报页面点击列表展示按钮", "子过程": [{"子过程描述": "输入密码应用漏洞/安全事件上报数据列表查询条件", "数据移动类型": "E", "数据组": "密码应用漏洞/安全事件上报数据列表查询请求", "数据属性": "密码应用漏洞/安全事件上报数据列表查询条件、密码应用漏洞/安全事件上报数据列表查询项", "CFP": 1}, {"子过程描述": "读取密码应用漏洞/安全事件上报数据列表", "数据移动类型": "R", "数据组": "密码应用漏洞/安全事件上报数据列表", "数据属性": "密码应用漏洞/安全事件上报数据列表名称、密码应用漏洞/安全事件上报数据列表类型、密码应用漏洞/安全事件上报数据列表ID", "CFP": 1}, {"子过程描述": "输出密码应用漏洞/安全事件上报数据列表查询结果", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件上报数据列表查询结果", "数据属性": "密码应用漏洞/安全事件上报数据列表名称、密码应用漏洞/安全事件上报数据列表类型、密码应用漏洞/安全事件上报数据列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "密码应用漏洞/安全事件定时上报更新", "功能描述": "补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用漏洞/安全事件上报页面点击定时上报更新按钮", "子过程": [{"子过程描述": "发起密码应用漏洞/安全事件定时上报更新请求", "数据移动类型": "E", "数据组": "密码应用漏洞/安全事件定时上报更新请求", "数据属性": "密码应用漏洞/安全事件定时上报更新参数", "CFP": 1}, {"子过程描述": "获取密码应用漏洞/安全事件定时上报更新任务", "数据移动类型": "R", "数据组": "密码应用漏洞/安全事件定时上报更新任务", "数据属性": "密码应用漏洞/安全事件定时上报更新任务名称、密码应用漏洞/安全事件定时上报更新任务类型、密码应用漏洞/安全事件定时上报更新任务ID", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件定时上报更新", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件定时上报更新结果", "数据属性": "密码应用漏洞/安全事件定时上报更新结果名称、密码应用漏洞/安全事件定时上报更新结果类型、密码应用漏洞/安全事件定时上报更新结果ID", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件定时上报更新数据归档", "数据移动类型": "W", "数据组": "密码应用漏洞/安全事件定时上报更新数据", "数据属性": "密码应用漏洞/安全事件定时上报更新数据名称、密码应用漏洞/安全事件定时上报更新数据类型、密码应用漏洞/安全事件定时上报更新数据ID、归档时间", "CFP": 1}]}, {"一级功能模块": "数据上报接口", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "密码应用漏洞/安全事件补充", "功能描述": "补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息", "预估工作量（人天）": 4, "功能用户": "发起者：用户，接收者：密码服务管理平台", "触发事件": "用户在密码应用漏洞/安全事件上报页面点击补充按钮", "子过程": [{"子过程描述": "输入密码应用漏洞/安全事件补充信息", "数据移动类型": "E", "数据组": "密码应用漏洞/安全事件补充信息", "数据属性": "密码应用漏洞/安全事件补充ID、密码应用漏洞/安全事件补充名称", "CFP": 1}, {"子过程描述": "密码应用漏洞/安全事件补充保存", "数据移动类型": "W", "数据组": "密码应用漏洞/安全事件补充结果", "数据属性": "密码应用漏洞/安全事件补充结果名称、密码应用漏洞/安全事件补充结果类型、密码应用漏洞/安全事件补充结果ID", "CFP": 1}, {"子过程描述": "获取密码应用漏洞/安全事件补充内容", "数据移动类型": "R", "数据组": "密码应用漏洞/安全事件补充内容", "数据属性": "密码应用漏洞/安全事件补充名称、密码应用漏洞/安全事件补充类型、密码应用漏洞/安全事件补充ID、补充时间", "CFP": 1}, {"子过程描述": "返回展示密码应用漏洞/安全事件补充结果", "数据移动类型": "X", "数据组": "密码应用漏洞/安全事件补充展示数据", "数据属性": "密码应用漏洞/安全事件补充名称、密码应用漏洞/安全事件补充类型、密码应用漏洞/安全事件补充ID、分页数、当前页数", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报", "子过程": [{"子过程描述": "输入漏洞事件上报信息", "数据移动类型": "E", "数据组": "漏洞事件上报信息", "数据属性": "漏洞事件上报ID、漏洞事件上报名称", "CFP": 1}, {"子过程描述": "漏洞事件上报保存", "数据移动类型": "W", "数据组": "漏洞事件上报信息", "数据属性": "漏洞事件上报ID、漏洞事件上报名称、漏洞事件上报类型、漏洞事件上报时间", "CFP": 1}, {"子过程描述": "输出漏洞事件上报结果", "数据移动类型": "X", "数据组": "漏洞事件上报结果", "数据属性": "漏洞事件上报结果", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报", "子过程": [{"子过程描述": "输入安全事件上报信息", "数据移动类型": "E", "数据组": "安全事件上报信息", "数据属性": "安全事件上报ID、安全事件上报名称", "CFP": 1}, {"子过程描述": "安全事件上报保存", "数据移动类型": "W", "数据组": "安全事件上报信息", "数据属性": "安全事件上报ID、安全事件上报名称、安全事件上报类型、安全事件上报时间", "CFP": 1}, {"子过程描述": "输出安全事件上报结果", "数据移动类型": "X", "数据组": "安全事件上报结果", "数据属性": "安全事件上报结果", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报列表查询", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报列表", "子过程": [{"子过程描述": "输入漏洞事件上报列表查询条件", "数据移动类型": "E", "数据组": "漏洞事件上报列表查询请求", "数据属性": "漏洞事件上报列表查询条件、漏洞事件上报列表查询项", "CFP": 1}, {"子过程描述": "获取漏洞事件上报列表", "数据移动类型": "R", "数据组": "漏洞事件上报列表", "数据属性": "漏洞事件上报列表名称、漏洞事件上报列表类型、漏洞事件上报列表ID", "CFP": 1}, {"子过程描述": "返回漏洞事件上报列表查询结果展示", "数据移动类型": "X", "数据组": "漏洞事件上报列表查询结果", "数据属性": "漏洞事件上报列表名称、漏洞事件上报列表类型、漏洞事件上报列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报列表查询", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报列表", "子过程": [{"子过程描述": "输入安全事件上报列表查询条件", "数据移动类型": "E", "数据组": "安全事件上报列表查询请求", "数据属性": "安全事件上报列表查询条件、安全事件上报列表查询项", "CFP": 1}, {"子过程描述": "获取安全事件上报列表", "数据移动类型": "R", "数据组": "安全事件上报列表", "数据属性": "安全事件上报列表名称、安全事件上报列表类型、安全事件上报列表ID", "CFP": 1}, {"子过程描述": "返回安全事件上报列表查询结果展示", "数据移动类型": "X", "数据组": "安全事件上报列表查询结果", "数据属性": "安全事件上报列表名称、安全事件上报列表类型、安全事件上报列表ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报详情查看", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报详情", "子过程": [{"子过程描述": "输入漏洞事件上报详情查询请求", "数据移动类型": "E", "数据组": "漏洞事件上报详情查询请求", "数据属性": "漏洞事件上报详情查询条件", "CFP": 1}, {"子过程描述": "读取漏洞事件上报详情", "数据移动类型": "R", "数据组": "漏洞事件上报详情", "数据属性": "漏洞事件上报详情名称、漏洞事件上报详情类型、漏洞事件上报详情ID", "CFP": 1}, {"子过程描述": "输出漏洞事件上报详情查询结果", "数据移动类型": "X", "数据组": "漏洞事件上报详情查询结果", "数据属性": "漏洞事件上报详情名称、漏洞事件上报详情类型、漏洞事件上报详情ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报详情查看", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报详情", "子过程": [{"子过程描述": "输入安全事件上报详情查询请求", "数据移动类型": "E", "数据组": "安全事件上报详情查询请求", "数据属性": "安全事件上报详情查询条件", "CFP": 1}, {"子过程描述": "读取安全事件上报详情", "数据移动类型": "R", "数据组": "安全事件上报详情", "数据属性": "安全事件上报详情名称、安全事件上报详情类型、安全事件上报详情ID", "CFP": 1}, {"子过程描述": "输出安全事件上报详情查询结果", "数据移动类型": "X", "数据组": "安全事件上报详情查询结果", "数据属性": "安全事件上报详情名称、安全事件上报详情类型、安全事件上报详情ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报内容查看", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报内容", "子过程": [{"子过程描述": "输入漏洞事件上报内容查询请求", "数据移动类型": "E", "数据组": "漏洞事件上报内容查询请求", "数据属性": "漏洞事件上报内容查询条件", "CFP": 1}, {"子过程描述": "获取漏洞事件上报内容", "数据移动类型": "R", "数据组": "漏洞事件上报内容", "数据属性": "漏洞事件上报内容名称、漏洞事件上报内容类型、漏洞事件上报内容ID", "CFP": 1}, {"子过程描述": "返回漏洞事件上报内容查询结果", "数据移动类型": "X", "数据组": "漏洞事件上报内容查询结果", "数据属性": "漏洞事件上报内容名称、漏洞事件上报内容类型、漏洞事件上报内容ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报内容查看", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报内容", "子过程": [{"子过程描述": "输入安全事件上报内容查询请求", "数据移动类型": "E", "数据组": "安全事件上报内容查询请求", "数据属性": "安全事件上报内容查询条件", "CFP": 1}, {"子过程描述": "获取安全事件上报内容", "数据移动类型": "R", "数据组": "安全事件上报内容", "数据属性": "安全事件上报内容名称、安全事件上报内容类型、安全事件上报内容ID", "CFP": 1}, {"子过程描述": "返回安全事件上报内容查询结果", "数据移动类型": "X", "数据组": "安全事件上报内容查询结果", "数据属性": "安全事件上报内容名称、安全事件上报内容类型、安全事件上报内容ID、查询时间", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报统计", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报统计", "子过程": [{"子过程描述": "发起漏洞事件上报统计请求", "数据移动类型": "E", "数据组": "漏洞事件上报统计请求", "数据属性": "漏洞事件上报统计参数", "CFP": 1}, {"子过程描述": "漏洞事件上报统计", "数据移动类型": "R", "数据组": "漏洞事件上报统计维度", "数据属性": "漏洞事件上报统计维度", "CFP": 1}, {"子过程描述": "返回展示漏洞事件上报统计结果", "数据移动类型": "X", "数据组": "漏洞事件上报统计结果", "数据属性": "漏洞事件上报统计名称、漏洞事件上报统计类型、漏洞事件上报统计ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报统计", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报统计", "子过程": [{"子过程描述": "发起安全事件上报统计请求", "数据移动类型": "E", "数据组": "安全事件上报统计请求", "数据属性": "安全事件上报统计参数", "CFP": 1}, {"子过程描述": "安全事件上报统计", "数据移动类型": "R", "数据组": "安全事件上报统计维度", "数据属性": "安全事件上报统计维度", "CFP": 1}, {"子过程描述": "返回展示安全事件上报统计结果", "数据移动类型": "X", "数据组": "安全事件上报统计结果", "数据属性": "安全事件上报统计名称、安全事件上报统计类型、安全事件上报统计ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报分析", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报分析", "子过程": [{"子过程描述": "发起漏洞事件上报分析请求", "数据移动类型": "E", "数据组": "漏洞事件上报分析请求", "数据属性": "漏洞事件上报分析参数", "CFP": 1}, {"子过程描述": "漏洞事件上报分析", "数据移动类型": "R", "数据组": "漏洞事件上报分析维度", "数据属性": "漏洞事件上报分析维度", "CFP": 1}, {"子过程描述": "返回展示漏洞事件上报分析结果", "数据移动类型": "X", "数据组": "漏洞事件上报分析结果", "数据属性": "漏洞事件上报分析名称、漏洞事件上报分析类型、漏洞事件上报分析ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报分析", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报分析", "子过程": [{"子过程描述": "发起安全事件上报分析请求", "数据移动类型": "E", "数据组": "安全事件上报分析请求", "数据属性": "安全事件上报分析参数", "CFP": 1}, {"子过程描述": "安全事件上报分析", "数据移动类型": "R", "数据组": "安全事件上报分析维度", "数据属性": "安全事件上报分析维度", "CFP": 1}, {"子过程描述": "返回展示安全事件上报分析结果", "数据移动类型": "X", "数据组": "安全事件上报分析结果", "数据属性": "安全事件上报分析名称、安全事件上报分析类型、安全事件上报分析ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报下载", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报下载", "子过程": [{"子过程描述": "发起漏洞事件上报下载请求", "数据移动类型": "E", "数据组": "漏洞事件上报下载请求", "数据属性": "漏洞事件上报下载指令", "CFP": 1}, {"子过程描述": "漏洞事件上报下载", "数据移动类型": "R", "数据组": "漏洞事件上报下载内容", "数据属性": "漏洞事件上报文件格式、漏洞事件上报文件名称", "CFP": 1}, {"子过程描述": "返回漏洞事件上报下载结果", "数据移动类型": "X", "数据组": "漏洞事件上报下载结果", "数据属性": "漏洞事件上报下载内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报下载", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报下载", "子过程": [{"子过程描述": "发起安全事件上报下载请求", "数据移动类型": "E", "数据组": "安全事件上报下载请求", "数据属性": "安全事件上报下载指令", "CFP": 1}, {"子过程描述": "安全事件上报下载", "数据移动类型": "R", "数据组": "安全事件上报下载内容", "数据属性": "安全事件上报文件格式、安全事件上报文件名称", "CFP": 1}, {"子过程描述": "返回安全事件上报下载结果", "数据移动类型": "X", "数据组": "安全事件上报下载结果", "数据属性": "安全事件上报下载内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报导入", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报导入", "子过程": [{"子过程描述": "发起漏洞事件上报导入请求", "数据移动类型": "E", "数据组": "漏洞事件上报导入请求", "数据属性": "漏洞事件上报导入指令", "CFP": 1}, {"子过程描述": "漏洞事件上报导入", "数据移动类型": "W", "数据组": "漏洞事件上报导入内容", "数据属性": "漏洞事件上报文件格式、漏洞事件上报文件名称", "CFP": 1}, {"子过程描述": "返回漏洞事件上报导入结果", "数据移动类型": "X", "数据组": "漏洞事件上报导入结果", "数据属性": "漏洞事件上报导入内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报导入", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报导入", "子过程": [{"子过程描述": "发起安全事件上报导入请求", "数据移动类型": "E", "数据组": "安全事件上报导入请求", "数据属性": "安全事件上报导入指令", "CFP": 1}, {"子过程描述": "安全事件上报导入", "数据移动类型": "W", "数据组": "安全事件上报导入内容", "数据属性": "安全事件上报文件格式、安全事件上报文件名称", "CFP": 1}, {"子过程描述": "返回安全事件上报导入结果", "数据移动类型": "X", "数据组": "安全事件上报导入结果", "数据属性": "安全事件上报导入内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报导出", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报导出", "子过程": [{"子过程描述": "发起漏洞事件上报导出请求", "数据移动类型": "E", "数据组": "漏洞事件上报导出请求", "数据属性": "漏洞事件上报导出指令", "CFP": 1}, {"子过程描述": "漏洞事件上报导出", "数据移动类型": "R", "数据组": "漏洞事件上报导出内容", "数据属性": "漏洞事件上报文件格式、漏洞事件上报文件名称", "CFP": 1}, {"子过程描述": "返回漏洞事件上报导出结果", "数据移动类型": "X", "数据组": "漏洞事件上报导出结果", "数据属性": "漏洞事件上报导出内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报导出", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报导出", "子过程": [{"子过程描述": "发起安全事件上报导出请求", "数据移动类型": "E", "数据组": "安全事件上报导出请求", "数据属性": "安全事件上报导出指令", "CFP": 1}, {"子过程描述": "安全事件上报导出", "数据移动类型": "R", "数据组": "安全事件上报导出内容", "数据属性": "安全事件上报文件格式、安全事件上报文件名称", "CFP": 1}, {"子过程描述": "返回安全事件上报导出结果", "数据移动类型": "X", "数据组": "安全事件上报导出结果", "数据属性": "安全事件上报导出内容", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报分页", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报分页", "子过程": [{"子过程描述": "输入漏洞事件上报分页数", "数据移动类型": "E", "数据组": "漏洞事件上报分页信息", "数据属性": "漏洞事件上报分页数", "CFP": 1}, {"子过程描述": "获取漏洞事件上报分页", "数据移动类型": "R", "数据组": "漏洞事件上报分页", "数据属性": "漏洞事件上报分页数、漏洞事件上报分页大小", "CFP": 1}, {"子过程描述": "返回漏洞事件上报分页结果", "数据移动类型": "X", "数据组": "漏洞事件上报分页结果", "数据属性": "漏洞事件上报分页数、漏洞事件上报分页大小、漏洞事件上报分页ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报分页", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报分页", "子过程": [{"子过程描述": "输入安全事件上报分页数", "数据移动类型": "E", "数据组": "安全事件上报分页信息", "数据属性": "安全事件上报分页数", "CFP": 1}, {"子过程描述": "获取安全事件上报分页", "数据移动类型": "R", "数据组": "安全事件上报分页", "数据属性": "安全事件上报分页数、安全事件上报分页大小", "CFP": 1}, {"子过程描述": "返回安全事件上报分页结果", "数据移动类型": "X", "数据组": "安全事件上报分页结果", "数据属性": "安全事件上报分页数、安全事件上报分页大小、安全事件上报分页ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "漏洞事件上报", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击漏洞事件上报", "子过程": [{"子过程描述": "发起漏洞事件上报请求", "数据移动类型": "E", "数据组": "漏洞事件上报请求", "数据属性": "漏洞事件上报请求", "CFP": 1}, {"子过程描述": "漏洞事件上报", "数据移动类型": "R", "数据组": "漏洞事件上报信息", "数据属性": "漏洞事件上报名称、漏洞事件上报类型、漏洞事件上报ID", "CFP": 1}, {"子过程描述": "返回漏洞事件上报结果", "数据移动类型": "X", "数据组": "漏洞事件上报结果", "数据属性": "漏洞事件上报名称、漏洞事件上报类型、漏洞事件上报ID", "CFP": 1}]}, {"一级功能模块": "合计", "二级功能模块": "密码应用漏洞/安全事件上报类接口", "三级功能模块": "密码应用漏洞/安全事件上报", "功能过程": "安全事件上报", "功能描述": "nan", "预估工作量（人天）": "1053", "功能用户": "发起者：用户，接收者：密码服务平台", "触发事件": "用户点击安全事件上报", "子过程": [{"子过程描述": "发起安全事件上报请求", "数据移动类型": "E", "数据组": "安全事件上报请求", "数据属性": "安全事件上报请求", "CFP": 1}, {"子过程描述": "安全事件上报", "数据移动类型": "R", "数据组": "安全事件上报信息", "数据属性": "安全事件上报名称、安全事件上报类型、安全事件上报ID", "CFP": 1}, {"子过程描述": "返回安全事件上报结果", "数据移动类型": "X", "数据组": "安全事件上报结果", "数据属性": "安全事件上报名称、安全事件上报类型、安全事件上报ID", "CFP": 1}]}]