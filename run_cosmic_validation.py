#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验功能使用示例

这个脚本演示如何使用cosmic_validator模块来校验COSMIC功能拆解数据
"""

import os
import sys
from cosmic_validator import CosmicValidator
import config


def main():
    """主函数"""
    print("COSMIC功能拆解校验工具")
    print("=" * 50)
    
    # 检查输入文件
    csv_file = "output-new.csv"
    if not os.path.exists(csv_file):
        print(f"错误: 找不到输入文件 {csv_file}")
        print("请确保CSV文件存在于当前目录")
        return
    
    # 检查提示词文件
    prompt_file = "check_prompt.md"
    if not os.path.exists(prompt_file):
        print(f"错误: 找不到提示词文件 {prompt_file}")
        print("请确保提示词文件存在于当前目录")
        return
    
    # 确保输出目录存在
    os.makedirs("debug", exist_ok=True)
    
    print(f"输入文件: {csv_file}")
    print(f"提示词文件: {prompt_file}")
    print(f"输出目录: debug/")
    print()
    
    # 初始化校验器
    print("初始化COSMIC校验器...")
    validator = CosmicValidator(config)
    
    # 执行校验
    print("开始校验...")
    result = validator.validate_cosmic_data(csv_file, prompt_file)
    
    if "error" in result:
        print(f"校验失败: {result['error']}")
        return
    
    print("校验完成！")
    print()
    
    # 显示摘要信息
    input_summary = result.get('input_summary', {})
    print("输入数据摘要:")
    print(f"  总记录数: {input_summary.get('total_records', 0)}")
    print(f"  一级模块数: {len(input_summary.get('level1_modules', []))}")
    print(f"  二级模块数: {len(input_summary.get('level2_modules', []))}")
    print(f"  三级模块数: {len(input_summary.get('level3_modules', []))}")
    
    # 数据移动类型分布
    data_movements = input_summary.get('data_movement_types', {})
    if data_movements:
        print(f"  数据移动类型分布: {data_movements}")
    
    # 功能过程摘要
    fp_summary = result.get('function_process_summary', {})
    if fp_summary:
        total = fp_summary.get('total_processes', 0)
        complete = fp_summary.get('complete_processes', 0)
        incomplete = fp_summary.get('incomplete_processes', 0)
        
        print()
        print("功能过程摘要:")
        print(f"  总功能过程数: {total}")
        print(f"  完整功能过程数: {complete}")
        print(f"  不完整功能过程数: {incomplete}")
        
        if total > 0:
            completion_rate = (complete / total) * 100
            print(f"  完整性比率: {completion_rate:.1f}%")
    
    # 校验结果预览
    validation_result = result.get('validation_result', {})
    if isinstance(validation_result, dict):
        overall = validation_result.get('overall_assessment', {})
        if overall:
            print()
            print("校验结果预览:")
            print(f"  合规率: {overall.get('compliance_rate', '未知')}")
            print(f"  重大问题数: {overall.get('major_issues_count', 0)}")
            print(f"  轻微问题数: {overall.get('minor_issues_count', 0)}")
            
            # 显示前几个问题
            findings = validation_result.get('detailed_findings', [])
            if findings:
                print()
                print("主要问题示例:")
                for i, finding in enumerate(findings[:3], 1):
                    print(f"  {i}. {finding.get('module_path', '未知路径')}")
                    print(f"     问题: {finding.get('issue_description', '无描述')}")
                    print(f"     建议: {finding.get('suggested_fix', '无建议')}")
                    print()
                
                if len(findings) > 3:
                    print(f"  ... 还有 {len(findings) - 3} 个问题")
    
    # 保存结果
    print("保存校验结果...")
    validator.save_validation_result(result)
    validator.save_validation_report(result)
    
    print()
    print("校验完成！输出文件:")
    print("  - debug/cosmic_validation_result.json  (详细JSON结果)")
    print("  - debug/cosmic_validation_report.txt   (人类可读报告)")
    print("  - debug/cosmic_validation_input.json   (输入数据调试)")
    
    # 给出后续建议
    print()
    print("后续建议:")
    print("1. 查看详细报告了解所有问题")
    print("2. 根据修改建议调整功能拆解")
    print("3. 重新运行校验确认修改效果")


if __name__ == "__main__":
    main()
