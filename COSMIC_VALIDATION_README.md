# COSMIC功能拆解校验工具

## 概述

本工具实现了COSMIC功能拆解的自动化校验功能，使用大模型检查用户提交的COSMIC功能拆解是否符合规范。

## 功能特性

1. **CSV数据解析**: 解析CSV格式的COSMIC功能拆解数据
2. **层次化JSON转换**: 将CSV数据组织为层次化JSON结构，减少提示词使用
3. **大模型校验**: 使用大模型对整个COSMIC数据进行一次性校验
4. **智能建议**: 对不符合规范的功能过程或子过程给出具体修改建议
5. **多格式输出**: 提供JSON和人类可读的校验报告

## 文件结构

```
cosmic/
├── cosmic_validator.py          # 核心校验模块
├── check_prompt.md             # 系统提示词文件
├── run_cosmic_validation.py    # 使用示例脚本
├── config.py                   # 配置文件（已扩展）
├── output-new.csv              # 示例输入数据
└── debug/                      # 输出目录
    ├── cosmic_validation_result.json    # 详细JSON结果
    ├── cosmic_validation_report.txt     # 人类可读报告
    └── cosmic_validation_input.json     # 输入数据调试文件
```

## 配置说明

在 `config.py` 中新增了以下配置项：

### COSMIC校验专用配置
```python
# 校验专用的大模型配置（如果不设置，则使用默认配置）
CHECK_ENDPOINT_URL = None  # 校验专用API端点
CHECK_MODEL_NAME = None    # 校验专用模型名称  
CHECK_API_KEY = None       # 校验专用API密钥
CHECK_API_QPM = None       # 校验专用QPM限制
CHECK_API_TPM = None       # 校验专用TPM限制
```

### 校验功能特定配置
```python
CHECK_EXCLUDED_FIELDS = [   # 在校验时需要去除的字段列表
    '预估工作量（人天）',      # 去除预估工作量字段，减少提示词使用
]
```

## 使用方法

### 方法1: 使用示例脚本（推荐）
```bash
python run_cosmic_validation.py
```

### 方法2: 直接调用核心模块
```bash
python cosmic_validator.py
```

### 方法3: 在代码中使用
```python
from cosmic_validator import CosmicValidator
import config

# 初始化校验器
validator = CosmicValidator(config)

# 执行校验
result = validator.validate_cosmic_data("your_file.csv")

# 保存结果
validator.save_validation_result(result)
validator.save_validation_report(result)
```

## 校验标准

工具基于以下COSMIC方法论的4个核心检查点进行校验：

### 1. 完整性检查（最高优先级）
- 每个功能过程是否包含至少1个E（输入）和1个X（输出）
- 识别缺少输入或输出的功能过程

### 2. 数据组聚合检查（高优先级）
- 是否将关联数据属性合并为最小单元
- 检查数据组命名的一致性
- 避免同一业务实体被拆分为多个数据组

### 3. 存储边界检查（中等优先级）
- R/W是否仅针对边界内持久存储
- 区分系统内部存储操作和跨边界数据移动

### 4. 无重复计数（中等优先级）
- 同一数据组在同一功能过程中是否被重复计数
- 确保CFP计算的准确性

## 输出说明

### 校验报告格式
```
================================================================================
COSMIC功能拆解校验报告
================================================================================
生成时间: 2025-07-26 18:16:47

输入数据摘要:
  总记录数: 1131
  一级模块数: 7
  二级模块数: 26
  三级模块数: 56
  数据移动类型分布: {'E': 312, 'R': 336, 'W': 247, 'X': 236}
  CFP分布: {1: 1131}

功能过程摘要:
  总功能过程数: 295
  完整功能过程数: 195
  不完整功能过程数: 100
  完整性比率: 66.1%

整体评估:
  合规率: 66.1%
  重大问题数: 100
  轻微问题数: 25

详细问题发现:
  1. 系统管理/总部一级平台对接/总部平台HTTPS对接
     功能过程: 总部平台上报路径查看
     问题类型: 完整性
     严重程度: 高
     问题描述: 缺少E（输入）
     修改建议: 增加E类型数据组『上报路径查询请求』
```

### JSON结果结构
```json
{
  "overall_assessment": {
    "total_records": "总记录数",
    "compliance_rate": "合规率（百分比）",
    "major_issues_count": "重大问题数量",
    "minor_issues_count": "轻微问题数量"
  },
  "detailed_findings": [
    {
      "module_path": "一级模块/二级模块/三级模块",
      "function_process": "功能过程名称",
      "issue_type": "问题类型",
      "severity": "严重程度",
      "issue_description": "问题描述",
      "suggested_fix": "修改建议"
    }
  ],
  "summary_recommendations": ["总体建议"],
  "best_practices": ["最佳实践建议"]
}
```

## 性能优化

- **数据简化**: 当数据量过大时，自动进行简化处理，优先校验不完整的功能过程
- **限流处理**: 自动处理API调用限流，避免超过QPM/TPM限制
- **批量处理**: 支持大量数据的高效处理

## 注意事项

1. 确保 `output-new.csv` 文件格式正确，包含必要的列
2. 确保 `check_prompt.md` 提示词文件存在
3. 检查网络连接和API配置
4. 大数据量可能需要较长处理时间

## 故障排除

### 常见问题
1. **文件不存在**: 确保CSV文件和提示词文件在正确位置
2. **API调用失败**: 检查网络连接和API配置
3. **数据解析错误**: 检查CSV文件格式和编码
4. **内存不足**: 对于大文件，工具会自动进行数据简化

### 调试方法
- 查看 `debug/` 目录下的调试文件
- 检查 `cosmic_validation_input.json` 了解输入数据结构
- 查看控制台输出的详细信息

## 扩展功能

可以通过修改以下文件来扩展功能：
- `check_prompt.md`: 调整校验标准和提示词
- `config.py`: 修改配置参数
- `cosmic_validator.py`: 添加新的校验逻辑

## 技术支持

如有问题，请检查：
1. 配置文件是否正确
2. 输入数据格式是否符合要求
3. 网络连接是否正常
4. API配额是否充足
