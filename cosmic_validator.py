#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能拆解校验模块

该模块实现以下功能：
1. 支持CSV和JSON格式的cosmic功能拆解数据输入
2. CSV文件直接作为大模型输入，不进行JSON转换
3. JSON文件按原有逻辑处理
4. 调用大模型进行cosmic规范校验
5. 输出校验结果和修改建议
"""

import csv
import json
import pandas as pd
import requests
import time
import math
import os
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, OrderedDict
import config
from main import call_LLM

class CosmicValidator:
    """COSMIC功能拆解校验器"""
    
    def __init__(self, config):
        """
        初始化校验器

        Args:
            config: 配置模块，包含大模型配置等
        """
        self.config = config

        # 使用校验专用配置，如果为None则使用默认配置
        self.endpoint_url = getattr(config, 'CHECK_ENDPOINT_URL', None) or config.ENDPOINT_URL
        self.model_name = getattr(config, 'CHECK_MODEL_NAME', None) or config.MODEL_NAME
        self.api_key = getattr(config, 'CHECK_API_KEY', None) or config.API_KEY
        self.api_qpm = getattr(config, 'CHECK_API_QPM', None) or config.API_QPM
        self.api_tpm = getattr(config, 'CHECK_API_TPM', None) or config.API_TPM

        # 可配置的字段过滤列表
        self.excluded_fields = getattr(config, 'CHECK_EXCLUDED_FIELDS', [
            '预估工作量（人天）'  # 默认去除预估工作量字段
        ])

        # 限流状态
        self.qpm_counter = 0
        self.tpm_counter = 0
        self.window_start_time = time.time()
    
    def validate_cosmic_data(self, file_path: str, prompt_file_path: str = "check_prompt.md") -> Dict[str, Any]:
        """
        校验cosmic数据，支持CSV和JSON格式

        Args:
            file_path: 数据文件路径（CSV或JSON）
            prompt_file_path: 提示词文件路径

        Returns:
            校验结果
        """
        # 检测文件类型
        file_extension = os.path.splitext(file_path)[1].lower()

        if file_extension == '.csv':
            df = pd.read_csv(file_path, encoding='utf-8-sig')
        elif file_extension == '.xlsx':
             # 读取XLS文件内容
            sheet_name = 0
            header = 0
            df = pd.read_excel(file_path, sheet_name = sheet_name, header = header)
            # 向后填充有合并的数据列
            level_1, level_2, level_3, func_user,event_name,func_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
            df[[level_1, level_2, level_3, func_user,event_name,func_name]] = df[[level_1, level_2, level_3, func_user,event_name,func_name]].ffill()
        else:
            return {"error": f"不支持的文件格式: {file_extension}，仅支持 .csv 和 .xlsx 文件"}

        # 过滤掉不需要的字段
        for field in self.excluded_fields:
            if field in df.columns:
                df = df.drop(field, axis=1)

        print("读取系统提示词...")
        try:
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt = f.read()
        except FileNotFoundError:
            return {"error": f"提示词文件 {prompt_file_path} 不存在"}
        try:
            # 文件的文本内容
            #file_content = df.to_csv(index=False, encoding='utf-8-sig')

            # 构建基本统计信息
            summary_info = self._generate_csv_summary(df)

        except Exception as e:
            return {"error": f"CSV文件处理失败: {e}"}

        return self._process_data_in_batches(df, prompt, summary_info)
    
    def _process_data_in_batches(self, df: pd.DataFrame, prompt: str, summary_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        分批次处理cosmic数据，每个批次都带上header

        Args:
            df: DataFrame格式的cosmic数据
            prompt: 系统提示词
            summary_info: 摘要信息

        Returns:
            合并后的校验结果
        """
        # 将DataFrame转换为CSV格式的行数据
        csv_content = df.to_csv(index=False, encoding='utf-8')
        lines = csv_content.strip().split('\n')

        if len(lines) <= 1:
            return {"error": "数据为空或只有header"}

        header = lines[0]
        data_lines = lines[1:]

        # 批次大小
        batch_size = config.CHECK_BATCH_COUNT
        total_batches = math.ceil(len(data_lines) / batch_size)

        print(f"CSV数据将分为 {total_batches} 个批次处理，每批次约 {batch_size} 行")

        batch_results = []

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(data_lines))
            batch_data_lines = data_lines[start_idx:end_idx]

            # 构建批次CSV内容（包含header）
            batch_csv_content = header + '\n' + '\n'.join(batch_data_lines)

            # 构建批次校验数据
            batch_validation_data = {
                "data_format": "CSV",
                "batch_info": {
                    "batch_index": batch_idx + 1,
                    "total_batches": total_batches,
                    "batch_size": len(batch_data_lines),
                    "data_range": f"第{start_idx + 1}行到第{end_idx}行"
                },
                "summary": summary_info,
                "csv_content": batch_csv_content
            }

            batch_validation_str = json.dumps(batch_validation_data, ensure_ascii=False, indent=2)

            print(f"处理批次 {batch_idx + 1}/{total_batches} (第{start_idx + 1}-{end_idx}行)...")

            # 保存批次调试信息
            batch_debug_file = f"debug/csv_batch_{batch_idx + 1}_input.json"
            try:
                with open(batch_debug_file, 'w', encoding='utf-8') as f:
                    f.write(batch_validation_str)
                print(f"批次调试数据已保存到: {batch_debug_file}")
            except Exception as e:
                print(f"保存批次调试数据失败: {e}")

            # 调用大模型处理批次
            batch_result = call_LLM(prompt, batch_validation_str, "", self.endpoint_url, self.model_name)

            if batch_result is None:
                print(f"批次 {batch_idx + 1} 处理失败")
                batch_results.append({
                    "batch_index": batch_idx + 1,
                    "error": "大模型调用失败",
                    "data_range": f"第{start_idx + 1}行到第{end_idx}行"
                })
            else:
                parsed_result = self._parse_validation_result(batch_result)
                batch_results.append({
                    "batch_index": batch_idx + 1,
                    "data_range": f"第{start_idx + 1}行到第{end_idx}行",
                    "validation_result": parsed_result if parsed_result else batch_result,
                    #"raw_result": batch_result
                })

        # 合并批次结果
        return self._merge_csv_batch_results(batch_results, summary_info)
    def _generate_csv_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成CSV数据的摘要信息

        Args:
            df: pandas DataFrame

        Returns:
            摘要信息
        """
        summary = {
            "total_records": len(df),
            "columns": list(df.columns),
            "data_movement_types": {},
            "cfp_distribution": {},
            "level1_modules": [],
            "level2_modules": [],
            "level3_modules": []
        }

        # 统计数据移动类型
        if '数据移动类型' in df.columns:
            movement_counts = df['数据移动类型'].value_counts().to_dict()
            summary["data_movement_types"] = movement_counts

        # 统计CFP分布
        if 'CFP' in df.columns:
            try:
                cfp_counts = df['CFP'].value_counts().to_dict()
                summary["cfp_distribution"] = cfp_counts
            except:
                pass

        # 统计模块信息
        if '一级功能模块' in df.columns:
            summary["level1_modules"] = df['一级功能模块'].dropna().unique().tolist()
        if '二级功能模块' in df.columns:
            summary["level2_modules"] = df['二级功能模块'].dropna().unique().tolist()
        if '三级功能模块' in df.columns:
            summary["level3_modules"] = df['三级功能模块'].dropna().unique().tolist()

        return summary

    def _merge_csv_batch_results(self, batch_results: List[Dict[str, Any]], summary_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并CSV批次处理结果

        Args:
            batch_results: 批次结果列表
            summary_info: 摘要信息

        Returns:
            合并后的结果
        """
        successful_batches = [r for r in batch_results if "error" not in r]
        failed_batches = [r for r in batch_results if "error" in r]

        merged_result = {
            "input_summary": summary_info,
            "batch_processing_info": {
                "total_batches": len(batch_results),
                "successful_batches": len(successful_batches),
                "failed_batches": len(failed_batches),
                "processing_method": "CSV分批次处理，每批次包含header"
            },
            "batch_results": batch_results,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if successful_batches:
            # 尝试合并校验结果
            merged_validation = self._merge_validation_results([r.get("validation_result") for r in successful_batches])
            if merged_validation:
                merged_result["merged_validation_result"] = merged_validation

                # 生成大模型汇总建议
                summary_suggestions = self._generate_summary_suggestions(merged_validation)
                if summary_suggestions:
                    merged_result["ai_generated_summary"] = summary_suggestions

        if failed_batches:
            merged_result["failed_batch_info"] = [
                {
                    "batch_index": r["batch_index"],
                    "data_range": r["data_range"],
                    "error": r["error"]
                } for r in failed_batches
            ]

        return merged_result

    def _merge_validation_results(self, validation_results: List[Any]) -> Dict[str, Any]:
        """
        合并多个校验结果

        Args:
            validation_results: 校验结果列表

        Returns:
            合并后的校验结果
        """
        if not validation_results:
            return {}

        # 过滤掉None值
        valid_results = [r for r in validation_results if r is not None]
        if not valid_results:
            return {}

        # 初始化统计数据
        severity_stats = {"高": 0, "中": 0, "低": 0}
        total_issues = 0
        total_records = 0
        all_findings = []
        all_recommendations = []

        # 统计各批次的数据
        for result in valid_results:
            if isinstance(result, dict):
                # 统计总记录数
                overall = result.get("overall_assessment", {})
                if overall and "total_records" in overall:
                    batch_records = overall.get("total_records", 0)
                    if batch_records > total_records:
                        total_records = batch_records  # 使用最大值，因为是同一数据集的不同批次

                # 统计问题按严重程度分类
                findings = result.get("detailed_findings", [])
                for finding in findings:
                    severity = finding.get("severity", "").strip()
                    if severity in severity_stats:
                        severity_stats[severity] += 1
                        total_issues += 1
                    all_findings.append(finding)

                # 收集建议
                recommendations = result.get("summary_recommendations", [])
                all_recommendations.extend(recommendations)

        # 计算占比 (按照总数量)
        severity_percentages = {}
        if total_records > 0:
            for severity, count in severity_stats.items():
                severity_percentages[severity] = round((count / total_records) * 100, 1)

        merged = {
            "batch_count": len(valid_results),
            "individual_results": valid_results,
            "summary": {
                "total_issues_found": total_issues,
                "total_records": total_records,
                "severity_statistics": {
                    "counts": severity_stats,
                    "percentages": severity_percentages
                },
                "all_findings": all_findings,
                "all_recommendations": list(set(all_recommendations)),  # 去重
                "overall_assessment": "基于分批次处理的综合评估"
            }
        }

        return merged

    def _generate_summary_suggestions(self, merged_validation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        使用大模型生成汇总建议

        Args:
            merged_validation: 合并后的校验结果

        Returns:
            大模型生成的汇总建议
        """
        try:
            # 构建汇总提示词
            summary_prompt = """你是一个COSMIC功能拆解专家。请基于以下分批次校验结果，生成综合性的汇总建议。

请分析：
1. 各严重程度问题的分布情况和主要特征
2. 跨批次的共同问题模式
3. 优先级建议（哪些问题应该优先解决）
4. 具体的改进措施和最佳实践

请以JSON格式返回结果，包含以下字段：
- priority_recommendations: 优先级建议列表
- common_patterns: 共同问题模式分析
- improvement_measures: 具体改进措施
- best_practices: 最佳实践建议
- severity_analysis: 各严重程度问题的特征分析

确保建议具体、可操作，并针对COSMIC方法的特点。"""

            # 构建输入数据
            summary_data = {
                "severity_statistics": merged_validation.get("summary", {}).get("severity_statistics", {}),
                "total_issues": merged_validation.get("summary", {}).get("total_issues_found", 0),
                "total_records": merged_validation.get("summary", {}).get("total_records", 0),
                "sample_findings": merged_validation.get("summary", {}).get("all_findings", [])[:20],  # 取前20个问题作为样本
                "existing_recommendations": merged_validation.get("summary", {}).get("all_recommendations", [])
            }

            summary_input = json.dumps(summary_data, ensure_ascii=False, indent=2)

            # 调用大模型
            result = call_LLM(summary_prompt, summary_input, "", self.endpoint_url, self.model_name)
            if result:
                parsed_result = self._parse_validation_result(result)
                if parsed_result:
                    return parsed_result
                else:
                    return {"raw_suggestion": result}

        except Exception as e:
            print(f"生成汇总建议失败: {e}")

        return None
    def _parse_validation_result(self, validation_result: str) -> Optional[Dict[str, Any]]:
        """
        尝试解析大模型返回的JSON格式校验结果

        Args:
            validation_result: 大模型返回的原始结果

        Returns:
            解析后的JSON对象，如果解析失败则返回None
        """
        try:
            # 尝试直接解析JSON
            return json.loads(validation_result)
        except json.JSONDecodeError:
            # 尝试提取JSON代码块
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', validation_result, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    pass

            # 如果都失败了，返回None
            return None

    def save_validation_result(self, result: Dict[str, Any], output_file: str = "debug/cosmic_validation_result.json"):
        """
        保存校验结果到文件

        Args:
            result: 校验结果
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")

    def generate_validation_report(self, result: Dict[str, Any]) -> str:
        """
        生成人类可读的校验报告

        Args:
            result: 校验结果

        Returns:
            格式化的校验报告
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("COSMIC功能拆解校验报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {result.get('timestamp', '未知')}")
        report_lines.append("")

        # 输入数据摘要
        input_summary = result.get('input_summary', {})
        report_lines.append("输入数据摘要:")
        report_lines.append(f"  总记录数: {input_summary.get('total_records', 0)}")
        report_lines.append(f"  一级模块数: {len(input_summary.get('level1_modules', []))}")
        report_lines.append(f"  二级模块数: {len(input_summary.get('level2_modules', []))}")
        report_lines.append(f"  三级模块数: {len(input_summary.get('level3_modules', []))}")

        # 数据移动类型分布
        data_movements = input_summary.get('data_movement_types', {})
        if data_movements:
            report_lines.append(f"  数据移动类型分布: {data_movements}")

        # CFP分布
        cfp_dist = input_summary.get('cfp_distribution', {})
        if cfp_dist:
            report_lines.append(f"  CFP分布: {cfp_dist}")

        report_lines.append("")

        # 功能过程摘要
        fp_summary = result.get('function_process_summary', {})
        if fp_summary:
            report_lines.append("功能过程摘要:")
            report_lines.append(f"  总功能过程数: {fp_summary.get('total_processes', 0)}")
            report_lines.append(f"  完整功能过程数: {fp_summary.get('complete_processes', 0)}")
            report_lines.append(f"  不完整功能过程数: {fp_summary.get('incomplete_processes', 0)}")

            total = fp_summary.get('total_processes', 0)
            complete = fp_summary.get('complete_processes', 0)
            if total > 0:
                completion_rate = (complete / total) * 100
                report_lines.append(f"  完整性比率: {completion_rate:.1f}%")
            report_lines.append("")

        # 批次处理信息
        batch_info = result.get('batch_processing_info', {})
        if batch_info:
            report_lines.append("批次处理信息:")
            report_lines.append(f"  总批次数: {batch_info.get('total_batches', 0)}")
            report_lines.append(f"  成功批次数: {batch_info.get('successful_batches', 0)}")
            report_lines.append(f"  失败批次数: {batch_info.get('failed_batches', 0)}")
            report_lines.append(f"  处理方法: {batch_info.get('processing_method', '未知')}")
            report_lines.append("")

        # 合并后的校验结果
        merged_validation = result.get('merged_validation_result', {})
        if merged_validation:
            summary = merged_validation.get('summary', {})

            # 严重程度统计
            severity_stats = summary.get('severity_statistics', {})
            if severity_stats:
                report_lines.append("问题严重程度统计:")
                counts = severity_stats.get('counts', {})
                percentages = severity_stats.get('percentages', {})
                total_issues = summary.get('total_issues_found', 0)

                report_lines.append(f"  总问题数: {total_issues}")
                for severity in ['高', '中', '低']:
                    count = counts.get(severity, 0)
                    percentage = percentages.get(severity, 0)
                    report_lines.append(f"  {severity}严重程度: {count}个 ({percentage}%)")
                report_lines.append("")

        # 校验结果
        validation_result = result.get('validation_result', {})
        if isinstance(validation_result, dict):
            # 结构化的校验结果
            overall = validation_result.get('overall_assessment', {})
            if overall:
                report_lines.append("整体评估:")
                report_lines.append(f"  合规率: {overall.get('compliance_rate', '未知')}")
                report_lines.append(f"  重大问题数: {overall.get('major_issues_count', 0)}")
                report_lines.append(f"  轻微问题数: {overall.get('minor_issues_count', 0)}")
                report_lines.append("")

            # 详细发现
            findings = validation_result.get('detailed_findings', [])
            if findings:
                report_lines.append("详细问题发现:")
                for i, finding in enumerate(findings[:10], 1):  # 只显示前10个问题
                    report_lines.append(f"  {i}. {finding.get('module_path', '未知路径')}")
                    report_lines.append(f"     功能过程: {finding.get('function_process', '未知')}")
                    report_lines.append(f"     问题类型: {finding.get('issue_type', '未知')}")
                    report_lines.append(f"     严重程度: {finding.get('severity', '未知')}")
                    report_lines.append(f"     问题描述: {finding.get('issue_description', '无描述')}")
                    report_lines.append(f"     修改建议: {finding.get('suggested_fix', '无建议')}")
                    report_lines.append("")

                if len(findings) > 10:
                    report_lines.append(f"  ... 还有 {len(findings) - 10} 个问题，详见完整报告")
                    report_lines.append("")

            # 总体建议
            recommendations = validation_result.get('summary_recommendations', [])
            if recommendations:
                report_lines.append("总体建议:")
                for rec in recommendations:
                    report_lines.append(f"  • {rec}")
                report_lines.append("")
        else:
            # 非结构化的校验结果
            report_lines.append("校验结果:")
            report_lines.append(str(validation_result))
            report_lines.append("")

        # AI生成的汇总建议
        ai_summary = result.get('ai_generated_summary', {})
        if ai_summary:
            report_lines.append("AI生成的汇总建议:")
            report_lines.append("-" * 60)

            # 优先级建议
            priority_recs = ai_summary.get('priority_recommendations', [])
            if priority_recs:
                report_lines.append("优先级建议:")
                for i, rec in enumerate(priority_recs, 1):
                    report_lines.append(f"  {i}. {rec}")
                report_lines.append("")

            # 共同问题模式
            common_patterns = ai_summary.get('common_patterns', [])
            if common_patterns:
                report_lines.append("共同问题模式:")
                for pattern in common_patterns:
                    report_lines.append(f"  • {pattern}")
                report_lines.append("")

            # 改进措施
            improvements = ai_summary.get('improvement_measures', [])
            if improvements:
                report_lines.append("具体改进措施:")
                for measure in improvements:
                    report_lines.append(f"  • {measure}")
                report_lines.append("")

            # 最佳实践
            best_practices = ai_summary.get('best_practices', [])
            if best_practices:
                report_lines.append("最佳实践建议:")
                for practice in best_practices:
                    report_lines.append(f"  • {practice}")
                report_lines.append("")

            # 严重程度分析
            severity_analysis = ai_summary.get('severity_analysis', {})
            if severity_analysis:
                report_lines.append("严重程度分析:")
                for severity, analysis in severity_analysis.items():
                    report_lines.append(f"  {severity}严重程度问题特征: {analysis}")
                report_lines.append("")

        report_lines.append("=" * 80)

        return "\n".join(report_lines)

    def save_validation_report(self, result: Dict[str, Any], report_file: str = "debug/cosmic_validation_report.txt"):
        """
        保存人类可读的校验报告

        Args:
            result: 校验结果
            report_file: 报告文件路径
        """
        try:
            report = self.generate_validation_report(result)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"校验报告已保存到: {report_file}")
            return report
        except Exception as e:
            print(f"保存校验报告失败: {e}")
        return ""


def main():
    """主函数，用于测试"""
    import config
    import sys

    print("初始化COSMIC校验器...")
    validator = CosmicValidator(config)

    # 支持命令行参数指定文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认测试文件，优先使用CSV格式
        if os.path.exists("output.xlsx"):
            file_path = "output.xlsx"
        elif os.path.exists("output.csv"):
            file_path = "output.csv"
        else:
            print("错误：未找到测试文件，请指定文件路径")
            return

    print(f"开始校验cosmic数据文件: {file_path}")
    result = validator.validate_cosmic_data(file_path)

    if "error" in result:
        print(f"校验失败: {result['error']}")
        return

    print("校验完成！")

    # 显示摘要信息
    input_summary = result.get('input_summary', {})
    print(f"\n输入数据统计:")
    print(f"  总记录数: {input_summary.get('total_records', 0)}")

    # 显示模块信息
    level1_modules = input_summary.get('level1_modules', [])
    level2_modules = input_summary.get('level2_modules', [])
    level3_modules = input_summary.get('level3_modules', [])

    if level1_modules:
        print(f"  模块数量: L1={len(level1_modules)}, L2={len(level2_modules)}, L3={len(level3_modules)}")

    # 显示数据移动类型分布
    data_movements = input_summary.get('data_movement_types', {})
    if data_movements:
        print(f"  数据移动类型分布: {data_movements}")

    # 显示功能过程摘要（仅JSON格式）
    fp_summary = result.get('function_process_summary', {})
    if fp_summary:
        print(f"  功能过程: 总数={fp_summary.get('total_processes', 0)}, "
              f"完整={fp_summary.get('complete_processes', 0)}, "
              f"不完整={fp_summary.get('incomplete_processes', 0)}")

    # 保存结果和报告
    print("\n保存校验结果...")
    validator.save_validation_result(result)
    report = validator.save_validation_report(result)

    # 显示校验结果预览
    print(f"\n校验结果预览:{report[:300]}")
    
    print("请查看debug目录下的详细结果文件。")


if __name__ == "__main__":
    main()
